# EZContext-N Backend API Enhancement Plan

## Project Overview
Transform the current Cloudflare Workers vector database API into a comprehensive multi-tenant SaaS backend platform with team management, RBAC, billing integration, and enhanced database architecture. This backend will provide robust APIs for a separate frontend project to consume.

## Current State Analysis
- ✅ Cloudflare Workers + Hono backend with TypeScript
- ✅ Drizzle ORM with PostgreSQL (Neon)
- ✅ Better Auth integration
- ✅ Vector database (Vectorize) with embedding services
- ✅ Basic user management and API keys
- ✅ Path aliases configured in tsconfig.json
- ✅ Production-ready middleware and validation

## Sprint-Based Implementation Plan

### Sprint 1: Database Schema & Team Management (Week 1)
**Goal**: Establish multi-tenant architecture with teams, projects, and RBAC foundation

#### Tasks:
1. **Database Schema Enhancement**
   - Add teams table with soft delete support
   - Add team_members table with role-based permissions
   - Add billing_subscriptions and billing_transactions tables
   - Update existing tables to support multi-tenancy
   - Add soft delete columns (deleted_at) to relevant tables

2. **Team Management System**
   - Create team CRUD operations with proper validation
   - Implement team member invitation system
   - Add default roles (Owner, Admin, Manager, Member, Viewer)
   - Create permission system with granular controls

3. **Database Commands & Migrations**
   - Add Drizzle migration scripts
   - Create seed scripts for default roles and permissions
   - Add database backup/restore commands

### Sprint 2: RBAC & API Key Management (Week 2)
**Goal**: Implement comprehensive role-based access control and enhanced API key management

#### Tasks:
1. **RBAC Implementation**
   - Define permission constants and types
   - Create middleware for permission checking
   - Implement resource-level access control
   - Add audit logging for all actions

2. **Enhanced API Key Management**
   - Add permission-based API key scoping
   - Implement API key rate limiting per permission level
   - Add API key usage analytics
   - Create API key management endpoints

3. **Project Management Enhancement**
   - Update project CRUD to support team ownership
   - Add project-level permissions
   - Implement project sharing and collaboration

### Sprint 3: Billing Integration (Week 3)
**Goal**: Integrate Paddle and PayPal for subscription and usage-based billing

#### Tasks:
1. **Billing System Architecture**
   - Design subscription plans and pricing tiers
   - Create billing service abstraction layer
   - Add usage tracking and metering

2. **Paddle Integration**
   - Implement Paddle webhook handlers
   - Add subscription management endpoints
   - Create billing dashboard data endpoints

3. **PayPal Integration**
   - Add PayPal payment processing
   - Implement one-time payment handling
   - Add payment method management

### Sprint 4: Email & Vector Database Refactoring (Week 4)
**Goal**: Implement Resend email service and refactor vector operations to use database-first approach

#### Tasks:
1. **Email Service Integration**
   - Integrate Resend for transactional emails
   - Create email templates for invitations, billing, etc.
   - Add email verification and password reset flows

2. **Vector Database Refactoring**
   - Remove KV dependency for vector operations
   - Implement database-first vector workflow:
     - Frontend → Database → Background Vector Update
     - Query → Vector Search → Database Enrichment
     - CRUD → Database → Background Vector Sync

3. **Drizzle-Zod Validation**
   - Add drizzle-zod for DB schema validation
   - Create comprehensive validation schemas
   - Add input sanitization and security validation



## Technical Specifications

### Database Schema Additions
```sql
-- Teams table
teams (id, name, slug, description, owner_id, plan_type, created_at, updated_at, deleted_at)

-- Team members with RBAC
team_members (id, team_id, user_id, role, permissions, invited_by, joined_at, created_at, updated_at, deleted_at)

-- Billing tables
billing_subscriptions (id, team_id, provider, plan_id, status, current_period_start, current_period_end, created_at, updated_at)
billing_transactions (id, team_id, provider, transaction_id, amount, currency, status, created_at)

-- Enhanced API keys
api_keys (add: team_id, scoped_permissions, usage_limit, usage_count)
```

### Permission System
```typescript
// Permission categories
- read.team, manage.team
- read.project, manage.project  
- read.billing, manage.billing
- read.vectors, manage.vectors
- read.analytics, manage.analytics

// Default roles
- Owner: All permissions
- Admin: All except billing management
- Manager: Project and team read/write
- Member: Project read/write, team read
- Viewer: Read-only access
```

### Technology Stack
- **Backend**: Cloudflare Workers + Hono + TypeScript
- **Database**: PostgreSQL (Neon) + Drizzle ORM
- **Auth**: Better Auth
- **Validation**: Drizzle-Zod
- **Email**: Resend
- **Billing**: Paddle + PayPal
- **Vector**: Cloudflare Vectorize
- **Storage**: Cloudflare R2

### Path Aliases (Already Configured)
```json
{
  "@/*": ["src/*"],
  "@api/*": ["src/api/*"],
  "@services": ["src/services"],
  "@routes": ["src/routes"],
  "@middleware/*": ["src/middleware/*"],
  "@types": ["src/types"],
  "@utils": ["src/utils"],
  "@dbSchema": ["src/dbSchema"]
}
```

## Success Criteria
- [ ] Multi-tenant team management with RBAC
- [ ] Integrated billing with Paddle and PayPal
- [ ] Database-first vector operations (no KV dependency)
- [ ] Comprehensive API key management with permissions
- [ ] Email notifications via Resend
- [ ] Soft delete support across all entities
- [ ] Production-ready security and validation
- [ ] Zero TypeScript errors and 100% test coverage
- [ ] Comprehensive backend APIs ready for frontend consumption

## Next Steps
1. Begin Sprint 1 with database schema enhancements
2. Configure billing provider accounts (Paddle/PayPal)
3. Set up Resend account for email services
4. Implement comprehensive API documentation for frontend integration

---
*This plan follows the user's preferences for sprint-based development, backend-only approach, and production-ready architecture.*
