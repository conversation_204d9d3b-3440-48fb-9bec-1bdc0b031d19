# Security & Performance Improvement Plan

## Overview
This document outlines a comprehensive improvement plan based on the security audit findings. All tasks are prioritized and include specific implementation requirements, acceptance criteria, and time estimates.

## Priority Levels
- **Critical**: Security vulnerabilities that must be fixed immediately
- **High**: Important security/performance issues that should be addressed within 1 week
- **Medium**: Improvements that should be completed within 1 month
- **Low**: Nice-to-have improvements for future iterations

---

## 🚨 CRITICAL PRIORITY TASKS

### 1. API Key Authentication System
**Priority**: Critical  
**Dependencies**: None

**Files to Create/Modify**:
- `src/middleware/api-key-middleware.ts` (new)
- `src/middleware/index.ts` (export new middleware)
- `src/types/auth.ts` (add API key types)
- `src/services/apiKeyService.ts` (add hash validation function)

**Task Details**:
- Create `requireApiKey` middleware that validates `Authorization: Bearer <token>` headers
- Implement API key hashing and validation using existing `getApiKeyByHash()` function
- Add API key context to Hono environment types
- Support both session-based and API key authentication on the same routes

**Acceptance Criteria**:
- [ ] API key middleware validates Bearer tokens from Authorization header
- [ ] Invalid API keys return 401 with proper error message
- [ ] Valid API keys set user context from associated userId
- [ ] Middleware integrates with existing session authentication
- [ ] API key usage is tracked and incremented on successful requests
- [ ] Expired and usage-limit-exceeded API keys are rejected

### 2. Route Protection Implementation
**Priority**: Critical  
**Estimated Time**: 4-6 hours  
**Dependencies**: Task 1 (API Key Authentication)

**Files to Modify**:
- `src/routes/document-routes.ts`
- `src/routes/search-routes.ts`
- `src/routes/upload-routes.ts`
- `src/index.ts` (global middleware application)

**Task Details**:
- Apply `authMiddleware`, `requireAuth`, and `requireApiKey` to unprotected routes
- Implement dual authentication: accept either valid session OR valid API key
- Ensure proper error responses for unauthenticated requests
- Maintain backward compatibility with existing authenticated routes

**Acceptance Criteria**:
- [ ] `/api/documents/*` requires authentication (session OR API key)
- [ ] `/api/search/*` requires authentication (session OR API key)
- [ ] `/api/upload/*` requires authentication (session OR API key)
- [ ] Health check (`/`) remains unprotected
- [ ] OpenAPI docs (`/openapi.*`, `/docs`) remain unprotected
- [ ] Auth routes (`/api/auth/*`) remain unprotected
- [ ] All other routes require authentication
- [ ] Proper 401 responses for unauthenticated requests

### 3. API Key Permission System
**Priority**: Critical  
**Dependencies**: Task 1 (API Key Authentication)

**Files to Create/Modify**:
- `src/middleware/api-key-permissions.ts` (new)
- `src/types/permissions.ts` (new)
- `src/services/apiKeyService.ts` (add permission validation)
- All route files (add permission checks)

**Task Details**:
- Define granular permission system (read, write, admin, search, upload, etc.)
- Create permission validation middleware
- Apply permission checks to all protected routes
- Implement permission inheritance and scoping

**Acceptance Criteria**:
- [ ] API keys have configurable permissions stored in database
- [ ] Permission middleware validates API key permissions for each route
- [ ] Read-only API keys cannot access write endpoints
- [ ] Admin API keys have full access
- [ ] Scoped permissions work correctly (e.g., project-specific access)
- [ ] Permission violations return 403 with clear error messages
- [ ] Permission system is documented with examples

---

## 🔥 HIGH PRIORITY TASKS

### 4. Comprehensive Rate Limiting Implementation
**Priority**: High  
**Dependencies**: Task 2 (Route Protection)

**Files to Modify**:
- `src/middleware/billing-middleware.ts`
- `src/index.ts` (global middleware)
- All protected route files

**Task Details**:
- Apply `enforceApiRequestLimits()` to all protected routes
- Implement team context middleware consistently
- Add proper usage tracking for billing
- Ensure rate limiting works with both session and API key authentication

**Acceptance Criteria**:
- [ ] All protected routes track API usage
- [ ] Plan limits are enforced consistently
- [ ] Rate limiting works with API key authentication
- [ ] Usage tracking includes API key identification
- [ ] Proper 402 responses when limits exceeded
- [ ] Additional request purchases are supported ($5 per 1,000 requests)

### 5. Team Context Middleware Consistency ✅ **COMPLETED**
**Priority**: High
**Dependencies**: Task 2 (Route Protection)

**Files Modified**:
- `src/routes/document-routes.ts` - Updated to use IRBACHonoEnv and teamContextMiddleware
- `src/routes/search-routes.ts` - Updated to use IRBACHonoEnv and teamContextMiddleware
- `src/routes/upload-routes.ts` - Updated to use IRBACHonoEnv and teamContextMiddleware
- `src/middleware/rbac-middleware.ts` - Enhanced to preserve existing team context from API keys

**Implementation Details**:
- Applied `teamContextMiddleware` to document, search, and upload routes
- Modified `teamContextMiddleware` to preserve team context set by API key middleware
- Ensured billing middleware has access to team information for all protected routes
- Fixed team context inheritance for API key authentication

**Acceptance Criteria**:
- [x] Team context is available on all routes that need it
- [x] API keys inherit team context from associated user
- [x] Billing enforcement works correctly with team context
- [x] RBAC permissions work with API key authentication
- [x] Team-scoped API keys respect team boundaries

### 6. Vector Database Query Optimization ✅ **COMPLETED**
**Priority**: High
**Dependencies**: None

**Files Modified**:
- `src/services/vector-service.ts` - Replaced dummy embedding queries with KV-based metadata operations
- `src/services/search-service.ts` - Optimized collection info and stats queries to use KV storage
- `src/tests/integration.test.ts` - Added KVVectorService mocks and KV list method support

**Implementation Details**:
- Replaced all dummy embedding queries (`Array(1024).fill(0)`) with efficient KV-based metadata operations
- Implemented true pagination at the data level using `listVectorIdsWithFilters()` method
- Optimized `listVectors()`, `bulkDelete()`, `getCollectionInfo()`, `getAvailableCollections()`, and `getSearchStats()` methods
- Added `convertKVMetadataToIMetadata()` helper for metadata format conversion
- Enhanced test mocks to support new KV-based operations

**Performance Improvements**:
- Eliminated unnecessary vector embedding generation for metadata-only operations
- Reduced memory usage by implementing true pagination instead of post-query slicing
- Improved query performance by leveraging KV storage for filtering operations
- Maintained existing caching functionality for search results

**Acceptance Criteria**:
- [x] No more dummy embedding queries for metadata operations
- [x] Pagination is handled at the data level instead of post-query slicing
- [x] Query results are cached appropriately (existing functionality preserved)
- [x] Vector operations are optimized for performance with KV-based metadata queries
- [ ] Memory usage is reduced for large result sets
- [ ] Query performance is measurably improved

---

## 📊 MEDIUM PRIORITY TASKS

### 7. Frontend Integration Support
**Priority**: Medium  
**Dependencies**: Tasks 1-3 (Authentication system)

**Files to Modify**:
- `src/index.ts` (CORS configuration)
- `src/middleware/auth-middleware.ts`
- `src/types/responses.ts`
- Documentation files

**Task Details**:
- Ensure CORS supports Next.js frontend domains
- Standardize error response formats
- Document API endpoints for frontend consumption
- Test authentication compatibility

**Acceptance Criteria**:
- [ ] CORS allows Next.js frontend domains
- [ ] Cookie-based authentication works for frontend
- [ ] API key authentication works for API clients
- [ ] Error responses are consistent and frontend-friendly
- [ ] API documentation includes frontend integration examples
- [ ] Authentication flow is tested end-to-end

### 8. Database Query Optimization
**Priority**: Medium  
**Dependencies**: None

**Files to Modify**:
- `src/dbSchema.ts` (add indexes)
- `src/services/*.ts` (optimize queries)
- `src/utils/getDB.ts` (connection optimization)

**Task Details**:
- Add database indexes on frequently queried fields
- Optimize Drizzle ORM queries
- Implement query result caching
- Address potential N+1 query issues

**Acceptance Criteria**:
- [ ] Database indexes are added for performance
- [ ] Query performance is measurably improved
- [ ] N+1 query issues are resolved
- [ ] Connection pooling is optimized
- [ ] Query result caching reduces database load

### 9. Security Monitoring & Logging
**Priority**: Medium  
**Dependencies**: Tasks 1-2 (Authentication system)

**Files to Create/Modify**:
- `src/middleware/security-logger.ts` (new)
- `src/middleware/error-handler.ts`
- `src/services/auditService.ts`

**Task Details**:
- Log failed authentication attempts
- Track suspicious activity patterns
- Implement security event auditing
- Add rate limiting violation logging

**Acceptance Criteria**:
- [ ] Failed authentication attempts are logged
- [ ] API key usage is audited
- [ ] Rate limiting violations are tracked
- [ ] Security events include relevant context
- [ ] Logs are structured for analysis
- [ ] Sensitive data is not logged

---

## 🏗️ MULTI-TENANT SAAS ENHANCEMENT TASKS

### 12. Database Schema Enhancement for Multi-Tenancy ✅ **COMPLETED**
**Priority**: High
**Dependencies**: Tasks 1-3 (Authentication system)
**Sprint**: 1 (Week 1)

**Files Modified**:
- `src/dbSchema.ts` - Enhanced with comprehensive multi-tenant schema
- `migrations/0001_brief_pepper_potts.sql` - Migration for multi-tenant tables
- `src/types/team.ts` - Comprehensive team management types
- `src/types/billing.ts` - Billing and subscription types

**Implementation Details**:
- Added comprehensive teams table with soft delete support and proper constraints
- Implemented team_members table with full RBAC support and role hierarchy
- Created billing_subscriptions and billing_transactions tables supporting Paddle and PayPal
- Enhanced existing tables (projects, collections, documents, api_keys, audit_logs) for multi-tenancy
- Implemented soft delete functionality across all relevant tables
- Added proper foreign key relationships and cascade behaviors
- Created comprehensive type definitions for all team and billing operations

**Database Schema Enhancements**:
- Teams table with owner relationships and plan management
- Team members with granular permissions and invitation tracking
- Billing subscriptions with provider support and status management
- Additional API requests tracking for plan-based billing
- Audit logs with team context for compliance
- Multi-tenant support in projects, collections, and documents

**Acceptance Criteria**:
- [x] Teams table created with proper schema and constraints
- [x] Team members table supports full RBAC with role hierarchy
- [x] Billing tables support both Paddle and PayPal providers
- [x] All existing tables updated for multi-tenancy with optional team associations
- [x] Soft delete functionality implemented across all relevant tables
- [x] Migration scripts created and successfully applied
- [x] Database relationships and constraints properly configured

### 13. Team Management System Implementation ✅ **COMPLETED**
**Priority**: High
**Dependencies**: Task 12 (Database Schema)
**Sprint**: 1 (Week 1)

**Files Implemented**:
- `src/services/teamService.ts` - Comprehensive team management service
- `src/routes/teamsApp.ts` - Full REST API for team operations
- `src/types/teams.ts` - Complete type definitions with RBAC
- `src/middleware/rbac-middleware.ts` - Team context and permission middleware

**Implementation Details**:
- Created comprehensive team CRUD operations with proper validation and error handling
- Implemented team member management with invitation system and role assignment
- Added complete role hierarchy (Owner, Admin, Manager, Member, Viewer) with granular permissions
- Created permission system with 12 distinct permissions across 6 categories
- Implemented team context middleware for multi-tenant operations
- Added audit logging for all team operations
- Created plan-based limits and feature management

**API Endpoints Implemented**:
- `POST /api/teams` - Create new team
- `GET /api/teams/:teamId` - Get team details with member count and plan info
- `PUT /api/teams/:teamId` - Update team information
- `DELETE /api/teams/:teamId` - Soft delete team (owner only)
- `GET /api/teams/:teamId/members` - List team members with permissions
- `POST /api/teams/:teamId/members` - Add team member with role assignment

**Permission System**:
- 6 permission categories: team, project, billing, vectors, analytics, api_keys
- 2 permission levels: read, manage
- Role-based default permissions with custom permission overrides
- Hierarchical role system with proper inheritance

**Acceptance Criteria**:
- [x] Team CRUD operations implemented with comprehensive validation
- [x] Team member invitation system with email and user ID support
- [x] Default roles created and assigned with proper hierarchy
- [x] Permission system with granular controls across 6 categories
- [x] Team validation using Zod with proper error handling
- [x] Comprehensive error handling and audit logging

### 14. Enhanced RBAC Implementation ✅ **COMPLETED**
**Priority**: High
**Dependencies**: Task 13 (Team Management)
**Sprint**: 2 (Week 2)

**Files Created/Modified**:
- `src/utils/permission-constants.ts` (new) - 850+ lines of permission constants and utilities
- `src/types/permissions.ts` (new) - 300+ lines of comprehensive permission types
- `src/services/permissionService.ts` (new) - 400+ lines of permission service implementation
- `src/middleware/rbac-middleware.ts` (enhanced) - Added new permission middleware functions
- `src/routes/*.ts` (all routes) - Updated with resource-level permission checks
- `src/services/auditService.ts` (enhanced) - Permission-specific audit logging
- `src/types/teams.ts` (updated) - Expanded permission types
- `src/utils/permissions.ts` (updated) - Uses new permission constants

**Implementation Details**:
- ✅ Created comprehensive permission system with 25 granular permissions across 6 categories
- ✅ Enhanced RBAC middleware with `enhancedPermissionCheck`, `resourcePermissionCheck`, `adminOnlyPermissionCheck`
- ✅ Implemented resource-level access control tied to specific resources with context
- ✅ Added comprehensive audit logging for all permission checks with detailed context
- ✅ Configured default role permissions with validation and recommendation system
- ✅ Implemented permission inheritance with comprehensive role hierarchy validation

**Key Features**:
- **Granular Permissions**: team, project, billing, vectors, analytics, api_keys categories
- **Resource-Level Access**: Permission checks tied to specific resources with context
- **Permission Inheritance**: Comprehensive role hierarchy with inheritance validation
- **Enhanced Audit Logging**: Detailed logging of all permission checks
- **Permission Validation**: Comprehensive validation and recommendation system

**Acceptance Criteria**:
- [x] Permission constants defined (25 permissions across 6 categories)
- [x] Permission checking middleware implemented (3 new middleware functions)
- [x] Resource-level access control working (all routes updated)
- [x] Audit logging for all actions (comprehensive logging system)
- [x] Default role permissions configured (role hierarchy with validation)
- [x] Permission inheritance working (inheritance chain resolution)

### 15. Enhanced API Key Management with Permissions ✅ **COMPLETED**
**Priority**: High
**Dependencies**: Task 14 (RBAC Implementation)
**Sprint**: 2 (Week 2)

**Files Implemented/Modified**:
- `src/services/apiKeyService.ts` - Enhanced with comprehensive API key management features
- `src/routes/apiKeysApp.ts` - Complete REST API for API key operations
- `src/types/auth.ts` - Extended with API key types and interfaces
- `src/middleware/api-key-middleware.ts` - Enhanced with permission validation and team context
- `src/services/apiKeyAnalyticsService.ts` - New service for usage analytics and tracking
- `src/services/rotationService.ts` - New service for API key rotation management
- `src/services/notificationService.ts` - Enhanced for API key notifications
- `src/tests/api-key-*.test.ts` - Comprehensive test suite for all API key functionality

**Implementation Details**:
- ✅ **Task 15.1: Permission-Based API Key Scoping** - API keys now support granular permission assignment with team-level scoping
- ✅ **Task 15.2: Rate Limiting per Permission Level** - Implemented plan-based rate limiting with API key usage tracking
- ✅ **Task 15.3: Usage Analytics and Tracking** - Complete analytics service with detailed usage metrics and reporting
- ✅ **Task 15.4: Comprehensive API Key Management Endpoints** - Full CRUD operations with advanced filtering and bulk operations
- ✅ **Task 15.5: Team-Scoped API Keys** - API keys inherit team context and respect team boundaries
- ✅ **Task 15.6: API Key Expiration and Rotation** - Advanced expiration management with automated rotation scheduling
- ✅ **Task 15.7: Integration Testing and Documentation** - Comprehensive test coverage and production-ready documentation

**Key Features Implemented**:
- **Advanced Expiration Management**: Automatic detection, grace periods, and notification system
- **Enhanced Rotation System**: Manual and automated rotation with overlap periods and history tracking
- **Comprehensive Analytics**: Usage tracking, performance metrics, and detailed reporting
- **Bulk Operations**: Efficient handling of multiple API keys with transaction support
- **Security Features**: Permission validation, rate limiting, and comprehensive audit trails
- **Notification System**: Email and webhook notifications for key lifecycle events

**API Endpoints Implemented**:
- `GET /api/api-keys` - List API keys with advanced filtering and pagination
- `POST /api/api-keys` - Create new API key with permission assignment
- `GET /api/api-keys/:keyId` - Get detailed API key information
- `PUT /api/api-keys/:keyId` - Update API key properties and permissions
- `DELETE /api/api-keys/:keyId` - Soft delete API key with audit trail
- `POST /api/api-keys/:keyId/rotate` - Standard API key rotation
- `POST /api/api-keys/:keyId/rotate-enhanced` - Enhanced rotation with overlap support
- `GET /api/api-keys/expiration/status` - Get expiration status across all keys
- `POST /api/api-keys/expiration/extend` - Bulk extend API key expiration
- `GET /api/api-keys/analytics` - Comprehensive usage analytics and metrics
- `POST /api/api-keys/bulk` - Bulk operations for multiple API keys

**Acceptance Criteria**:
- [x] API keys support permission scoping with granular team-level controls
- [x] Rate limiting per permission level with plan-based enforcement
- [x] Usage analytics and tracking with comprehensive reporting
- [x] API key management endpoints with full CRUD operations
- [x] Team-scoped API keys with proper context inheritance
- [x] API key expiration and rotation with advanced scheduling and overlap support
- [x] Zero-error Biome linting standards achieved
- [x] Comprehensive test coverage for all API key functionality
- [x] Production-ready documentation and best practices guide

### 16. Billing Integration Architecture
**Priority**: Medium
**Dependencies**: Task 12 (Database Schema)
**Sprint**: 3 (Week 3)

**Files to Create/Modify**:
- `src/services/billingService.ts` (new)
- `src/services/paddleService.ts` (new)
- `src/services/paypalService.ts` (new)
- `src/routes/billing-routes.ts` (new)
- `src/types/billing.ts` (extend)

**Task Details**:
- Design subscription plans and pricing tiers
- Create billing service abstraction layer
- Add usage tracking and metering
- Implement Paddle and PayPal integrations

**Acceptance Criteria**:
- [ ] Subscription plans and pricing defined
- [ ] Billing service abstraction layer
- [ ] Usage tracking and metering
- [ ] Paddle webhook handlers
- [ ] PayPal payment processing
- [ ] Billing dashboard data endpoints

### 17. Email Service Integration
**Priority**: Medium
**Dependencies**: Task 13 (Team Management)
**Sprint**: 4 (Week 4)

**Files to Create/Modify**:
- `src/services/emailService.ts` (new)
- `src/templates/` (new directory)
- `src/routes/email-routes.ts` (new)
- `src/types/email.ts` (new)

**Task Details**:
- Integrate Resend for transactional emails
- Create email templates for invitations, billing, etc.
- Add email verification and password reset flows
- Implement email queue and retry logic

**Acceptance Criteria**:
- [ ] Resend integration working
- [ ] Email templates created
- [ ] Email verification flow
- [ ] Password reset flow
- [ ] Email queue and retry logic
- [ ] Email analytics and tracking

### 18. Vector Database Refactoring to Database-First
**Priority**: Medium
**Dependencies**: Task 12 (Database Schema)
**Sprint**: 4 (Week 4)

**Files to Modify**:
- `src/services/vector-service.ts` (major refactor)
- `src/services/search-service.ts` (refactor)
- `src/routes/document-routes.ts` (update workflow)
- `src/utils/vector-sync.ts` (new)

**Task Details**:
- Remove KV dependency for vector operations
- Implement database-first vector workflow
- Add background vector update system
- Create vector sync utilities

**Acceptance Criteria**:
- [ ] KV dependency removed
- [ ] Database-first workflow implemented
- [ ] Background vector updates working
- [ ] Vector sync utilities created
- [ ] Query performance maintained
- [ ] Data consistency ensured

---

## 🔧 LOW PRIORITY TASKS

### 10. Memory Usage Optimization
**Priority**: Low  
**Dependencies**: Task 6 (Vector optimization)

**Files to Modify**:
- `src/services/upload-service.ts`
- `src/mcp/websocket-handler.ts`
- Vector processing services

**Task Details**:
- Implement streaming for large file uploads
- Optimize vector embedding memory usage
- Fix potential WebSocket memory leaks

**Acceptance Criteria**:
- [ ] Large files are processed with streaming
- [ ] Vector embeddings use memory efficiently
- [ ] WebSocket connections don't leak memory
- [ ] Memory usage is within Cloudflare Workers limits

### 11. Comprehensive Testing Suite
**Priority**: Low  
**Dependencies**: All above tasks

**Files to Create**:
- `src/tests/security/*.test.ts`
- `src/tests/performance/*.test.ts`
- `src/tests/integration/*.test.ts`

**Task Details**:
- Create security-focused tests
- Add performance benchmarks
- Implement integration tests
- Test authentication flows

**Acceptance Criteria**:
- [ ] Security vulnerabilities are covered by tests
- [ ] Performance regressions are caught by tests
- [ ] Authentication flows are thoroughly tested
- [ ] API key permissions are tested
- [ ] Rate limiting is tested
- [ ] All tests pass consistently

---

## 📋 IMPLEMENTATION PHASES

### Phase 1: Critical Security (Immediate)
- Tasks 1-3: API Key system and route protection
- Estimated total time: 22-32 hours
- **Must complete before proceeding to other phases**

### Phase 2: Performance & Consistency (Week 1)
- Tasks 4-6: Rate limiting, team context, vector optimization
- Tasks 12-13: Database schema and team management
- Estimated total time: 28-36 hours

### Phase 3: RBAC & Enhanced API Management (Week 2)
- Tasks 14-15: Enhanced RBAC and API key management
- Estimated total time: 20-28 hours

### Phase 4: Integration & Monitoring (Week 3)
- Tasks 7-9: Frontend support, database optimization, security logging
- Task 16: Billing integration architecture
- Estimated total time: 28-36 hours

### Phase 5: Email & Vector Refactoring (Week 4)
- Tasks 17-18: Email service and vector database refactoring
- Estimated total time: 24-32 hours

### Phase 6: Polish & Testing (Week 5)
- Tasks 10-11: Memory optimization, comprehensive testing
- Final integration testing and documentation
- Estimated total time: 20-28 hours

## 🚀 DEPLOYMENT STRATEGY

### Pre-deployment Checklist
- [ ] All critical tasks completed and tested
- [ ] Database migrations prepared (if needed)
- [ ] Environment variables documented
- [ ] Rollback procedures documented
- [ ] Performance benchmarks established

### Rollback Procedures
- Maintain feature flags for new authentication system
- Keep backup of current middleware configuration
- Document quick rollback steps for each major change
- Test rollback procedures in staging environment

### Success Metrics

#### Security & Authentication
- Zero authentication bypass vulnerabilities
- API key system working with proper permissions
- All protected routes properly secured
- RBAC system functioning correctly

#### Multi-Tenant SaaS Features
- Multi-tenant team management with RBAC
- Database-first vector operations (no KV dependency)
- Comprehensive API key management with permissions
- Integrated billing with Paddle and PayPal
- Email notifications via Resend
- Soft delete support across all entities

#### Performance & Integration
- Performance improvements measurable
- Frontend integration working seamlessly
- Vector database operations optimized
- Database queries optimized with proper indexing

#### Quality Standards
- Zero TypeScript errors and 100% test coverage
- Production-ready security and validation
- Comprehensive backend APIs ready for frontend consumption
