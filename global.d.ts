import type { RequestIdVariables } from 'hono/request-id';

declare global {
  interface Env {
    Variables: {
      message: string;
      requestId: string;
    } & RequestIdVariables;
    Bindings: {
      // Cloudflare bindings
      ai: Ai;
      vdb: VectorizeIndex;
      kv: KVNamespace;
      r2: R2Bucket;
      browser: Fetcher;

      // Environment variables with proper typing
      EMBEDDING_PROVIDER: 'openai' | 'voyageai' | 'cloudflare';
      EMBEDDING_MODEL: string;
      EMBEDDING_DIMENSIONS: string;
      VECTOR_DIMENSIONS: string;
      OPENAI_BASE_URL?: string;
      OPENAI_API_KEY?: string;
      OPENROUTER_API_KEY?: string;
      VOYAGEAI_API_KEY?: string;
      MCP_MAX_CONNECTIONS?: string;
      MCP_CACHE_TTL?: string;
      ALLOWED_FILE_EXTENSIONS?: string;

      // Database configuration
      DATABASE_URL: string;

      // Better Auth configuration
      BETTER_AUTH_SECRET: string;
      BETTER_AUTH_URL?: string;

      // Security and rate limiting
      RATE_LIMIT_REQUESTS_PER_MINUTE?: string;
      RATE_LIMIT_ENABLED?: string;

      // Logging and monitoring
      LOG_LEVEL?: 'debug' | 'info' | 'warn' | 'error';
      ENABLE_REQUEST_LOGGING?: string;

      // Email configuration
      RESEND_API_KEY?: string;
      FROM_EMAIL?: string;
      FROM_NAME?: string;
      SUPPORT_EMAIL?: string;

      // CORS configuration
      CORS_ORIGINS?: string;
      CORS_METHODS?: string;
      CORS_HEADERS?: string;
    } & CloudflareBindings;
  }
}
