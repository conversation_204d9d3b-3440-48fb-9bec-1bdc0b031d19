# ezcontext-n

A production-ready Cloudflare Workers-based semantic search API for library documentation with comprehensive CRUD operations, file processing, and MCP (Model Context Protocol) server integration.

## Features

### Core Functionality
- **Semantic Search**: Multi-provider document search (OpenAI, VoyageAI, Cloudflare AI)
- **File Processing**: Upload and process 25+ file types including PDF, DOC, and code files
- **Document Management**: Full CRUD operations with pagination and filtering
- **Content Intelligence**: Automatic code detection and categorization
- **Duplicate Prevention**: SHA-256 content hashing for duplicate detection

### AI Integration
- **MCP Server**: Model Context Protocol server for AI agent integration
- **Multiple Embedding Providers**: OpenAI, VoyageAI, and Cloudflare AI support
- **Optimized Responses**: AI-friendly response formatting
- **Real-time Communication**: WebSocket support for MCP protocol

### Production Features
- **Security**: Rate limiting, input sanitization, security headers
- **Monitoring**: Comprehensive logging and health checks
- **Validation**: Zod-based validation across all endpoints
- **Error Handling**: Graceful error handling with proper HTTP status codes
- **Modular Architecture**: Clean separation of services, routes, and middleware

## Architecture

- **Backend**: Cloudflare Workers with Hono framework
- **Document Database**: Cloudflare Vectorize (1024-dimensional embeddings)
- **Storage**: Cloudflare KV for caching and R2 for file storage
- **AI Integration**: Multi-provider embedding support
- **Protocol**: MCP (Model Context Protocol) for AI agent communication
- **Validation**: @hono/zod-validator for comprehensive input validation

## Quick Start

### Installation

```bash
# Install dependencies
pnpm install

# Generate Cloudflare types
pnpm run cf-typegen
```

### Development

```bash
# Start development server
pnpm run dev
```

### Deployment

```bash
# Deploy to Cloudflare Workers
pnpm run deploy
```

## API Endpoints

### Search API
- `POST /api/search` - Semantic search with configurable providers
- `GET /api/search/collections` - List available collections
- `POST /api/search/collection-info` - Get detailed collection information
- `GET /api/search/stats` - Get search statistics
- `GET /api/search/health` - Health check for search functionality

### Upload API
- `POST /api/upload` - Upload and process files
- `POST /api/upload/submit-text` - Submit raw text content
- `GET /api/upload/supported-types` - Get supported file types
- `GET /api/upload/health` - Health check for upload functionality

### Document Management API
- `GET /api/documents` - List documents with pagination
- `GET /api/documents/{id}` - Get specific document
- `PUT /api/documents/{id}` - Update document metadata
- `DELETE /api/documents/{id}` - Delete specific document
- `POST /api/documents/bulk-delete` - Bulk delete with filters
- `GET /api/documents/health` - Health check for document operations

### MCP Server
- `WebSocket /mcp/websocket` - MCP protocol endpoint
- `GET /mcp/status` - Connection status and statistics

### Documentation
- `GET /openapi.json` - OpenAPI 3.0 specification
- `GET /` - API overview and health status

## Configuration

### Environment Variables

Required environment variables in `wrangler.toml`:

```toml
[vars]
# Embedding Configuration
EMBEDDING_PROVIDER = "cloudflare"  # or "openai", "voyageai"
EMBEDDING_MODEL = "@cf/baai/bge-large-en-v1.5"
DOCUMENT_DIMENSIONS = "1024"

# API Keys (as secrets)
OPENAI_API_KEY = "your-openai-key"
VOYAGEAI_API_KEY = "your-voyageai-key"

# Optional Configuration
RATE_LIMIT_ENABLED = "true"
RATE_LIMIT_REQUESTS_PER_MINUTE = "60"
ALLOWED_FILE_EXTENSIONS = ".txt,.md,.pdf,.py,.js,.ts"
LOG_LEVEL = "info"
```

### Cloudflare Bindings

Required bindings in `wrangler.toml`:

```toml
[[vectorize]]
binding = "vdb"
index_name = "your-vectorize-index"

[[kv_namespaces]]
binding = "kv"
id = "your-kv-namespace-id"

[[r2_buckets]]
binding = "r2"
bucket_name = "your-r2-bucket"

[ai]
binding = "ai"
```

## Usage Examples

### Search Documentation

```bash
curl -X POST https://your-worker.your-subdomain.workers.dev/api/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "how to use React hooks",
    "library": "react",
    "limit": 5,
    "embedding_provider": "openai"
  }'
```

### Upload File

```bash
curl -X POST https://your-worker.your-subdomain.workers.dev/api/upload \
  -F "file=@documentation.pdf" \
  -F "category=docs" \
  -F "library=my-library"
```

### Submit Text Content

```bash
curl -X POST https://your-worker.your-subdomain.workers.dev/api/upload/submit-text \
  -H "Content-Type: application/json" \
  -d '{
    "text": "This is sample documentation content...",
    "title": "Sample Documentation",
    "category": "docs",
    "library": "my-library"
  }'
```

### List Documents

```bash
curl "https://your-worker.your-subdomain.workers.dev/api/documents?page=1&limit=20&library=react"
```

## Supported File Types

### Documents
- `.txt` - Plain text files
- `.md` - Markdown files
- `.pdf` - PDF documents
- `.doc`, `.docx` - Microsoft Word documents
- `.json` - JSON files

### Code Files
- `.py` - Python
- `.js`, `.jsx` - JavaScript
- `.ts`, `.tsx` - TypeScript
- `.java` - Java
- `.cpp`, `.c`, `.h` - C/C++
- `.cs` - C#
- `.php` - PHP
- `.rb` - Ruby
- `.go` - Go
- `.rs` - Rust
- `.swift` - Swift
- `.kt` - Kotlin
- `.scala` - Scala
- `.r` - R
- `.sql` - SQL
- `.sh`, `.bat` - Shell scripts
- `.yaml`, `.yml` - YAML
- `.xml` - XML
- `.html` - HTML
- `.css`, `.scss`, `.less` - CSS

## MCP Integration

### AI Agent Setup

The MCP server can be used with AI agents like Claude, GPT, and others that support the Model Context Protocol.

#### Connection URL
```
ws://your-worker.your-subdomain.workers.dev/mcp/websocket
```

#### Available Tools
- `search_documentation` - Search library documentation
- `get_library_info` - Get library information
- `list_libraries` - List available libraries

### Example MCP Tool Usage

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "search_documentation",
    "arguments": {
      "query": "React useState hook",
      "library": "react",
      "limit": 5
    }
  }
}
```

## Development

### Project Structure

```
src/
├── api/           # Legacy API files (being phased out)
├── services/      # Business logic services
├── routes/        # API route handlers
├── middleware/    # Shared middleware
├── types/         # TypeScript type definitions
├── utils/         # Utility functions
├── mcp/           # MCP server implementation
└── index.ts       # Main application entry point
```

### Adding New Features

1. **Services**: Add business logic to `src/services/`
2. **Routes**: Add API endpoints to `src/routes/`
3. **Types**: Define interfaces in `src/types/`
4. **Middleware**: Add shared functionality to `src/middleware/`

### Testing

```bash
# Run tests
pnpm test

# Run tests with coverage
pnpm test:coverage

# Run integration tests
pnpm test:integration
```

## Production Deployment

### Prerequisites
- Cloudflare Workers account
- Vectorize index created (for document embeddings)
- KV namespace created
- R2 bucket created
- API keys configured as secrets

### Deployment Steps

1. **Configure Environment**
   ```bash
   # Set up secrets
   wrangler secret put OPENAI_API_KEY
   wrangler secret put VOYAGEAI_API_KEY
   ```

2. **Deploy**
   ```bash
   pnpm run deploy
   ```

3. **Verify Deployment**
   ```bash
   curl https://your-worker.your-subdomain.workers.dev/
   ```

### Monitoring

- **Health Checks**: Available at `/api/*/health` endpoints
- **Logs**: View in Cloudflare Workers dashboard
- **Metrics**: Monitor via Cloudflare Analytics

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Create an issue in the GitHub repository
- Check the documentation in the `roadmap/` directory
- Review the OpenAPI specification at `/openapi.json`
