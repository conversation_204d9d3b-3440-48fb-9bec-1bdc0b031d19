{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "skipLibCheck": true, "lib": ["ESNext"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@api/*": ["src/api/*"], "@config/*": ["src/config/*"], "@mcp/*": ["src/mcp/*"], "@services": ["src/services"], "@services/*": ["src/services/*"], "@routes": ["src/routes"], "@routes/*": ["src/routes/*"], "@middleware/*": ["src/middleware/*"], "@types": ["src/types"], "@types/*": ["src/types/*"], "@utils": ["src/utils"], "@utils/*": ["src/utils/*"], "@dbSchema": ["src/dbSchema.ts"], "@dbSchema/*": ["src/dbSchema/*"]}}}