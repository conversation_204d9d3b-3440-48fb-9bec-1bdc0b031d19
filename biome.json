{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": true, "includes": ["**/*", "!node_modules/**", "!dist/**", "!build/**", "!.wrangler/**", "!coverage/**", "!worker-configuration.d.ts", "!.git/**", "!pnpm-lock.yaml", "!docs/**", "!roadmap/**", "!seed/**", "!*.md", "!*.txt"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto"}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noExtraBooleanCast": "error", "noUselessCatch": "error", "noUselessTypeConstraint": "error"}, "correctness": {"noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidConstructorSuper": "error", "noInvalidBuiltinInstantiation": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "useIsNan": "error", "useValidForDirection": "error", "useYield": "error"}, "style": {"noNamespace": "error", "useAsConstAssertion": "error", "useBlockStatements": "off", "useConsistentArrayType": {"level": "error", "options": {"syntax": "shorthand"}}, "useForOf": "error", "useFragmentSyntax": "error", "useShorthandFunctionType": "error", "useSingleVarDeclarator": "error"}, "suspicious": {"noArrayIndexKey": "error", "noAssignInExpressions": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noExplicitAny": "error", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useGetterReturn": "error"}, "nursery": {"useSortedClasses": "off"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto"}}, "json": {"formatter": {"trailingCommas": "none"}}}