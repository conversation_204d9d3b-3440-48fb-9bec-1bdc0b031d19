CREATE TABLE "additional_api_requests" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"team_id" uuid NOT NULL,
	"transaction_id" uuid NOT NULL,
	"request_count" integer NOT NULL,
	"used_count" integer DEFAULT 0 NOT NULL,
	"expires_at" timestamp NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "api_key_rotation_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"old_key_id" uuid NOT NULL,
	"new_key_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"team_id" uuid,
	"rotation_type" varchar(20) DEFAULT 'manual' NOT NULL,
	"reason" varchar(100),
	"old_key_name" varchar(255) NOT NULL,
	"old_key_prefix" varchar(20) NOT NULL,
	"new_key_prefix" varchar(20) NOT NULL,
	"rotated_at" timestamp DEFAULT now() NOT NULL,
	"rotated_by" uuid,
	"ip_address" varchar(45),
	"user_agent" text
);
--> statement-breakpoint
CREATE TABLE "api_key_rotation_policies" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"team_id" uuid,
	"enabled" boolean DEFAULT true NOT NULL,
	"rotation_interval_days" integer NOT NULL,
	"rotate_before_expiration_days" integer,
	"overlap_period_hours" integer DEFAULT 24 NOT NULL,
	"notify_before_rotation" boolean DEFAULT true NOT NULL,
	"notification_days" integer DEFAULT 3 NOT NULL,
	"key_name_pattern" varchar(255),
	"permissions" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"last_applied_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "email_notification_preferences" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"team_id" uuid,
	"expiration_warnings" boolean DEFAULT true NOT NULL,
	"expiration_warning_days" text DEFAULT '[7,1]' NOT NULL,
	"rotation_notifications" boolean DEFAULT true NOT NULL,
	"rotation_failure_alerts" boolean DEFAULT true NOT NULL,
	"bulk_operation_summaries" boolean DEFAULT true NOT NULL,
	"team_expiration_summaries" boolean DEFAULT false NOT NULL,
	"email_enabled" boolean DEFAULT true NOT NULL,
	"webhook_enabled" boolean DEFAULT false NOT NULL,
	"webhook_url" varchar(500),
	"max_emails_per_day" integer DEFAULT 10 NOT NULL,
	"quiet_hours_start" varchar(5),
	"quiet_hours_end" varchar(5),
	"quiet_hours_timezone" varchar(50) DEFAULT 'UTC',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "notification_queue" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"type" varchar(50) NOT NULL,
	"priority" varchar(10) DEFAULT 'normal' NOT NULL,
	"user_id" uuid NOT NULL,
	"user_email" varchar(255) NOT NULL,
	"team_id" uuid,
	"context" text NOT NULL,
	"scheduled_at" timestamp DEFAULT now() NOT NULL,
	"max_retries" integer DEFAULT 3 NOT NULL,
	"retry_count" integer DEFAULT 0 NOT NULL,
	"status" varchar(20) DEFAULT 'pending' NOT NULL,
	"last_attempt_at" timestamp,
	"sent_at" timestamp,
	"error" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "scheduled_api_key_rotations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"key_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"team_id" uuid,
	"scheduled_at" timestamp NOT NULL,
	"rotation_type" varchar(20) DEFAULT 'scheduled' NOT NULL,
	"reason" varchar(100),
	"overlap_period_hours" integer DEFAULT 24 NOT NULL,
	"status" varchar(20) DEFAULT 'pending' NOT NULL,
	"executed_at" timestamp,
	"failure_reason" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "billing_transactions" ADD COLUMN "type" varchar(50) DEFAULT 'subscription' NOT NULL;--> statement-breakpoint
ALTER TABLE "additional_api_requests" ADD CONSTRAINT "additional_api_requests_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "additional_api_requests" ADD CONSTRAINT "additional_api_requests_transaction_id_billing_transactions_id_fk" FOREIGN KEY ("transaction_id") REFERENCES "public"."billing_transactions"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "api_key_rotation_history" ADD CONSTRAINT "api_key_rotation_history_new_key_id_api_keys_id_fk" FOREIGN KEY ("new_key_id") REFERENCES "public"."api_keys"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "api_key_rotation_history" ADD CONSTRAINT "api_key_rotation_history_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "api_key_rotation_history" ADD CONSTRAINT "api_key_rotation_history_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "api_key_rotation_history" ADD CONSTRAINT "api_key_rotation_history_rotated_by_users_id_fk" FOREIGN KEY ("rotated_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "api_key_rotation_policies" ADD CONSTRAINT "api_key_rotation_policies_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "api_key_rotation_policies" ADD CONSTRAINT "api_key_rotation_policies_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "email_notification_preferences" ADD CONSTRAINT "email_notification_preferences_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "email_notification_preferences" ADD CONSTRAINT "email_notification_preferences_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "notification_queue" ADD CONSTRAINT "notification_queue_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "notification_queue" ADD CONSTRAINT "notification_queue_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_api_key_rotations" ADD CONSTRAINT "scheduled_api_key_rotations_key_id_api_keys_id_fk" FOREIGN KEY ("key_id") REFERENCES "public"."api_keys"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_api_key_rotations" ADD CONSTRAINT "scheduled_api_key_rotations_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_api_key_rotations" ADD CONSTRAINT "scheduled_api_key_rotations_team_id_teams_id_fk" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE cascade ON UPDATE no action;