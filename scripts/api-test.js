#!/usr/bin/env node

/**
 * Comprehensive API Test Script for EZContext-N Backend
 *
 * Tests all API endpoints using fetch() requests only.
 * Covers authentication, CRUD operations, vector operations, and MCP server endpoints.
 *
 * Usage:
 *   node scripts/api-test.js [BASE_URL]
 *
 * Environment Variables:
 *   API_BASE_URL - Base URL for the API (default: http://localhost:8787)
 *   TEST_EMAIL - Email for test user (default: <EMAIL>)
 *   TEST_PASSWORD - Password for test user (default: testpassword123)
 *   VERBOSE - Enable verbose logging (default: false)
 *   SKIP_AUTH_TESTS - Skip authentication tests (default: false)
 *   SKIP_UPLOAD_TESTS - Skip file upload tests (default: false)
 */

const fs = require('node:fs');
const path = require('node:path');

// Configuration
const BASE_URL = process.argv[2] || process.env.API_BASE_URL || 'http://localhost:8787';
const TEST_EMAIL = process.env.TEST_EMAIL || '<EMAIL>';
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'testpassword123';
const VERBOSE = process.env.VERBOSE === 'true';
const SKIP_AUTH_TESTS = process.env.SKIP_AUTH_TESTS === 'true';
const SKIP_UPLOAD_TESTS = process.env.SKIP_UPLOAD_TESTS === 'true';

// Test results tracking
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;
const testResults = [];
let authToken = null;
let apiKey = null;
let testTeamId = null;
let testProjectId = null;

// Utility functions
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const prefix =
    level === 'error' ? '❌' : level === 'success' ? '✅' : level === 'warn' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function verbose(message) {
  if (VERBOSE) {
    console.log(`🔍 ${message}`);
  }
}

function recordTest(name, passed, details = '') {
  totalTests++;
  if (passed) {
    passedTests++;
    log(`${name} - PASSED ${details}`, 'success');
  } else {
    failedTests++;
    log(`${name} - FAILED ${details}`, 'error');
  }
  testResults.push({ name, passed, details });
}

// HTTP request helper
async function makeRequest(method, endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    'X-Request-ID': `test-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    ...options.headers,
  };

  if (authToken) {
    headers.Authorization = `Bearer ${authToken}`;
  }

  const config = {
    method,
    headers,
    ...options,
  };

  if (options.body && typeof options.body === 'object') {
    config.body = JSON.stringify(options.body);
  }

  verbose(`${method} ${url} ${config.body ? `with body: ${config.body}` : ''}`);

  try {
    const response = await fetch(url, config);
    const responseText = await response.text();

    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = { raw: responseText };
    }

    verbose(
      `Response: ${response.status} ${responseText.substring(0, 200)}${responseText.length > 200 ? '...' : ''}`
    );

    return {
      status: response.status,
      ok: response.ok,
      data: responseData,
      headers: response.headers,
    };
  } catch (error) {
    verbose(`Request failed: ${error.message}`);
    return {
      status: 0,
      ok: false,
      error: error.message,
      data: null,
    };
  }
}

// Validation helpers
function validateResponse(response, expectedStatus, testName, additionalChecks = {}) {
  const statusMatch = response.status === expectedStatus;
  const hasData = response.data !== null;

  let details = `Status: ${response.status}`;
  let passed = statusMatch && hasData;

  if (additionalChecks.hasStatus && response.data) {
    const hasStatusField = 'status' in response.data;
    passed = passed && hasStatusField;
    details += hasStatusField ? ', has status field' : ', missing status field';
  }

  if (additionalChecks.hasTimestamp && response.data) {
    const hasTimestamp = 'timestamp' in response.data;
    passed = passed && hasTimestamp;
    details += hasTimestamp ? ', has timestamp' : ', missing timestamp';
  }

  if (additionalChecks.hasData && response.data) {
    const hasDataField = 'data' in response.data;
    passed = passed && hasDataField;
    details += hasDataField ? ', has data field' : ', missing data field';
  }

  recordTest(testName, passed, details);
  return passed;
}

// Create test files
function createTestFiles() {
  const testDir = path.join(__dirname, 'test-files');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }

  // Create test markdown file
  const markdownContent = `# Test Document

This is a test document for API testing.

## Features
- Document upload
- Content processing
- Vector embedding

## Code Example
\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`
`;

  fs.writeFileSync(path.join(testDir, 'test-document.md'), markdownContent);

  // Create test code file
  const codeContent = `/**
 * Test JavaScript file for code detection
 */

class Calculator {
  constructor() {
    this.result = 0;
  }

  add(a, b) {
    return a + b;
  }

  multiply(a, b) {
    return a * b;
  }
}

function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

module.exports = { fibonacci, Calculator };
`;

  fs.writeFileSync(path.join(testDir, 'test-code.js'), codeContent);

  return {
    textFile: path.join(testDir, 'test-document.md'),
    codeFile: path.join(testDir, 'test-code.js'),
  };
}

// Test suites
async function testRootEndpoints() {
  log('Testing Root & Documentation Endpoints...', 'info');

  // Test root endpoint
  const rootResponse = await makeRequest('GET', '/');
  validateResponse(rootResponse, 200, 'GET / - Root endpoint', {
    hasStatus: true,
    hasTimestamp: true,
  });

  // Test OpenAPI spec
  const openApiResponse = await makeRequest('GET', '/openapi.json');
  validateResponse(openApiResponse, 200, 'GET /openapi.json - OpenAPI specification');

  // Test API docs
  const docsResponse = await makeRequest('GET', '/docs');
  validateResponse(docsResponse, 200, 'GET /docs - API documentation');
}

async function testAuthenticationEndpoints() {
  if (SKIP_AUTH_TESTS) {
    log('Skipping authentication tests...', 'warn');
    return;
  }

  log('Testing Authentication Endpoints...', 'info');

  // Test user registration
  const registerResponse = await makeRequest('POST', '/api/auth/sign-up', {
    body: {
      email: TEST_EMAIL,
      password: TEST_PASSWORD,
      name: 'Test User',
    },
  });

  // Registration might fail if user exists, that's okay
  if (registerResponse.status === 201 || registerResponse.status === 409) {
    recordTest(
      'POST /api/auth/sign-up - User registration',
      true,
      `Status: ${registerResponse.status}`
    );
  } else {
    recordTest(
      'POST /api/auth/sign-up - User registration',
      false,
      `Status: ${registerResponse.status}`
    );
  }

  // Test user login
  const loginResponse = await makeRequest('POST', '/api/auth/sign-in', {
    body: {
      email: TEST_EMAIL,
      password: TEST_PASSWORD,
    },
  });

  if (loginResponse.ok && loginResponse.data && loginResponse.data.token) {
    authToken = loginResponse.data.token;
    recordTest('POST /api/auth/sign-in - User login', true, 'Token received');
  } else {
    recordTest('POST /api/auth/sign-in - User login', false, `Status: ${loginResponse.status}`);
  }

  // Test session info
  if (authToken) {
    const sessionResponse = await makeRequest('GET', '/api/auth/session');
    validateResponse(sessionResponse, 200, 'GET /api/auth/session - Session info', {
      hasData: true,
    });
  }
}

async function testTeamManagement() {
  if (!authToken) {
    log('Skipping team tests - no auth token', 'warn');
    return;
  }

  log('Testing Team Management Endpoints...', 'info');

  // Create a test team
  const createTeamResponse = await makeRequest('POST', '/api/teams', {
    body: {
      name: 'Test Team',
      slug: `test-team-${Date.now()}`,
      description: 'A test team for API testing',
    },
  });

  if (createTeamResponse.ok && createTeamResponse.data && createTeamResponse.data.data) {
    testTeamId = createTeamResponse.data.data.id;
    recordTest('POST /api/teams - Create team', true, `Team ID: ${testTeamId}`);
  } else {
    recordTest('POST /api/teams - Create team', false, `Status: ${createTeamResponse.status}`);
  }

  // List teams
  const listTeamsResponse = await makeRequest('GET', '/api/teams');
  validateResponse(listTeamsResponse, 200, 'GET /api/teams - List teams', {
    hasData: true,
  });

  // Get specific team
  if (testTeamId) {
    const getTeamResponse = await makeRequest('GET', `/api/teams/${testTeamId}`);
    validateResponse(getTeamResponse, 200, 'GET /api/teams/:id - Get team details', {
      hasData: true,
    });
  }
}

async function testApiKeyManagement() {
  if (!authToken) {
    log('Skipping API key tests - no auth token', 'warn');
    return;
  }

  log('Testing API Key Management Endpoints...', 'info');

  // Create an API key
  const createKeyResponse = await makeRequest('POST', '/api/api-keys', {
    body: {
      name: 'Test API Key',
      permissions: ['read.vectors', 'manage.vectors'],
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
    },
  });

  if (createKeyResponse.ok && createKeyResponse.data && createKeyResponse.data.data) {
    apiKey = createKeyResponse.data.data.key;
    recordTest('POST /api/api-keys - Create API key', true, 'API key created');
  } else {
    recordTest('POST /api/api-keys - Create API key', false, `Status: ${createKeyResponse.status}`);
  }

  // List API keys
  const listKeysResponse = await makeRequest('GET', '/api/api-keys');
  validateResponse(listKeysResponse, 200, 'GET /api/api-keys - List API keys', {
    hasData: true,
  });

  // Test API key authentication
  if (apiKey) {
    const testApiKeyResponse = await makeRequest('GET', '/api/documents', {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    });
    validateResponse(testApiKeyResponse, 200, 'API Key Authentication Test', {
      hasData: true,
    });
  }
}

async function testProjectManagement() {
  if (!authToken) {
    log('Skipping project tests - no auth token', 'warn');
    return;
  }

  log('Testing Project Management Endpoints...', 'info');

  // Create a test project
  const createProjectResponse = await makeRequest('POST', '/api/projects', {
    body: {
      name: 'Test Project',
      description: 'A test project for API testing',
      teamId: testTeamId,
    },
  });

  if (createProjectResponse.ok && createProjectResponse.data && createProjectResponse.data.data) {
    testProjectId = createProjectResponse.data.data.id;
    recordTest('POST /api/projects - Create project', true, `Project ID: ${testProjectId}`);
  } else {
    recordTest(
      'POST /api/projects - Create project',
      false,
      `Status: ${createProjectResponse.status}`
    );
  }

  // List projects
  const listProjectsResponse = await makeRequest('GET', '/api/projects');
  validateResponse(listProjectsResponse, 200, 'GET /api/projects - List projects', {
    hasData: true,
  });

  // Get specific project
  if (testProjectId) {
    const getProjectResponse = await makeRequest('GET', `/api/projects/${testProjectId}`);
    validateResponse(getProjectResponse, 200, 'GET /api/projects/:id - Get project details', {
      hasData: true,
    });
  }
}

async function testDocumentOperations() {
  if (!authToken && !apiKey) {
    log('Skipping document tests - no authentication', 'warn');
    return;
  }

  log('Testing Document Management Endpoints...', 'info');

  // List documents
  const listDocsResponse = await makeRequest('GET', '/api/documents?page=1&limit=10');
  validateResponse(listDocsResponse, 200, 'GET /api/documents - List documents', {
    hasData: true,
  });

  // Test backward compatibility with vectors endpoint
  const listVectorsResponse = await makeRequest('GET', '/api/vectors?page=1&limit=10');
  validateResponse(listVectorsResponse, 200, 'GET /api/vectors - Backward compatibility', {
    hasData: true,
  });

  // Test document health check
  const healthResponse = await makeRequest('GET', '/api/documents/health');
  validateResponse(healthResponse, 200, 'GET /api/documents/health - Health check');
}

async function testSearchEndpoints() {
  if (!authToken && !apiKey) {
    log('Skipping search tests - no authentication', 'warn');
    return;
  }

  log('Testing Search Endpoints...', 'info');

  // Test semantic search
  const searchResponse = await makeRequest('POST', '/api/search', {
    body: {
      query: 'test document',
      limit: 5,
      embedding_provider: 'cloudflare',
    },
  });

  validateResponse(searchResponse, 200, 'POST /api/search - Semantic search', {
    hasData: true,
  });

  // Test search with filters
  const filteredSearchResponse = await makeRequest('POST', '/api/search', {
    body: {
      query: 'javascript function',
      limit: 3,
      filters: {
        category: 'code',
      },
    },
  });

  validateResponse(filteredSearchResponse, 200, 'POST /api/search - Filtered search', {
    hasData: true,
  });
}

async function testUploadEndpoints() {
  if (SKIP_UPLOAD_TESTS) {
    log('Skipping upload tests...', 'warn');
    return;
  }

  if (!authToken && !apiKey) {
    log('Skipping upload tests - no authentication', 'warn');
    return;
  }

  log('Testing Upload Endpoints...', 'info');

  // Test supported file types endpoint
  const supportedTypesResponse = await makeRequest('GET', '/api/upload/supported-types');
  validateResponse(supportedTypesResponse, 200, 'GET /api/upload/supported-types', {
    hasData: true,
  });

  // Create test files
  createTestFiles();

  // Test text upload
  const textUploadResponse = await makeRequest('POST', '/api/upload/text', {
    body: {
      content: 'This is a test document for upload testing.',
      title: 'Test Upload Document',
      category: 'docs',
      collection: 'test-collection',
    },
  });

  validateResponse(textUploadResponse, 201, 'POST /api/upload/text - Text upload', {
    hasData: true,
  });

  // Note: File upload testing would require multipart/form-data which is more complex
  // For now, we'll test the text upload endpoint which covers the core functionality
}

async function testBillingEndpoints() {
  if (!authToken) {
    log('Skipping billing tests - no auth token', 'warn');
    return;
  }

  log('Testing Billing Endpoints...', 'info');

  // Test usage metrics
  const usageResponse = await makeRequest('GET', '/api/billing/usage');
  validateResponse(usageResponse, 200, 'GET /api/billing/usage - Usage metrics', {
    hasData: true,
  });

  // Test subscription info
  const subscriptionResponse = await makeRequest('GET', '/api/billing/subscription');
  // This might return 404 if no subscription exists, which is okay
  if (subscriptionResponse.status === 200 || subscriptionResponse.status === 404) {
    recordTest(
      'GET /api/billing/subscription - Subscription info',
      true,
      `Status: ${subscriptionResponse.status}`
    );
  } else {
    recordTest(
      'GET /api/billing/subscription - Subscription info',
      false,
      `Status: ${subscriptionResponse.status}`
    );
  }
}

async function testMCPEndpoints() {
  log('Testing MCP Server Endpoints...', 'info');

  // Test WebSocket upgrade (this will fail in Node.js but we can test the endpoint exists)
  const mcpResponse = await makeRequest('GET', '/mcp/websocket', {
    headers: {
      Upgrade: 'websocket',
      Connection: 'Upgrade',
      'Sec-WebSocket-Key': 'test-key',
      'Sec-WebSocket-Version': '13',
    },
  });

  // We expect this to fail in Node.js environment, but endpoint should exist
  if (mcpResponse.status === 400 || mcpResponse.status === 426) {
    recordTest(
      'GET /mcp/websocket - MCP WebSocket endpoint exists',
      true,
      `Status: ${mcpResponse.status}`
    );
  } else {
    recordTest(
      'GET /mcp/websocket - MCP WebSocket endpoint exists',
      false,
      `Status: ${mcpResponse.status}`
    );
  }
}

async function testErrorScenarios() {
  log('Testing Error Scenarios...', 'info');

  // Test 404 endpoint
  const notFoundResponse = await makeRequest('GET', '/api/nonexistent');
  validateResponse(notFoundResponse, 404, 'GET /api/nonexistent - 404 handling');

  // Test invalid JSON
  const invalidJsonResponse = await makeRequest('POST', '/api/search', {
    body: 'invalid json',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (invalidJsonResponse.status >= 400) {
    recordTest(
      'POST with invalid JSON - Error handling',
      true,
      `Status: ${invalidJsonResponse.status}`
    );
  } else {
    recordTest(
      'POST with invalid JSON - Error handling',
      false,
      `Status: ${invalidJsonResponse.status}`
    );
  }

  // Test unauthorized access
  const unauthorizedResponse = await makeRequest('GET', '/api/documents', {
    headers: {
      Authorization: 'Bearer invalid-token',
    },
  });

  if (unauthorizedResponse.status === 401) {
    recordTest(
      'Unauthorized access - 401 handling',
      true,
      `Status: ${unauthorizedResponse.status}`
    );
  } else {
    recordTest(
      'Unauthorized access - 401 handling',
      false,
      `Status: ${unauthorizedResponse.status}`
    );
  }
}

async function testPerformance() {
  log('Testing Performance...', 'info');

  const startTime = Date.now();
  const promises = [];

  // Make 5 concurrent requests to test performance
  for (let i = 0; i < 5; i++) {
    promises.push(makeRequest('GET', '/'));
  }

  await Promise.all(promises);
  const endTime = Date.now();
  const duration = endTime - startTime;

  if (duration < 5000) {
    // Less than 5 seconds for 5 requests
    recordTest('Performance - Concurrent requests', true, `Duration: ${duration}ms`);
  } else {
    recordTest('Performance - Concurrent requests', false, `Duration: ${duration}ms (too slow)`);
  }
}

// Cleanup function
function cleanup() {
  const testDir = path.join(__dirname, 'test-files');
  if (fs.existsSync(testDir)) {
    fs.rmSync(testDir, { recursive: true, force: true });
    verbose('Cleaned up test files');
  }
}

// Report generation
function generateReport() {
  console.log(`\n${'='.repeat(60)}`);
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ❌`);
  console.log(
    `Success Rate: ${totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0}%`
  );
  console.log('='.repeat(60));

  if (failedTests > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults
      .filter((test) => !test.passed)
      .forEach((test) => {
        console.log(`  - ${test.name}: ${test.details}`);
      });
  }

  if (passedTests === totalTests && totalTests > 0) {
    console.log('\n🎉 ALL TESTS PASSED!');
    return true;
  } else {
    console.log(`\n⚠️  ${failedTests} test(s) failed.`);
    return false;
  }
}

// Main execution function
async function main() {
  log(`Starting API validation for: ${BASE_URL}`, 'info');
  log(`Test email: ${TEST_EMAIL}`, 'info');
  log(`Verbose mode: ${VERBOSE}`, 'info');
  log(`Skip auth tests: ${SKIP_AUTH_TESTS}`, 'info');
  log(`Skip upload tests: ${SKIP_UPLOAD_TESTS}`, 'info');
  log('', 'info');

  try {
    // Run all test suites
    await testRootEndpoints();
    await testAuthenticationEndpoints();
    await testTeamManagement();
    await testApiKeyManagement();
    await testProjectManagement();
    await testDocumentOperations();
    await testSearchEndpoints();
    await testUploadEndpoints();
    await testBillingEndpoints();
    await testMCPEndpoints();
    await testErrorScenarios();
    await testPerformance();

    // Generate and display report
    const allTestsPassed = generateReport();

    // Cleanup
    cleanup();

    // Exit with appropriate code
    process.exit(allTestsPassed ? 0 : 1);
  } catch (error) {
    log(`Fatal error during testing: ${error.message}`, 'error');
    cleanup();
    process.exit(1);
  }
}

// Run the tests if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  main,
  testRootEndpoints,
  testAuthenticationEndpoints,
  testTeamManagement,
  testApiKeyManagement,
  testProjectManagement,
  testDocumentOperations,
  testSearchEndpoints,
  testUploadEndpoints,
  testBillingEndpoints,
  testMCPEndpoints,
  testErrorScenarios,
  testPerformance,
};
