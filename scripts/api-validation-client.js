#!/usr/bin/env node

/**
 * Comprehensive API Validation Client for ezcontext-n
 *
 * This standalone test client validates all API endpoints with realistic data,
 * error scenarios, and edge cases. It can be run independently to verify
 * that all API functionality is working correctly.
 *
 * Usage:
 *   node api-validation-client.js [BASE_URL]
 *
 * Environment Variables:
 *   API_BASE_URL - Base URL for the API (default: http://localhost:8787)
 *   VERBOSE - Set to 'true' for detailed output
 *   SKIP_UPLOAD_TESTS - Set to 'true' to skip file upload tests
 */

const fs = require('node:fs');
const path = require('node:path');

// Configuration
const BASE_URL = process.argv[2] || process.env.API_BASE_URL || 'http://localhost:8787';
const VERBOSE = process.env.VERBOSE === 'true';
const SKIP_UPLOAD_TESTS = process.env.SKIP_UPLOAD_TESTS === 'true';

// Test results tracking
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;
const testResults = [];

// Utility functions
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const prefix =
    level === 'error' ? '❌' : level === 'success' ? '✅' : level === 'warn' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function verbose(message) {
  if (VERBOSE) {
    console.log(`🔍 ${message}`);
  }
}

async function makeRequest(method, endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  verbose(`${method} ${url}`);

  const config = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'ezcontext-api-validator/1.0',
      ...options.headers,
    },
    ...options,
  };

  if (options.body && typeof options.body === 'object' && !options.formData) {
    config.body = JSON.stringify(options.body);
  }

  try {
    const response = await fetch(url, config);
    const contentType = response.headers.get('content-type');

    let data;
    if (contentType?.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    return {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      data,
    };
  } catch (error) {
    return {
      status: 0,
      statusText: 'Network Error',
      error: error.message,
      data: null,
    };
  }
}

function recordTest(testName, passed, details = {}) {
  totalTests++;
  if (passed) {
    passedTests++;
    log(`${testName} - PASSED`, 'success');
  } else {
    failedTests++;
    log(`${testName} - FAILED: ${details.error || 'Unknown error'}`, 'error');
  }

  testResults.push({
    name: testName,
    passed,
    timestamp: new Date().toISOString(),
    ...details,
  });
}

// Test helper functions
function validateResponse(response, expectedStatus, testName, additionalChecks = {}) {
  const passed = response.status === expectedStatus;
  const details = {
    expectedStatus,
    actualStatus: response.status,
    responseData: response.data,
    error: passed ? null : `Expected status ${expectedStatus}, got ${response.status}`,
  };

  // Additional validation checks
  if (passed && additionalChecks.hasData) {
    const hasData = response.data && typeof response.data === 'object' && response.data.data;
    if (!hasData) {
      details.error = 'Response missing data field';
      recordTest(testName, false, details);
      return false;
    }
  }

  if (passed && additionalChecks.hasStatus) {
    const hasStatus = response.data?.status;
    if (!hasStatus) {
      details.error = 'Response missing status field';
      recordTest(testName, false, details);
      return false;
    }
  }

  if (passed && additionalChecks.hasTimestamp) {
    const hasTimestamp = response.data?.timestamp;
    if (!hasTimestamp) {
      details.error = 'Response missing timestamp field';
      recordTest(testName, false, details);
      return false;
    }
  }

  recordTest(testName, passed, details);
  return passed;
}

// Create test files for upload testing
function createTestFiles() {
  const testDir = path.join(__dirname, 'test-files');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir);
  }

  // Create a simple text file
  const textContent = `# Test Document

This is a test document for API validation.
It contains multiple lines and some **markdown** formatting.

## Features
- Text processing
- Vector embedding
- Search functionality

The quick brown fox jumps over the lazy dog.
`;

  fs.writeFileSync(path.join(testDir, 'test-document.md'), textContent);

  // Create a simple code file
  const codeContent = `/**
 * Test JavaScript file for code processing
 */

function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

class Calculator {
  constructor() {
    this.history = [];
  }

  add(a, b) {
    const result = a + b;
    this.history.push(\`\${a} + \${b} = \${result}\`);
    return result;
  }

  getHistory() {
    return this.history;
  }
}

module.exports = { fibonacci, Calculator };
`;

  fs.writeFileSync(path.join(testDir, 'test-code.js'), codeContent);

  return {
    textFile: path.join(testDir, 'test-document.md'),
    codeFile: path.join(testDir, 'test-code.js'),
  };
}

// Test suites
async function testRootEndpoints() {
  log('Testing Root & Documentation Endpoints...', 'info');

  // Test root endpoint
  const rootResponse = await makeRequest('GET', '/');
  validateResponse(rootResponse, 200, 'GET / - Root endpoint', {
    hasStatus: true,
    hasTimestamp: true,
  });

  // Test OpenAPI JSON
  const openApiResponse = await makeRequest('GET', '/openapi.json');
  validateResponse(openApiResponse, 200, 'GET /openapi.json - OpenAPI specification');

  // Test OpenAPI YAML
  const openApiYamlResponse = await makeRequest('GET', '/openapi.yaml');
  validateResponse(openApiYamlResponse, 200, 'GET /openapi.yaml - OpenAPI YAML');

  // Test API docs
  const docsResponse = await makeRequest('GET', '/docs');
  validateResponse(docsResponse, 200, 'GET /docs - API documentation');
}

async function testSearchEndpoints() {
  log('Testing Search API Endpoints...', 'info');

  // Test semantic search - happy path
  const searchResponse = await makeRequest('POST', '/api/search', {
    body: {
      query: 'Next.js routing and navigation',
      limit: 5,
      returnMetadata: true,
      returnValues: false,
    },
  });
  validateResponse(searchResponse, 200, 'POST /api/search - Semantic search', {
    hasData: true,
    hasStatus: true,
  });

  // Test search with collection filter
  const collectionSearchResponse = await makeRequest('POST', '/api/search', {
    body: {
      query: 'metadata management',
      collection: 'next.js',
      limit: 3,
      embedding_provider: 'cloudflare',
    },
  });
  validateResponse(collectionSearchResponse, 200, 'POST /api/search - Collection filtered search', {
    hasData: true,
  });

  // Test search with invalid parameters
  const invalidSearchResponse = await makeRequest('POST', '/api/search', {
    body: {
      query: '',
      limit: 25, // Exceeds max limit
    },
  });
  validateResponse(invalidSearchResponse, 400, 'POST /api/search - Invalid parameters');

  // Test search with missing query
  const missingQueryResponse = await makeRequest('POST', '/api/search', {
    body: {
      limit: 5,
    },
  });
  validateResponse(missingQueryResponse, 400, 'POST /api/search - Missing query');

  // Test get available collections
  const collectionsResponse = await makeRequest('GET', '/api/search/collections');
  validateResponse(collectionsResponse, 200, 'GET /api/search/collections - List collections', {
    hasData: true,
    hasStatus: true,
  });

  // Test collection info
  const collectionInfoResponse = await makeRequest('POST', '/api/search/collection-info', {
    body: {
      collection_name: 'next.js',
      include_versions: true,
    },
  });
  validateResponse(
    collectionInfoResponse,
    200,
    'POST /api/search/collection-info - Collection details',
    {
      hasData: true,
    }
  );

  // Test collection info with invalid collection
  const invalidCollectionResponse = await makeRequest('POST', '/api/search/collection-info', {
    body: {
      collection_name: '',
      include_versions: false,
    },
  });
  validateResponse(
    invalidCollectionResponse,
    400,
    'POST /api/search/collection-info - Invalid collection'
  );

  // Test search stats
  const statsResponse = await makeRequest('GET', '/api/search/stats');
  validateResponse(statsResponse, 200, 'GET /api/search/stats - Search statistics', {
    hasData: true,
  });

  // Test search health check
  const searchHealthResponse = await makeRequest('GET', '/api/search/health');
  validateResponse(searchHealthResponse, 200, 'GET /api/search/health - Health check');
}

async function testUploadEndpoints() {
  log('Testing Upload API Endpoints...', 'info');

  if (SKIP_UPLOAD_TESTS) {
    log('Skipping upload tests (SKIP_UPLOAD_TESTS=true)', 'warn');
    return;
  }

  const testFiles = createTestFiles();

  // Test text submission - happy path
  const textSubmissionResponse = await makeRequest('POST', '/api/upload/submit-text', {
    body: {
      text: 'This is a test document for API validation. It demonstrates text processing and vector embedding capabilities.',
      title: 'API Test Document',
      category: 'docs',
      collection: 'test-collection',
    },
  });
  validateResponse(textSubmissionResponse, 201, 'POST /api/upload/submit-text - Text submission', {
    hasStatus: true,
  });

  // Test text submission with code category
  const codeTextResponse = await makeRequest('POST', '/api/upload/submit-text', {
    body: {
      text: 'function hello() { console.log("Hello, World!"); }',
      title: 'Test Code Snippet',
      category: 'code',
      embedding_provider: 'voyageai',
    },
  });
  validateResponse(codeTextResponse, 201, 'POST /api/upload/submit-text - Code text submission');

  // Test text submission with invalid data
  const invalidTextResponse = await makeRequest('POST', '/api/upload/submit-text', {
    body: {
      text: '',
      title: 'Empty Text Test',
    },
  });
  validateResponse(invalidTextResponse, 400, 'POST /api/upload/submit-text - Invalid text');

  // Test text submission with missing title
  const missingTitleResponse = await makeRequest('POST', '/api/upload/submit-text', {
    body: {
      text: 'Some content without title',
    },
  });
  validateResponse(missingTitleResponse, 400, 'POST /api/upload/submit-text - Missing title');

  // Test file upload - markdown file
  if (fs.existsSync(testFiles.textFile)) {
    const formData = new FormData();
    const fileContent = fs.readFileSync(testFiles.textFile);
    const blob = new Blob([fileContent], { type: 'text/markdown' });
    formData.append('file', blob, 'test-document.md');
    formData.append('category', 'docs');
    formData.append('collection', 'test-uploads');

    const fileUploadResponse = await makeRequest('POST', '/api/upload', {
      body: formData,
      headers: {}, // Let fetch set Content-Type for FormData
    });
    validateResponse(fileUploadResponse, 201, 'POST /api/upload - File upload (markdown)');
  }

  // Test file upload without file
  const noFileResponse = await makeRequest('POST', '/api/upload', {
    body: new FormData(),
    headers: {},
  });
  validateResponse(noFileResponse, 400, 'POST /api/upload - No file provided');

  // Test supported file types
  const supportedTypesResponse = await makeRequest('GET', '/api/upload/supported-types');
  validateResponse(
    supportedTypesResponse,
    200,
    'GET /api/upload/supported-types - Supported types',
    {
      hasData: true,
      hasStatus: true,
    }
  );

  // Test upload health check
  const uploadHealthResponse = await makeRequest('GET', '/api/upload/health');
  validateResponse(uploadHealthResponse, 200, 'GET /api/upload/health - Health check');
}

async function testVectorEndpoints() {
  log('Testing Vector Management API Endpoints...', 'info');

  // Test list vectors - happy path
  const listVectorsResponse = await makeRequest('GET', '/api/vectors?page=1&limit=10');
  validateResponse(listVectorsResponse, 200, 'GET /api/vectors - List vectors', {
    hasData: true,
    hasStatus: true,
  });

  // Test list vectors with filters
  const filteredVectorsResponse = await makeRequest(
    'GET',
    '/api/vectors?page=1&limit=5&collection=test&category=docs'
  );
  validateResponse(filteredVectorsResponse, 200, 'GET /api/vectors - Filtered list');

  // Test list vectors with invalid pagination
  const invalidPaginationResponse = await makeRequest('GET', '/api/vectors?page=0&limit=200');
  validateResponse(invalidPaginationResponse, 400, 'GET /api/vectors - Invalid pagination');

  // Test vector health check
  const vectorHealthResponse = await makeRequest('GET', '/api/vectors/health');
  validateResponse(vectorHealthResponse, 200, 'GET /api/vectors/health - Health check');

  // Test bulk delete with filters
  const bulkDeleteResponse = await makeRequest('POST', '/api/vectors/bulk-delete', {
    body: {
      collection: 'test-collection',
      category: 'docs',
    },
  });
  validateResponse(bulkDeleteResponse, 200, 'POST /api/vectors/bulk-delete - Bulk delete', {
    hasData: true,
  });

  // Test bulk delete with empty filters
  const emptyBulkDeleteResponse = await makeRequest('POST', '/api/vectors/bulk-delete', {
    body: {},
  });
  validateResponse(emptyBulkDeleteResponse, 200, 'POST /api/vectors/bulk-delete - Empty filters');

  // Note: Individual vector operations (GET/PUT/DELETE /:id) are known to have limitations
  // with Cloudflare Vectorize, so we test them but expect 404 responses

  // Test get specific vector (expected to fail due to Vectorize limitations)
  const getVectorResponse = await makeRequest('GET', '/api/vectors/test-vector-id');
  // This will likely return 404 due to Vectorize limitations, which is expected
  const getVectorPassed = getVectorResponse.status === 200 || getVectorResponse.status === 404;
  recordTest('GET /api/vectors/:id - Get specific vector (known limitation)', getVectorPassed, {
    expectedStatus: '200 or 404 (Vectorize limitation)',
    actualStatus: getVectorResponse.status,
    note: 'Vectorize does not support individual vector queries by ID',
  });

  // Test update vector (expected to fail due to Vectorize limitations)
  const updateVectorResponse = await makeRequest('PUT', '/api/vectors/test-vector-id', {
    body: {
      metadata: {
        updated: 'true',
        timestamp: new Date().toISOString(),
      },
    },
  });
  const updateVectorPassed =
    updateVectorResponse.status === 200 || updateVectorResponse.status === 404;
  recordTest('PUT /api/vectors/:id - Update vector (known limitation)', updateVectorPassed, {
    expectedStatus: '200 or 404 (Vectorize limitation)',
    actualStatus: updateVectorResponse.status,
    note: 'Vectorize does not support individual vector updates by ID',
  });

  // Test delete vector (expected to fail due to Vectorize limitations)
  const deleteVectorResponse = await makeRequest('DELETE', '/api/vectors/test-vector-id');
  const deleteVectorPassed =
    deleteVectorResponse.status === 200 || deleteVectorResponse.status === 404;
  recordTest('DELETE /api/vectors/:id - Delete vector (known limitation)', deleteVectorPassed, {
    expectedStatus: '200 or 404 (Vectorize limitation)',
    actualStatus: deleteVectorResponse.status,
    note: 'Vectorize does not support individual vector deletion by ID',
  });
}

async function testMCPEndpoints() {
  log('Testing MCP Server Endpoints...', 'info');

  // Test MCP WebSocket endpoint (should return 400 for HTTP request)
  const mcpWebSocketResponse = await makeRequest('GET', '/mcp/websocket');
  validateResponse(
    mcpWebSocketResponse,
    400,
    'GET /mcp/websocket - WebSocket endpoint (HTTP request)'
  );

  // Test MCP status
  const mcpStatusResponse = await makeRequest('GET', '/mcp/status');
  validateResponse(mcpStatusResponse, 200, 'GET /mcp/status - Connection status');
}

async function testErrorScenarios() {
  log('Testing Error Scenarios and Edge Cases...', 'info');

  // Test non-existent endpoint
  const notFoundResponse = await makeRequest('GET', '/api/nonexistent');
  validateResponse(notFoundResponse, 404, 'GET /api/nonexistent - Not found');

  // Test malformed JSON
  const malformedJsonResponse = await makeRequest('POST', '/api/search', {
    body: '{"query": "test", "limit":}', // Malformed JSON
    headers: { 'Content-Type': 'application/json' },
  });
  validateResponse(malformedJsonResponse, 400, 'POST /api/search - Malformed JSON');

  // Test unsupported method
  const unsupportedMethodResponse = await makeRequest('PATCH', '/api/search');
  const methodPassed =
    unsupportedMethodResponse.status === 404 || unsupportedMethodResponse.status === 405;
  recordTest('PATCH /api/search - Unsupported method', methodPassed, {
    expectedStatus: '404 or 405',
    actualStatus: unsupportedMethodResponse.status,
  });

  // Test large payload (if search supports it)
  const largeQuery = 'a'.repeat(10000); // 10KB query
  const largePayloadResponse = await makeRequest('POST', '/api/search', {
    body: {
      query: largeQuery,
      limit: 5,
    },
  });
  const largePassed =
    largePayloadResponse.status === 200 ||
    largePayloadResponse.status === 413 ||
    largePayloadResponse.status === 400;
  recordTest('POST /api/search - Large payload', largePassed, {
    expectedStatus: '200, 400, or 413',
    actualStatus: largePayloadResponse.status,
    note: 'Large payloads may be rejected or processed',
  });
}

// Performance testing
async function testPerformance() {
  log('Testing Performance...', 'info');

  const startTime = Date.now();

  // Test concurrent requests
  const concurrentPromises = Array(5)
    .fill(0)
    .map((_, i) =>
      makeRequest('POST', '/api/search', {
        body: {
          query: `performance test query ${i}`,
          limit: 3,
        },
      })
    );

  const results = await Promise.all(concurrentPromises);
  const endTime = Date.now();
  const totalTime = endTime - startTime;

  const allSuccessful = results.every((r) => r.status === 200);
  recordTest('Concurrent requests performance', allSuccessful, {
    totalTime: `${totalTime}ms`,
    requestCount: 5,
    averageTime: `${totalTime / 5}ms`,
    note: `5 concurrent requests completed in ${totalTime}ms`,
  });

  // Test response time for individual request
  const singleStartTime = Date.now();
  await makeRequest('GET', '/api/search/collections');
  const singleEndTime = Date.now();
  const singleTime = singleEndTime - singleStartTime;

  const performanceAcceptable = singleTime < 5000; // 5 second timeout
  recordTest('Single request response time', performanceAcceptable, {
    responseTime: `${singleTime}ms`,
    threshold: '5000ms',
    note: `Single request completed in ${singleTime}ms`,
  });
}

// Cleanup function
function cleanup() {
  const testDir = path.join(__dirname, 'test-files');
  if (fs.existsSync(testDir)) {
    try {
      fs.rmSync(testDir, { recursive: true, force: true });
      verbose('Cleaned up test files');
    } catch (error) {
      log(`Failed to cleanup test files: ${error.message}`, 'warn');
    }
  }
}

// Generate test report
function generateReport() {
  log(`\n${'='.repeat(80)}`, 'info');
  log('API VALIDATION REPORT', 'info');
  log('='.repeat(80), 'info');

  log(`Total Tests: ${totalTests}`, 'info');
  log(`Passed: ${passedTests}`, 'success');
  log(`Failed: ${failedTests}`, failedTests > 0 ? 'error' : 'info');
  log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 'info');

  if (failedTests > 0) {
    log('\nFAILED TESTS:', 'error');
    testResults
      .filter((test) => !test.passed)
      .forEach((test) => {
        log(`  - ${test.name}: ${test.error}`, 'error');
        if (test.note) {
          log(`    Note: ${test.note}`, 'warn');
        }
      });
  }

  log('\nKNOWN LIMITATIONS:', 'warn');
  log('  - Individual vector operations (GET/PUT/DELETE /api/vectors/:id) may return 404', 'warn');
  log('    due to Cloudflare Vectorize limitations with individual vector queries', 'warn');
  log('  - WebSocket endpoints return 400 for HTTP requests (expected behavior)', 'warn');

  if (VERBOSE) {
    log('\nDETAILED RESULTS:', 'info');
    testResults.forEach((test) => {
      const status = test.passed ? '✅' : '❌';
      log(`${status} ${test.name}`, 'info');
      if (test.note) {
        log(`    Note: ${test.note}`, 'info');
      }
    });
  }

  log(`\n${'='.repeat(80)}`, 'info');

  return failedTests === 0;
}

// Main execution function
async function main() {
  log(`Starting API validation for: ${BASE_URL}`, 'info');
  log(`Verbose mode: ${VERBOSE}`, 'info');
  log(`Skip upload tests: ${SKIP_UPLOAD_TESTS}`, 'info');
  log('', 'info');

  try {
    // Run all test suites
    await testRootEndpoints();
    await testSearchEndpoints();
    await testUploadEndpoints();
    await testVectorEndpoints();
    await testMCPEndpoints();
    await testErrorScenarios();
    await testPerformance();

    // Generate and display report
    const allTestsPassed = generateReport();

    // Cleanup
    cleanup();

    // Exit with appropriate code
    process.exit(allTestsPassed ? 0 : 1);
  } catch (error) {
    log(`Fatal error during testing: ${error.message}`, 'error');
    cleanup();
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  log('\nReceived SIGINT, cleaning up...', 'warn');
  cleanup();
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('\nReceived SIGTERM, cleaning up...', 'warn');
  cleanup();
  process.exit(1);
});

// Run the tests if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    log(`Unhandled error: ${error.message}`, 'error');
    cleanup();
    process.exit(1);
  });
}
