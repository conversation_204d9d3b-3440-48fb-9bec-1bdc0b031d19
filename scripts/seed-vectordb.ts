#!/usr/bin/env node

import { readdir, readFile } from 'node:fs/promises';
import { extname, join } from 'node:path';

// Configuration
const SEED_DATA_DIR = './seed';
const CHUNK_SIZE = 1000; // Characters per chunk
const CHUNK_OVERLAP = 200; // Overlap between chunks
const BATCH_SIZE = 10; // Number of embeddings to process in parallel

// Environment variables with new defaults
const EMBEDDING_PROVIDER = (process.env.EMBEDDING_PROVIDER || 'voyageai') as
  | 'openai'
  | 'voyageai'
  | 'cloudflare';
const EMBEDDING_MODEL = process.env.EMBEDDING_MODEL || getDefaultModel(EMBEDDING_PROVIDER);
const EMBEDDING_DIMENSIONS = 1024; // Fixed to 1024 for consistency across all providers
const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.OPENROUTER_API_KEY;
const _OPENAI_BASE_URL = process.env.OPENAI_BASE_URL || 'https://openrouter.ai/api/v1';
const VOYAGEAI_API_KEY = process.env.VOYAGEAI_API_KEY;
const _VECTORIZE_INDEX_NAME = process.env.VECTORIZE_INDEX_NAME || 'ezcontext';
const DRY_RUN = process.env.DRY_RUN === 'true';

// Helper function to get default model for provider
function getDefaultModel(provider: string): string {
  switch (provider) {
    case 'openai':
      return 'text-embedding-3-large';
    case 'voyageai':
      return 'voyage-3-large';
    case 'cloudflare':
      return '@cf/baai/bge-large-en-v1.5';
    default:
      return 'voyage-3-large';
  }
}

interface DocumentChunk {
  id: string;
  content: string;
  metadata: {
    library: string;
    title?: string;
    description?: string;
    source?: string;
    language?: string;
    chunk_index: number;
    total_chunks: number;
    file_name: string;
    content_type: 'documentation' | 'code' | 'example';
    embedding_provider: string;
    embedding_model: string;
    embedding_dimensions: number;
  };
}

interface EmbeddingResult {
  chunk: DocumentChunk;
  embedding: number[];
}

class VectorDBSeeder {
  private processedCount = 0;
  private totalChunks = 0;
  private embeddingService!: {
    generateEmbedding: (text: string) => Promise<{ embedding: number[] }>;
  }; // Will be imported dynamically

  constructor() {
    // Validate provider-specific requirements
    if (!DRY_RUN) {
      switch (EMBEDDING_PROVIDER) {
        case 'openai':
          if (!OPENAI_API_KEY) {
            throw new Error(
              'OpenAI API key not found. Set OPENAI_API_KEY or OPENROUTER_API_KEY environment variable.'
            );
          }
          break;
        case 'voyageai':
          if (!VOYAGEAI_API_KEY) {
            throw new Error(
              'VoyageAI API key not found. Set VOYAGEAI_API_KEY environment variable.'
            );
          }
          break;
        case 'cloudflare':
          console.warn(
            'Note: Cloudflare AI provider requires running in Cloudflare Workers environment.'
          );
          break;
      }
    }
  }

  async run() {
    console.log('🚀 Starting Vector Database Seeding Process');
    console.log(`📁 Reading files from: ${SEED_DATA_DIR}`);
    console.log(`🤖 Embedding provider: ${EMBEDDING_PROVIDER}`);
    console.log(`🎯 Embedding model: ${EMBEDDING_MODEL}`);
    console.log(`📏 Embedding dimensions: ${EMBEDDING_DIMENSIONS}`);
    console.log(`🧪 Dry run mode: ${DRY_RUN ? 'ENABLED' : 'DISABLED'}`);
    console.log('');

    try {
      // Read all files from seed directory
      const files = await this.getDocumentFiles();
      console.log(`📚 Found ${files.length} document files`);

      // Process each file and create chunks
      const allChunks: DocumentChunk[] = [];
      for (const file of files) {
        console.log(`📖 Processing: ${file}`);
        const chunks = await this.processFile(file);
        allChunks.push(...chunks);
        console.log(`   ✅ Created ${chunks.length} chunks`);
      }

      this.totalChunks = allChunks.length;
      console.log(`\n📊 Total chunks to process: ${this.totalChunks}`);

      // Generate embeddings in batches
      console.log('\n🧠 Generating embeddings...');
      const embeddingResults: EmbeddingResult[] = [];

      for (let i = 0; i < allChunks.length; i += BATCH_SIZE) {
        const batch = allChunks.slice(i, i + BATCH_SIZE);
        const batchResults = await this.generateEmbeddingsBatch(batch);
        embeddingResults.push(...batchResults);

        this.processedCount += batch.length;
        const progress = ((this.processedCount / this.totalChunks) * 100).toFixed(1);
        console.log(`   📈 Progress: ${this.processedCount}/${this.totalChunks} (${progress}%)`);
      }

      // Insert into Vectorize database
      console.log('\n💾 Inserting into Vectorize database...');
      await this.insertIntoVectorize(embeddingResults);

      console.log('\n✅ Seeding completed successfully!');
      console.log(`📊 Final stats:`);
      console.log(`   - Files processed: ${files.length}`);
      console.log(`   - Chunks created: ${this.totalChunks}`);
      console.log(`   - Embeddings generated: ${embeddingResults.length}`);
    } catch (error) {
      console.error('\n❌ Seeding failed:', error);
      process.exit(1);
    }
  }

  private async getDocumentFiles(): Promise<string[]> {
    const files = await readdir(SEED_DATA_DIR);
    return files
      .filter((file) => ['.txt', '.md', '.json'].includes(extname(file)))
      .map((file) => join(SEED_DATA_DIR, file));
  }

  private async processFile(filePath: string): Promise<DocumentChunk[]> {
    const content = await readFile(filePath, 'utf-8');
    const fileName = filePath.split('/').pop() || 'unknown';
    const libraryName = this.extractLibraryName(fileName);

    // Parse structured content if it follows our format
    const sections = this.parseStructuredContent(content);

    if (sections.length > 0) {
      // Process structured content (like the Next.js file)
      return this.createChunksFromSections(sections, fileName, libraryName);
    } else {
      // Process as plain text
      return this.createChunksFromText(content, fileName, libraryName);
    }
  }

  private extractLibraryName(fileName: string): string {
    // Extract library name from filename (e.g., "next.js.txt" -> "next.js")
    return fileName.replace(/\.(txt|md|json)$/, '');
  }

  private parseStructuredContent(content: string): Array<{
    title?: string;
    description?: string;
    content: string;
    source?: string;
    language?: string;
    code?: boolean;
  }> {
    const sections = [];
    const parts = content.split('----------------------------------------');

    for (const part of parts) {
      const trimmed = part.trim();
      if (!trimmed) continue;

      const section = this.parseSection(trimmed);
      if (section) {
        sections.push(section);
      }
    }

    return sections;
  }

  private parseSection(sectionText: string): {
    title?: string;
    description?: string;
    content: string;
    source?: string;
    language?: string;
    code?: boolean;
  } | null {
    const lines = sectionText.split('\n');
    const section: {
      title?: string;
      description?: string;
      content: string;
      source?: string;
      language?: string;
      code?: boolean;
    } = { content: '' };

    for (const line of lines) {
      if (line.startsWith('TITLE: ')) {
        section.title = line.substring(7);
      } else if (line.startsWith('DESCRIPTION: ')) {
        section.description = line.substring(13);
      } else if (line.startsWith('SOURCE: ')) {
        section.source = line.substring(8);
      } else if (line.startsWith('LANGUAGE: ')) {
        section.language = line.substring(10);
      } else if (line.startsWith('CODE:')) {
        // Mark as code section
        section.code = true;
      }
    }

    section.content = sectionText;
    return Object.keys(section).length > 1 ? section : null;
  }

  private createChunksFromSections(
    sections: Array<{
      title?: string;
      description?: string;
      content: string;
      source?: string;
      language?: string;
      code?: boolean;
    }>,
    fileName: string,
    libraryName: string
  ): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];

    sections.forEach((section, index) => {
      const chunkId = `${libraryName}-section-${index}`;
      const chunk: DocumentChunk = {
        id: chunkId,
        content: section.content,
        metadata: {
          library: libraryName,
          title: section.title,
          description: section.description,
          source: section.source,
          language: section.language,
          chunk_index: index,
          total_chunks: sections.length,
          file_name: fileName,
          content_type: section.code ? 'code' : 'documentation',
          embedding_provider: EMBEDDING_PROVIDER,
          embedding_model: EMBEDDING_MODEL,
          embedding_dimensions: EMBEDDING_DIMENSIONS,
        },
      };
      chunks.push(chunk);
    });

    return chunks;
  }

  private createChunksFromText(
    content: string,
    fileName: string,
    libraryName: string
  ): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];
    const totalLength = content.length;
    let chunkIndex = 0;

    for (let i = 0; i < totalLength; i += CHUNK_SIZE - CHUNK_OVERLAP) {
      const end = Math.min(i + CHUNK_SIZE, totalLength);
      const chunkContent = content.substring(i, end);

      if (chunkContent.trim().length === 0) continue;

      const chunkId = `${libraryName}-chunk-${chunkIndex}`;
      const chunk: DocumentChunk = {
        id: chunkId,
        content: chunkContent,
        metadata: {
          library: libraryName,
          chunk_index: chunkIndex,
          total_chunks: Math.ceil(totalLength / (CHUNK_SIZE - CHUNK_OVERLAP)),
          file_name: fileName,
          content_type: 'documentation',
          embedding_provider: EMBEDDING_PROVIDER,
          embedding_model: EMBEDDING_MODEL,
          embedding_dimensions: EMBEDDING_DIMENSIONS,
        },
      };

      chunks.push(chunk);
      chunkIndex++;
    }

    return chunks;
  }

  private async generateEmbeddingsBatch(chunks: DocumentChunk[]): Promise<EmbeddingResult[]> {
    if (DRY_RUN) {
      // Generate fake embeddings for dry run
      return chunks.map((chunk) => ({
        chunk,
        embedding: Array(EMBEDDING_DIMENSIONS)
          .fill(0)
          .map(() => Math.random() - 0.5),
      }));
    }

    try {
      // Import and create embedding service with custom configuration
      const { EmbeddingService } = await import('../src/services');
      const embeddingService = EmbeddingService.withConfig({
        provider: EMBEDDING_PROVIDER,
        model: EMBEDDING_MODEL,
        dimensions: EMBEDDING_DIMENSIONS,
      });

      const texts = chunks.map((chunk) => chunk.content);
      const embeddingResults = await embeddingService.generateBatchEmbeddings(texts);

      return chunks.map((chunk, index) => ({
        chunk,
        embedding: embeddingResults[index].embedding,
      }));
    } catch (error) {
      console.error('Error generating embeddings:', error);
      throw error;
    }
  }

  private async insertIntoVectorize(results: EmbeddingResult[]): Promise<void> {
    // Note: This is a placeholder for the actual Vectorize insertion
    // In a real implementation, you would use the Cloudflare Workers environment
    // For now, we'll just log what would be inserted

    console.log('📝 Would insert the following vectors into Vectorize:');
    results.forEach((result, index) => {
      if (index < 3) {
        // Show first 3 as examples
        console.log(`   ${index + 1}. ID: ${result.chunk.id}`);
        console.log(`      Library: ${result.chunk.metadata.library}`);
        console.log(`      Title: ${result.chunk.metadata.title || 'N/A'}`);
        console.log(`      Embedding dimensions: ${result.embedding.length}`);
        console.log(`      Content preview: ${result.chunk.content.substring(0, 100)}...`);
        console.log('');
      }
    });

    if (results.length > 3) {
      console.log(`   ... and ${results.length - 3} more vectors`);
    }

    // TODO: Implement actual Vectorize insertion using Cloudflare Workers API
    // This would require setting up a local development environment or
    // creating a separate endpoint for seeding
    console.log('\n⚠️  Note: Actual Vectorize insertion not implemented yet.');
    console.log('   This script generates embeddings and prepares data for insertion.');
    console.log('   To complete the seeding, you would need to:');
    console.log('   1. Deploy this as a Cloudflare Worker endpoint');
    console.log('   2. Use the Vectorize API to insert the vectors');
    console.log('   3. Or use wrangler CLI with vectorize commands');
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  const seeder = new VectorDBSeeder();
  seeder.run().catch(console.error);
}

export { VectorDBSeeder };
