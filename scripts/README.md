# Vector Database Seeding Script

This directory contains scripts for seeding the Vectorize database with documentation content.

## Usage

### Prerequisites

1. Set up your OpenAI/OpenRouter API key:
   ```bash
   export OPENAI_API_KEY="your-openai-api-key"
   # OR
   export OPENROUTER_API_KEY="your-openrouter-api-key"
   ```

2. Ensure you have documentation files in the `seed/` directory

### Running the Seeder

```bash
# Run with real API calls (requires valid API key)
pnpm seed

# Run in dry-run mode (for testing without API calls)
DRY_RUN=true pnpm seed

# Run with custom OpenAI base URL
OPENAI_BASE_URL=https://api.openai.com/v1 pnpm seed
```

### Environment Variables

- `OPENAI_API_KEY` or `OPENROUTER_API_KEY`: API key for embedding generation
- `OPENAI_BASE_URL`: Base URL for OpenAI API (defaults to OpenRouter)
- `DRY_RUN`: Set to `true` to run without making API calls
- `VECTORIZE_INDEX_NAME`: Name of the Vectorize index (defaults to `ezcontext`)

## Supported File Formats

### Structured Documentation Format

The seeder supports a structured format used in files like `seed/next.js.txt`:

```
TITLE: Feature Title
DESCRIPTION: Feature description
SOURCE: https://source-url.com
LANGUAGE: tsx
CODE:
```
code block here
```

----------------------------------------

TITLE: Another Feature
...
```

### Plain Text/Markdown

Regular text and markdown files are automatically chunked into smaller pieces with overlap for better search results.

## Configuration

- **Chunk Size**: 1000 characters per chunk
- **Chunk Overlap**: 200 characters between chunks
- **Batch Size**: 10 embeddings processed in parallel
- **Embedding Model**: `text-embedding-3-small` (1536 dimensions)

## Output

The script generates:
- Document chunks with metadata (library, title, description, etc.)
- 1536-dimensional embeddings for each chunk
- Progress logging during processing
- Summary statistics upon completion

## Integration with Vectorize

Currently, the script prepares data for insertion but doesn't directly insert into Vectorize. To complete the seeding process:

1. **Option 1**: Deploy as a Cloudflare Worker endpoint that can access Vectorize
2. **Option 2**: Use the Wrangler CLI with vectorize commands
3. **Option 3**: Extend the script to use the Vectorize REST API

## Adding New Documentation

1. Place documentation files in the `seed/` directory
2. Use supported formats (`.txt`, `.md`, `.json`)
3. For structured content, follow the format shown above
4. Run the seeding script to process new files

## Example Output

```
🚀 Starting Vector Database Seeding Process
📁 Reading files from: ./seed
🤖 Using embedding model: text-embedding-3-small
📏 Embedding dimensions: 1536
🔗 OpenAI Base URL: https://openrouter.ai/api/v1
🧪 Dry run mode: ENABLED

📚 Found 1 document files
📖 Processing: seed/next.js.txt
   ✅ Created 674 chunks

📊 Total chunks to process: 674

🧠 Generating embeddings...
   📈 Progress: 674/674 (100.0%)

💾 Inserting into Vectorize database...
✅ Seeding completed successfully!
```
