#!/usr/bin/env python3
"""
Test code file for the enhanced upload API with code category support.

This Python script demonstrates various programming concepts and will be used
to test the code category functionality of the upload API.
"""

import asyncio
import json
from typing import List, Dict, Optional
from dataclasses import dataclass


@dataclass
class EmbeddingConfig:
    """Configuration for embedding generation."""
    provider: str
    model: str
    dimensions: int


class VectorDatabase:
    """Simple vector database implementation for testing."""
    
    def __init__(self, config: EmbeddingConfig):
        self.config = config
        self.vectors: List[Dict] = []
    
    async def insert(self, vector: List[float], metadata: Dict) -> str:
        """Insert a vector with metadata into the database."""
        vector_id = f"vec_{len(self.vectors)}"
        self.vectors.append({
            "id": vector_id,
            "vector": vector,
            "metadata": metadata
        })
        return vector_id
    
    async def search(self, query_vector: List[float], limit: int = 5) -> List[Dict]:
        """Search for similar vectors."""
        # Simplified similarity search implementation
        results = []
        for item in self.vectors[:limit]:
            # Calculate cosine similarity (simplified)
            similarity = sum(a * b for a, b in zip(query_vector, item["vector"]))
            results.append({
                "id": item["id"],
                "score": similarity,
                "metadata": item["metadata"]
            })
        return sorted(results, key=lambda x: x["score"], reverse=True)


async def process_code_content(content: str, category: str = "code") -> Dict:
    """Process code content for vector storage."""
    config = EmbeddingConfig(
        provider="voyageai",
        model="voyage-code-2",
        dimensions=1024
    )
    
    db = VectorDatabase(config)
    
    # Simulate embedding generation
    embedding = [0.1] * config.dimensions
    
    metadata = {
        "content": content,
        "category": category,
        "content_type": "code",
        "language": "python"
    }
    
    vector_id = await db.insert(embedding, metadata)
    
    return {
        "vector_id": vector_id,
        "config": config,
        "metadata": metadata
    }


if __name__ == "__main__":
    # Test the code processing functionality
    sample_code = '''
    def fibonacci(n):
        if n <= 1:
            return n
        return fibonacci(n-1) + fibonacci(n-2)
    '''
    
    result = asyncio.run(process_code_content(sample_code))
    print(json.dumps(result, indent=2))
