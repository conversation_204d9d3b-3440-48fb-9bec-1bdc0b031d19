This is a documentation test file for the enhanced upload API.

The enhanced upload API now supports category-based processing with two main categories:
1. Documentation (docs) - for general documentation content
2. Code (code) - for source code and technical implementation content

When uploading documentation content, the system uses the default embedding configuration. This ensures that documentation is processed with models optimized for natural language understanding and semantic search.

Key features of the documentation category:
- Uses default embedding providers and models
- Optimized for natural language processing
- Suitable for user guides, API documentation, and explanatory content
- Supports multiple file formats including text, markdown, and JSON

The system automatically chunks the content into manageable pieces for vector storage and retrieval. Each chunk maintains metadata about its source, category, and processing details.

This test file will be used to verify that documentation content is properly categorized and processed through the enhanced upload pipeline.
