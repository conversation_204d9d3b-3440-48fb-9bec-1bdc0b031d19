/**
 * Billing Plans Configuration
 *
 * Defines subscription plans, pricing tiers, and feature limits
 * for the multi-tenant SaaS platform.
 */

import type { ISubscriptionPlanConfig, TSubscriptionPlan } from '@/types/billing';

// ============================================================================
// Plan Configurations
// ============================================================================

/**
 * Free plan configuration
 */
const freePlan: ISubscriptionPlanConfig = {
  id: 'free',
  name: 'Free',
  description: 'Perfect for getting started with basic vector operations',
  limits: {
    maxTeamMembers: 1,
    maxTeams: 1,
    maxProjects: 3,
    maxCollections: 5,
    maxDocuments: 1000,
    maxDocumentDimensions: 1024,
    maxSearchRequests: 1000, // per month
    maxApiKeys: 2,
    maxApiRequests: 500, // per month
    maxStorageGB: 1,
    maxFileSize: 10, // MB
    advancedAnalytics: false,
    prioritySupport: false,
    customIntegrations: false,
    ssoEnabled: false,
    auditLogs: false,
  },
  pricing: {
    monthly: {
      amount: 0,
      currency: 'USD',
    },
    yearly: {
      amount: 0,
      currency: 'USD',
    },
  },
  features: [
    '1,000 documents',
    '1,000 search requests/month',
    '500 API requests/month',
    '1GB storage',
    'Basic analytics',
    'Community support',
  ],
  trialDays: 0,
};

/**
 * Starter plan configuration
 */
const starterPlan: ISubscriptionPlanConfig = {
  id: 'starter',
  name: 'Starter',
  description: 'Ideal for small teams and growing projects',
  limits: {
    maxTeamMembers: 5,
    maxTeams: 2,
    maxProjects: 10,
    maxCollections: 25,
    maxDocuments: 10000,
    maxDocumentDimensions: 1024,
    maxSearchRequests: 10000, // per month
    maxApiKeys: 5,
    maxApiRequests: 50000, // per month
    maxStorageGB: 10,
    maxFileSize: 50, // MB
    advancedAnalytics: true,
    prioritySupport: false,
    customIntegrations: false,
    ssoEnabled: false,
    auditLogs: true,
  },
  pricing: {
    monthly: {
      amount: 2900, // $29.00
      currency: 'USD',
      paddlePlanId: 'starter_monthly',
      paypalPlanId: 'P-STARTER-MONTHLY',
    },
    yearly: {
      amount: 29000, // $290.00 (2 months free)
      currency: 'USD',
      paddlePlanId: 'starter_yearly',
      paypalPlanId: 'P-STARTER-YEARLY',
      discount: 17, // ~2 months free
    },
  },
  features: [
    '10,000 documents',
    '10,000 search requests/month',
    '50,000 API requests/month',
    '10GB storage',
    'Up to 5 team members',
    'Advanced analytics',
    'Audit logs',
    'Email support',
  ],
  trialDays: 14,
};

/**
 * Professional plan configuration
 */
const professionalPlan: ISubscriptionPlanConfig = {
  id: 'professional',
  name: 'Professional',
  description: 'Perfect for growing businesses and advanced use cases',
  limits: {
    maxTeamMembers: 25,
    maxTeams: 5,
    maxProjects: 50,
    maxCollections: 100,
    maxDocuments: 100000,
    maxDocumentDimensions: 1536,
    maxSearchRequests: 100000, // per month
    maxApiKeys: 20,
    maxApiRequests: 500000, // per month
    maxStorageGB: 100,
    maxFileSize: 200, // MB
    advancedAnalytics: true,
    prioritySupport: true,
    customIntegrations: true,
    ssoEnabled: true,
    auditLogs: true,
  },
  pricing: {
    monthly: {
      amount: 9900, // $99.00
      currency: 'USD',
      paddlePlanId: 'professional_monthly',
      paypalPlanId: 'P-PROFESSIONAL-MONTHLY',
    },
    yearly: {
      amount: 99000, // $990.00 (2 months free)
      currency: 'USD',
      paddlePlanId: 'professional_yearly',
      paypalPlanId: 'P-PROFESSIONAL-YEARLY',
      discount: 17, // ~2 months free
    },
  },
  features: [
    '100,000 documents',
    '100,000 search requests/month',
    '500,000 API requests/month',
    '100GB storage',
    'Up to 25 team members',
    'Advanced analytics & reporting',
    'Priority support',
    'SSO integration',
    'Custom integrations',
    'Audit logs',
  ],
  popular: true,
  trialDays: 14,
};

/**
 * Enterprise plan configuration
 */
const enterprisePlan: ISubscriptionPlanConfig = {
  id: 'enterprise',
  name: 'Enterprise',
  description: 'Unlimited scale for large organizations',
  limits: {
    maxTeamMembers: -1, // Unlimited
    maxTeams: -1, // Unlimited
    maxProjects: -1, // Unlimited
    maxCollections: -1, // Unlimited
    maxDocuments: -1, // Unlimited
    maxDocumentDimensions: 3072,
    maxSearchRequests: -1, // Unlimited
    maxApiKeys: -1, // Unlimited
    maxApiRequests: -1, // Unlimited
    maxStorageGB: -1, // Unlimited
    maxFileSize: 1000, // MB
    advancedAnalytics: true,
    prioritySupport: true,
    customIntegrations: true,
    ssoEnabled: true,
    auditLogs: true,
  },
  pricing: {
    monthly: {
      amount: 29900, // $299.00
      currency: 'USD',
      paddlePlanId: 'enterprise_monthly',
      paypalPlanId: 'P-ENTERPRISE-MONTHLY',
    },
    yearly: {
      amount: 299000, // $2,990.00 (2 months free)
      currency: 'USD',
      paddlePlanId: 'enterprise_yearly',
      paypalPlanId: 'P-ENTERPRISE-YEARLY',
      discount: 17, // ~2 months free
    },
  },
  features: [
    'Unlimited documents',
    'Unlimited search requests',
    'Unlimited API requests',
    'Unlimited storage',
    'Unlimited team members',
    'Advanced analytics & reporting',
    'Dedicated support',
    'SSO integration',
    'Custom integrations',
    'Audit logs',
    'SLA guarantee',
    'Custom contracts',
  ],
  trialDays: 30,
};

// ============================================================================
// Plan Registry
// ============================================================================

/**
 * All available subscription plans
 */
export const SUBSCRIPTION_PLANS: Record<TSubscriptionPlan, ISubscriptionPlanConfig> = {
  free: freePlan,
  starter: starterPlan,
  professional: professionalPlan,
  enterprise: enterprisePlan,
};

/**
 * Get plan configuration by ID
 */
export function getPlanConfig(planId: TSubscriptionPlan): ISubscriptionPlanConfig {
  const plan = SUBSCRIPTION_PLANS[planId];
  if (!plan) {
    throw new Error(`Unknown subscription plan: ${planId}`);
  }
  return plan;
}

/**
 * Get all available plans
 */
export function getAllPlans(): ISubscriptionPlanConfig[] {
  return Object.values(SUBSCRIPTION_PLANS);
}

/**
 * Get plans available for purchase (excluding free)
 */
export function getPaidPlans(): ISubscriptionPlanConfig[] {
  return getAllPlans().filter((plan) => plan.id !== 'free');
}

/**
 * Check if a plan allows unlimited usage for a specific limit
 */
export function isUnlimited(limit: number): boolean {
  return limit === -1;
}

/**
 * Get plan by provider-specific plan ID
 */
export function getPlanByProviderPlanId(
  providerPlanId: string,
  provider: 'paddle' | 'paypal'
): ISubscriptionPlanConfig | null {
  const providerKey = provider === 'paddle' ? 'paddlePlanId' : 'paypalPlanId';

  for (const plan of getAllPlans()) {
    if (
      plan.pricing.monthly[providerKey] === providerPlanId ||
      plan.pricing.yearly[providerKey] === providerPlanId
    ) {
      return plan;
    }
  }

  return null;
}

/**
 * Calculate yearly savings percentage
 */
export function getYearlySavings(planId: TSubscriptionPlan): number {
  const plan = getPlanConfig(planId);
  const monthlyTotal = plan.pricing.monthly.amount * 12;
  const yearlyAmount = plan.pricing.yearly.amount;

  if (monthlyTotal === 0 || yearlyAmount === 0) {
    return 0;
  }

  return Math.round(((monthlyTotal - yearlyAmount) / monthlyTotal) * 100);
}
