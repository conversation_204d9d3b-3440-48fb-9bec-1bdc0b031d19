/**
 * Document Database Configuration Utility
 *
 * Provides default configuration values and validation for document database operations.
 */

import { DocumentQueryConfigSchema, type IDocumentQueryConfig } from '@types';

/**
 * Default document database query configuration
 */
export const DEFAULT_DOCUMENT_CONFIG: Required<IDocumentQueryConfig> = {
  topK: 5,
  returnMetadata: true,
  returnValues: false,
  namespace: '',
};

/**
 * Maximum allowed values for document database queries
 * Based on Cloudflare Vectorize limitations
 */
export const MAX_DOCUMENT_CONFIG = {
  topK: 20, // Maximum allowed with returnMetadata: true
  topK_without_metadata: 1000, // Maximum allowed with returnMetadata: false
};

/**
 * Validates and merges document query configuration with defaults
 *
 * @param config - Partial configuration to merge with defaults
 * @returns Validated and complete configuration
 * @throws Error if configuration is invalid
 *
 * @example
 * ```typescript
 * const config = getDocumentQueryConfig({ topK: 10 });
 * console.log(config.topK); // 10
 * console.log(config.returnMetadata); // true (default)
 * ```
 */
export function getDocumentQueryConfig(
  config: Partial<IDocumentQueryConfig> = {}
): Required<IDocumentQueryConfig> {
  try {
    // Merge with defaults
    const mergedConfig = {
      ...DEFAULT_DOCUMENT_CONFIG,
      ...config,
    };

    // Validate using Zod schema
    const validatedConfig = DocumentQueryConfigSchema.parse(mergedConfig);

    // Additional validation for Cloudflare Vectorize limits
    if (validatedConfig.returnMetadata && validatedConfig.topK > MAX_DOCUMENT_CONFIG.topK) {
      throw new Error(
        `topK cannot exceed ${MAX_DOCUMENT_CONFIG.topK} when returnMetadata is true. Received: ${validatedConfig.topK}`
      );
    }

    if (
      !validatedConfig.returnMetadata &&
      validatedConfig.topK > MAX_DOCUMENT_CONFIG.topK_without_metadata
    ) {
      throw new Error(
        `topK cannot exceed ${MAX_DOCUMENT_CONFIG.topK_without_metadata} when returnMetadata is false. Received: ${validatedConfig.topK}`
      );
    }

    // Validate namespace if provided
    if (validatedConfig.namespace && validatedConfig.namespace.length > 100) {
      throw new Error(
        `namespace cannot exceed 100 characters. Received: ${validatedConfig.namespace.length} characters`
      );
    }

    return validatedConfig as Required<IDocumentQueryConfig>;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Document configuration validation failed: ${error.message}`);
    }
    throw new Error('Document configuration validation failed with unknown error');
  }
}

/**
 * Gets document query configuration from environment variables
 *
 * @returns Configuration object with environment-based defaults
 *
 * @example
 * ```typescript
 * // Set environment variables:
 * // DOCUMENT_DEFAULT_TOP_K=10
 * // DOCUMENT_DEFAULT_RETURN_METADATA=false
 *
 * const config = getDocumentConfigFromEnv();
 * console.log(config.topK); // 10
 * console.log(config.returnMetadata); // false
 * ```
 */
export function getDocumentConfigFromEnv(): Required<IDocumentQueryConfig> {
  const envConfig: Partial<IDocumentQueryConfig> = {};

  // Parse environment variables
  if (process.env.DOCUMENT_DEFAULT_TOP_K) {
    const topK = parseInt(process.env.DOCUMENT_DEFAULT_TOP_K, 10);
    if (!Number.isNaN(topK)) {
      envConfig.topK = topK;
    }
  }

  if (process.env.DOCUMENT_DEFAULT_RETURN_METADATA) {
    envConfig.returnMetadata =
      process.env.DOCUMENT_DEFAULT_RETURN_METADATA.toLowerCase() === 'true';
  }

  if (process.env.DOCUMENT_DEFAULT_RETURN_VALUES) {
    envConfig.returnValues = process.env.DOCUMENT_DEFAULT_RETURN_VALUES.toLowerCase() === 'true';
  }

  if (process.env.DOCUMENT_DEFAULT_NAMESPACE) {
    envConfig.namespace = process.env.DOCUMENT_DEFAULT_NAMESPACE;
  }

  return getDocumentQueryConfig(envConfig);
}

/**
 * Creates a document query configuration for search operations
 *
 * @param searchParams - Search parameters that may include document config
 * @param overrides - Additional configuration overrides
 * @returns Complete document query configuration
 *
 * @example
 * ```typescript
 * const searchParams = { query: "test", topK: 10 };
 * const config = createSearchDocumentConfig(searchParams, { returnValues: true });
 * console.log(config.topK); // 10
 * console.log(config.returnValues); // true
 * ```
 */
export function createSearchDocumentConfig(
  searchParams: { topK?: number; returnMetadata?: boolean; returnValues?: boolean; limit?: number },
  overrides: Partial<IDocumentQueryConfig> = {}
): Required<IDocumentQueryConfig> {
  try {
    const config: Partial<IDocumentQueryConfig> = {
      topK: searchParams.topK || searchParams.limit,
      returnMetadata: searchParams.returnMetadata,
      returnValues: searchParams.returnValues,
      ...overrides,
    };

    // Validate that we have a valid topK value
    if (config.topK !== undefined && config.topK <= 0) {
      throw new Error(`topK must be greater than 0. Received: ${config.topK}`);
    }

    return getDocumentQueryConfig(config);
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Search document configuration failed: ${error.message}`);
    }
    throw new Error('Search document configuration failed with unknown error');
  }
}
