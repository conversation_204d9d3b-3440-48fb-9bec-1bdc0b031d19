import * as schema from '@dbSchema';
import type { ICloudflareEnv } from '@types';
import { neon } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-http';
import { getEnv, getEnvSync } from './env';

/**
 * Get database instance with Drizzle ORM
 *
 * Configured for Cloudflare Workers with Neon serverless driver.
 * Includes full schema for type-safe database operations.
 *
 * @returns Promise resolving to configured Drizzle database instance
 * @throws Error if DATABASE_URL is not configured
 *
 * @example
 * ```typescript
 * const db = await getDB();
 * const users = await db.select().from(schema.users);
 * ```
 */
const getDB = async () => {
  const env = await getEnv();

  if (!env.DATABASE_URL) {
    throw new Error('DATABASE_URL environment variable is not configured');
  }

  // Create Neon serverless client
  const sql = neon(env.DATABASE_URL);

  // Initialize Drizzle with schema and client
  const db = drizzle({ client: sql, schema });

  return db;
};

/**
 * Get database instance synchronously with provided context
 * Safe to use during module initialization as it doesn't use async operations
 *
 * @param c - Hono context containing environment variables
 * @returns Configured Drizzle database instance
 * @throws Error if DATABASE_URL is not configured
 */
export const getDBSync = (c: { env: ICloudflareEnv }) => {
  const env = getEnvSync(c);

  if (!env.DATABASE_URL) {
    throw new Error('DATABASE_URL environment variable is not configured');
  }

  // Create Neon serverless client
  const sql = neon(env.DATABASE_URL);

  // Initialize Drizzle with schema and client
  const db = drizzle({ client: sql, schema });

  return db;
};

export default getDB;
