/**
 * Permission Constants and Types
 *
 * Comprehensive permission system for RBAC implementation.
 * Defines all available permissions, categories, and role-based defaults.
 */

import type { TTeamRole } from '@/types/teams';

// ============================================================================
// Permission Categories
// ============================================================================

/**
 * Available permission categories
 */
export const PERMISSION_CATEGORIES = {
  TEAM: 'team',
  PROJECT: 'project',
  BILLING: 'billing',
  VECTORS: 'vectors',
  ANALYTICS: 'analytics',
  API_KEYS: 'api_keys',
} as const;

/**
 * Available permission actions
 */
export const PERMISSION_ACTIONS = {
  READ: 'read',
  MANAGE: 'manage',
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
} as const;

// ============================================================================
// Permission Constants
// ============================================================================

/**
 * Team permissions
 */
export const TEAM_PERMISSIONS = {
  READ_TEAM: 'read.team',
  MANAGE_TEAM: 'manage.team',
  CREATE_TEAM: 'create.team',
  UPDATE_TEAM: 'update.team',
  DELETE_TEAM: 'delete.team',
} as const;

/**
 * Project permissions
 */
export const PROJECT_PERMISSIONS = {
  READ_PROJECT: 'read.project',
  MANAGE_PROJECT: 'manage.project',
  CREATE_PROJECT: 'create.project',
  UPDATE_PROJECT: 'update.project',
  DELETE_PROJECT: 'delete.project',
} as const;

/**
 * Billing permissions
 */
export const BILLING_PERMISSIONS = {
  READ_BILLING: 'read.billing',
  MANAGE_BILLING: 'manage.billing',
  CREATE_BILLING: 'create.billing',
  UPDATE_BILLING: 'update.billing',
  DELETE_BILLING: 'delete.billing',
} as const;

/**
 * Vector permissions
 */
export const VECTOR_PERMISSIONS = {
  READ_VECTORS: 'read.vectors',
  MANAGE_VECTORS: 'manage.vectors',
  CREATE_VECTORS: 'create.vectors',
  UPDATE_VECTORS: 'update.vectors',
  DELETE_VECTORS: 'delete.vectors',
} as const;

/**
 * Analytics permissions
 */
export const ANALYTICS_PERMISSIONS = {
  READ_ANALYTICS: 'read.analytics',
  MANAGE_ANALYTICS: 'manage.analytics',
  CREATE_ANALYTICS: 'create.analytics',
  UPDATE_ANALYTICS: 'update.analytics',
  DELETE_ANALYTICS: 'delete.analytics',
} as const;

/**
 * API Key permissions
 */
export const API_KEY_PERMISSIONS = {
  READ_API_KEYS: 'read.api_keys',
  MANAGE_API_KEYS: 'manage.api_keys',
  CREATE_API_KEYS: 'create.api_keys',
  UPDATE_API_KEYS: 'update.api_keys',
  DELETE_API_KEYS: 'delete.api_keys',
} as const;

/**
 * All available permissions
 */
export const ALL_PERMISSIONS = {
  ...TEAM_PERMISSIONS,
  ...PROJECT_PERMISSIONS,
  ...BILLING_PERMISSIONS,
  ...VECTOR_PERMISSIONS,
  ...ANALYTICS_PERMISSIONS,
  ...API_KEY_PERMISSIONS,
} as const;

// ============================================================================
// Permission Types
// ============================================================================

/**
 * Permission string type
 */
export type TPermission = (typeof ALL_PERMISSIONS)[keyof typeof ALL_PERMISSIONS];

/**
 * Permission category type
 */
export type TPermissionCategory =
  (typeof PERMISSION_CATEGORIES)[keyof typeof PERMISSION_CATEGORIES];

/**
 * Permission action type
 */
export type TPermissionAction = (typeof PERMISSION_ACTIONS)[keyof typeof PERMISSION_ACTIONS];

/**
 * Permission structure
 */
export interface IPermissionStructure {
  action: TPermissionAction;
  category: TPermissionCategory;
  permission: TPermission;
}

/**
 * Permission check context
 */
export interface IPermissionContext {
  userId: string;
  teamId?: string;
  projectId?: string;
  resourceId?: string;
  resourceType?: string;
}

/**
 * Permission check result
 */
export interface IPermissionCheckResult {
  allowed: boolean;
  reason?: string;
  requiredPermission?: TPermission;
  requiredPermissions?: TPermission[];
  context?: IPermissionContext;
}

// ============================================================================
// Role-Based Default Permissions
// ============================================================================

/**
 * Default permissions for each role
 */
export const ROLE_DEFAULT_PERMISSIONS: Record<TTeamRole, TPermission[]> = {
  owner: [
    // Team permissions - full access
    ALL_PERMISSIONS.READ_TEAM,
    ALL_PERMISSIONS.MANAGE_TEAM,
    ALL_PERMISSIONS.CREATE_TEAM,
    ALL_PERMISSIONS.UPDATE_TEAM,
    ALL_PERMISSIONS.DELETE_TEAM,

    // Project permissions - full access
    ALL_PERMISSIONS.READ_PROJECT,
    ALL_PERMISSIONS.MANAGE_PROJECT,
    ALL_PERMISSIONS.CREATE_PROJECT,
    ALL_PERMISSIONS.UPDATE_PROJECT,
    ALL_PERMISSIONS.DELETE_PROJECT,

    // Billing permissions - full access
    ALL_PERMISSIONS.READ_BILLING,
    ALL_PERMISSIONS.MANAGE_BILLING,
    ALL_PERMISSIONS.CREATE_BILLING,
    ALL_PERMISSIONS.UPDATE_BILLING,
    ALL_PERMISSIONS.DELETE_BILLING,

    // Vector permissions - full access
    ALL_PERMISSIONS.READ_VECTORS,
    ALL_PERMISSIONS.MANAGE_VECTORS,
    ALL_PERMISSIONS.CREATE_VECTORS,
    ALL_PERMISSIONS.UPDATE_VECTORS,
    ALL_PERMISSIONS.DELETE_VECTORS,

    // Analytics permissions - full access
    ALL_PERMISSIONS.READ_ANALYTICS,
    ALL_PERMISSIONS.MANAGE_ANALYTICS,
    ALL_PERMISSIONS.CREATE_ANALYTICS,
    ALL_PERMISSIONS.UPDATE_ANALYTICS,
    ALL_PERMISSIONS.DELETE_ANALYTICS,

    // API Key permissions - full access
    ALL_PERMISSIONS.READ_API_KEYS,
    ALL_PERMISSIONS.MANAGE_API_KEYS,
    ALL_PERMISSIONS.CREATE_API_KEYS,
    ALL_PERMISSIONS.UPDATE_API_KEYS,
    ALL_PERMISSIONS.DELETE_API_KEYS,
  ],

  admin: [
    // Team permissions - read and manage
    ALL_PERMISSIONS.READ_TEAM,
    ALL_PERMISSIONS.MANAGE_TEAM,
    ALL_PERMISSIONS.UPDATE_TEAM,

    // Project permissions - full access
    ALL_PERMISSIONS.READ_PROJECT,
    ALL_PERMISSIONS.MANAGE_PROJECT,
    ALL_PERMISSIONS.CREATE_PROJECT,
    ALL_PERMISSIONS.UPDATE_PROJECT,
    ALL_PERMISSIONS.DELETE_PROJECT,

    // Billing permissions - read only
    ALL_PERMISSIONS.READ_BILLING,

    // Vector permissions - full access
    ALL_PERMISSIONS.READ_VECTORS,
    ALL_PERMISSIONS.MANAGE_VECTORS,
    ALL_PERMISSIONS.CREATE_VECTORS,
    ALL_PERMISSIONS.UPDATE_VECTORS,
    ALL_PERMISSIONS.DELETE_VECTORS,

    // Analytics permissions - read and manage
    ALL_PERMISSIONS.READ_ANALYTICS,
    ALL_PERMISSIONS.MANAGE_ANALYTICS,

    // API Key permissions - full access
    ALL_PERMISSIONS.READ_API_KEYS,
    ALL_PERMISSIONS.MANAGE_API_KEYS,
    ALL_PERMISSIONS.CREATE_API_KEYS,
    ALL_PERMISSIONS.UPDATE_API_KEYS,
    ALL_PERMISSIONS.DELETE_API_KEYS,
  ],

  manager: [
    // Team permissions - read only
    ALL_PERMISSIONS.READ_TEAM,

    // Project permissions - full access
    ALL_PERMISSIONS.READ_PROJECT,
    ALL_PERMISSIONS.MANAGE_PROJECT,
    ALL_PERMISSIONS.CREATE_PROJECT,
    ALL_PERMISSIONS.UPDATE_PROJECT,
    ALL_PERMISSIONS.DELETE_PROJECT,

    // Vector permissions - full access
    ALL_PERMISSIONS.READ_VECTORS,
    ALL_PERMISSIONS.MANAGE_VECTORS,
    ALL_PERMISSIONS.CREATE_VECTORS,
    ALL_PERMISSIONS.UPDATE_VECTORS,
    ALL_PERMISSIONS.DELETE_VECTORS,

    // Analytics permissions - read only
    ALL_PERMISSIONS.READ_ANALYTICS,

    // API Key permissions - read and create
    ALL_PERMISSIONS.READ_API_KEYS,
    ALL_PERMISSIONS.CREATE_API_KEYS,
    ALL_PERMISSIONS.UPDATE_API_KEYS,
  ],

  member: [
    // Team permissions - read only
    ALL_PERMISSIONS.READ_TEAM,

    // Project permissions - read and manage
    ALL_PERMISSIONS.READ_PROJECT,
    ALL_PERMISSIONS.MANAGE_PROJECT,
    ALL_PERMISSIONS.UPDATE_PROJECT,

    // Vector permissions - read and manage
    ALL_PERMISSIONS.READ_VECTORS,
    ALL_PERMISSIONS.MANAGE_VECTORS,
    ALL_PERMISSIONS.CREATE_VECTORS,
    ALL_PERMISSIONS.UPDATE_VECTORS,

    // API Key permissions - read only
    ALL_PERMISSIONS.READ_API_KEYS,
  ],

  viewer: [
    // Team permissions - read only
    ALL_PERMISSIONS.READ_TEAM,

    // Project permissions - read only
    ALL_PERMISSIONS.READ_PROJECT,

    // Vector permissions - read only
    ALL_PERMISSIONS.READ_VECTORS,
  ],
} as const;

// ============================================================================
// Permission Utility Functions
// ============================================================================

/**
 * Parse permission string into action and category
 *
 * @param permission - Permission string (e.g., 'read.team')
 * @returns Object with action and category
 */
export function parsePermission(permission: TPermission): IPermissionStructure {
  const [action, category] = permission.split('.') as [TPermissionAction, TPermissionCategory];
  return { action, category, permission };
}

/**
 * Create permission string from action and category
 *
 * @param action - Permission action
 * @param category - Permission category
 * @returns Permission string
 */
export function createPermission(
  action: TPermissionAction,
  category: TPermissionCategory
): TPermission {
  return `${action}.${category}` as TPermission;
}

/**
 * Get all permissions for a specific category
 *
 * @param category - Permission category
 * @returns Array of permissions for the category
 */
export function getPermissionsByCategory(category: TPermissionCategory): TPermission[] {
  return Object.values(ALL_PERMISSIONS).filter((permission) => {
    const { category: permCategory } = parsePermission(permission);
    return permCategory === category;
  });
}

/**
 * Get all permissions for a specific action
 *
 * @param action - Permission action
 * @returns Array of permissions for the action
 */
export function getPermissionsByAction(action: TPermissionAction): TPermission[] {
  return Object.values(ALL_PERMISSIONS).filter((permission) => {
    const { action: permAction } = parsePermission(permission);
    return permAction === action;
  });
}

/**
 * Check if a permission exists
 *
 * @param permission - Permission to check
 * @returns True if permission exists
 */
export function isValidPermission(permission: string): permission is TPermission {
  return Object.values(ALL_PERMISSIONS).includes(permission as TPermission);
}

/**
 * Get effective permissions for a role, including custom permissions
 *
 * @param role - Team role
 * @param customPermissions - Additional custom permissions
 * @returns Array of effective permissions
 */
export function getEffectivePermissions(
  role: TTeamRole,
  customPermissions: TPermission[] = []
): TPermission[] {
  const defaultPermissions = ROLE_DEFAULT_PERMISSIONS[role];
  const allPermissions = [...defaultPermissions, ...customPermissions];

  // Remove duplicates
  return Array.from(new Set(allPermissions));
}

/**
 * Check if a role has a specific permission by default
 *
 * @param role - Team role
 * @param permission - Permission to check
 * @returns True if role has permission by default
 */
export function roleHasPermission(role: TTeamRole, permission: TPermission): boolean {
  return ROLE_DEFAULT_PERMISSIONS[role].includes(permission);
}

/**
 * Get role hierarchy level (higher number = more permissions)
 *
 * @param role - Team role
 * @returns Hierarchy level
 */
export function getRoleHierarchyLevel(role: TTeamRole): number {
  const hierarchy: Record<TTeamRole, number> = {
    viewer: 1,
    member: 2,
    manager: 3,
    admin: 4,
    owner: 5,
  };

  return hierarchy[role];
}

/**
 * Check if one role is higher than another in hierarchy
 *
 * @param role1 - First role
 * @param role2 - Second role
 * @returns True if role1 is higher than role2
 */
export function isRoleHigher(role1: TTeamRole, role2: TTeamRole): boolean {
  return getRoleHierarchyLevel(role1) > getRoleHierarchyLevel(role2);
}

/**
 * Get minimum role required for a permission
 *
 * @param permission - Permission to check
 * @returns Minimum role required, or null if no role has this permission
 */
export function getMinimumRoleForPermission(permission: TPermission): TTeamRole | null {
  const roles: TTeamRole[] = ['viewer', 'member', 'manager', 'admin', 'owner'];

  for (const role of roles) {
    if (roleHasPermission(role, permission)) {
      return role;
    }
  }

  return null;
}

// ============================================================================
// Permission Validation
// ============================================================================

/**
 * Validate permissions array
 *
 * @param permissions - Array of permissions to validate
 * @returns Object with valid permissions and invalid ones
 */
export function validatePermissions(permissions: string[]): {
  valid: TPermission[];
  invalid: string[];
} {
  const valid: TPermission[] = [];
  const invalid: string[] = [];

  for (const permission of permissions) {
    if (isValidPermission(permission)) {
      valid.push(permission);
    } else {
      invalid.push(permission);
    }
  }

  return { valid, invalid };
}

/**
 * Get all available permissions as array
 *
 * @returns Array of all available permissions
 */
export function getAllPermissions(): TPermission[] {
  return Object.values(ALL_PERMISSIONS);
}

/**
 * Get all available categories as array
 *
 * @returns Array of all available categories
 */
export function getAllCategories(): TPermissionCategory[] {
  return Object.values(PERMISSION_CATEGORIES);
}

/**
 * Get all available actions as array
 *
 * @returns Array of all available actions
 */
export function getAllActions(): TPermissionAction[] {
  return Object.values(PERMISSION_ACTIONS);
}

// ============================================================================
// Role Configuration Functions
// ============================================================================

/**
 * Validate role permissions configuration
 *
 * @param rolePermissions - Role permissions to validate
 * @returns Validation result with errors and warnings
 */
export function validateRolePermissions(rolePermissions: Record<TTeamRole, TPermission[]>): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  const roles: TTeamRole[] = ['viewer', 'member', 'manager', 'admin', 'owner'];

  // Check that all roles are defined
  for (const role of roles) {
    if (!rolePermissions[role]) {
      errors.push(`Missing permissions for role: ${role}`);
      continue;
    }

    // Validate each permission
    const { invalid } = validatePermissions(rolePermissions[role]);
    if (invalid.length > 0) {
      errors.push(`Invalid permissions for role ${role}: ${invalid.join(', ')}`);
    }

    // Check role hierarchy (higher roles should have at least the permissions of lower roles)
    if (role !== 'viewer') {
      const lowerRoleIndex = roles.indexOf(role) - 1;
      if (lowerRoleIndex >= 0) {
        const lowerRole = roles[lowerRoleIndex];
        const lowerRolePermissions = rolePermissions[lowerRole] || [];
        const currentRolePermissions = rolePermissions[role];

        const missingPermissions = lowerRolePermissions.filter(
          (permission) => !currentRolePermissions.includes(permission)
        );

        if (missingPermissions.length > 0) {
          warnings.push(
            `Role ${role} is missing permissions from lower role ${lowerRole}: ${missingPermissions.join(', ')}`
          );
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Get role permissions matrix for display
 *
 * @returns Matrix showing which permissions each role has
 */
export function getRolePermissionsMatrix(): Record<
  TTeamRole,
  Record<TPermissionCategory, TPermissionAction[]>
> {
  const matrix = {} as Record<TTeamRole, Record<TPermissionCategory, TPermissionAction[]>>;

  const roles: TTeamRole[] = ['viewer', 'member', 'manager', 'admin', 'owner'];
  const categories = getAllCategories();

  for (const role of roles) {
    matrix[role] = {} as Record<TPermissionCategory, TPermissionAction[]>;
    const rolePermissions = ROLE_DEFAULT_PERMISSIONS[role];

    for (const category of categories) {
      matrix[role][category] = [];

      // Find all actions this role has for this category
      for (const permission of rolePermissions) {
        const { action, category: permCategory } = parsePermission(permission);
        if (permCategory === category) {
          matrix[role][category].push(action);
        }
      }
    }
  }

  return matrix;
}

/**
 * Check if role configuration follows best practices
 *
 * @returns Array of recommendations for role configuration
 */
export function getRoleConfigurationRecommendations(): string[] {
  const recommendations: string[] = [];
  const validation = validateRolePermissions(ROLE_DEFAULT_PERMISSIONS);

  // Add validation warnings as recommendations
  recommendations.push(...validation.warnings);

  // Check for security best practices
  const ownerPermissions = ROLE_DEFAULT_PERMISSIONS.owner;
  const adminPermissions = ROLE_DEFAULT_PERMISSIONS.admin;

  // Owner should have all permissions
  const allPermissions = getAllPermissions();
  const missingOwnerPermissions = allPermissions.filter((p) => !ownerPermissions.includes(p));
  if (missingOwnerPermissions.length > 0) {
    recommendations.push(
      `Owner role should have all permissions. Missing: ${missingOwnerPermissions.join(', ')}`
    );
  }

  // Admin should not have delete team permission
  if (adminPermissions.includes('delete.team')) {
    recommendations.push('Admin role should not have delete.team permission for security');
  }

  // Viewer should only have read permissions
  const viewerPermissions = ROLE_DEFAULT_PERMISSIONS.viewer;
  const nonReadPermissions = viewerPermissions.filter((p) => !p.startsWith('read.'));
  if (nonReadPermissions.length > 0) {
    recommendations.push(
      `Viewer role should only have read permissions. Found: ${nonReadPermissions.join(', ')}`
    );
  }

  return recommendations;
}

// ============================================================================
// Permission Inheritance Functions
// ============================================================================

/**
 * Check if a permission can be inherited from a higher role
 *
 * @param currentRole - Current user role
 * @param permission - Permission to check
 * @param targetRole - Target role to inherit from (optional)
 * @returns Inheritance result
 */
export function canInheritPermission(
  currentRole: TTeamRole,
  permission: TPermission,
  targetRole?: TTeamRole
): {
  canInherit: boolean;
  inheritedFromRole?: TTeamRole;
  reason?: string;
} {
  const currentRoleLevel = getRoleHierarchyLevel(currentRole);

  // If target role is specified, check if current role can inherit from it
  if (targetRole) {
    const targetRoleLevel = getRoleHierarchyLevel(targetRole);

    if (currentRoleLevel < targetRoleLevel) {
      return {
        canInherit: false,
        reason: `Role ${currentRole} cannot inherit from higher role ${targetRole}`,
      };
    }

    const targetRolePermissions = ROLE_DEFAULT_PERMISSIONS[targetRole];
    if (targetRolePermissions.includes(permission)) {
      return {
        canInherit: true,
        inheritedFromRole: targetRole,
      };
    }

    return {
      canInherit: false,
      reason: `Target role ${targetRole} does not have permission ${permission}`,
    };
  }

  // Check inheritance from all lower roles
  const roles: TTeamRole[] = ['viewer', 'member', 'manager', 'admin', 'owner'];
  const currentRoleIndex = roles.indexOf(currentRole);

  // Check all roles at or below current role level
  for (let i = 0; i <= currentRoleIndex; i++) {
    const role = roles[i];
    const rolePermissions = ROLE_DEFAULT_PERMISSIONS[role];

    if (rolePermissions.includes(permission)) {
      return {
        canInherit: true,
        inheritedFromRole: role,
      };
    }
  }

  return {
    canInherit: false,
    reason: `Permission ${permission} not available in role hierarchy for ${currentRole}`,
  };
}

/**
 * Get all permissions a role can inherit
 *
 * @param role - Role to check inheritance for
 * @returns Array of permissions that can be inherited
 */
export function getInheritablePermissions(role: TTeamRole): {
  direct: TPermission[];
  inherited: TPermission[];
  all: TPermission[];
} {
  const directPermissions = ROLE_DEFAULT_PERMISSIONS[role];
  const roles: TTeamRole[] = ['viewer', 'member', 'manager', 'admin', 'owner'];
  const roleIndex = roles.indexOf(role);

  const inheritedPermissions: TPermission[] = [];

  // Collect permissions from all lower roles
  for (let i = 0; i < roleIndex; i++) {
    const lowerRole = roles[i];
    const lowerRolePermissions = ROLE_DEFAULT_PERMISSIONS[lowerRole];

    for (const permission of lowerRolePermissions) {
      if (!directPermissions.includes(permission) && !inheritedPermissions.includes(permission)) {
        inheritedPermissions.push(permission);
      }
    }
  }

  const allPermissions = [...directPermissions, ...inheritedPermissions];

  return {
    direct: directPermissions,
    inherited: inheritedPermissions,
    all: allPermissions,
  };
}

/**
 * Resolve permission inheritance chain
 *
 * @param role - Role to resolve inheritance for
 * @param permission - Permission to trace
 * @returns Inheritance chain showing where permission comes from
 */
export function resolvePermissionInheritance(
  role: TTeamRole,
  permission: TPermission
): {
  hasPermission: boolean;
  source: 'direct' | 'inherited' | 'none';
  sourceRole?: TTeamRole;
  inheritanceChain: TTeamRole[];
} {
  const roles: TTeamRole[] = ['viewer', 'member', 'manager', 'admin', 'owner'];
  const roleIndex = roles.indexOf(role);
  const inheritanceChain: TTeamRole[] = [];

  // Check direct permission first
  const directPermissions = ROLE_DEFAULT_PERMISSIONS[role];
  if (directPermissions.includes(permission)) {
    return {
      hasPermission: true,
      source: 'direct',
      sourceRole: role,
      inheritanceChain: [role],
    };
  }

  // Check inheritance from lower roles
  for (let i = 0; i < roleIndex; i++) {
    const lowerRole = roles[i];
    const lowerRolePermissions = ROLE_DEFAULT_PERMISSIONS[lowerRole];
    inheritanceChain.push(lowerRole);

    if (lowerRolePermissions.includes(permission)) {
      return {
        hasPermission: true,
        source: 'inherited',
        sourceRole: lowerRole,
        inheritanceChain: [...inheritanceChain, role],
      };
    }
  }

  return {
    hasPermission: false,
    source: 'none',
    inheritanceChain: [],
  };
}

/**
 * Validate permission inheritance consistency
 *
 * @returns Validation result with any inheritance issues
 */
export function validatePermissionInheritance(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const roles: TTeamRole[] = ['viewer', 'member', 'manager', 'admin', 'owner'];

  // Check that higher roles include all permissions from lower roles
  for (let i = 1; i < roles.length; i++) {
    const higherRole = roles[i];
    const higherRolePermissions = ROLE_DEFAULT_PERMISSIONS[higherRole];

    for (let j = 0; j < i; j++) {
      const lowerRole = roles[j];
      const lowerRolePermissions = ROLE_DEFAULT_PERMISSIONS[lowerRole];

      for (const permission of lowerRolePermissions) {
        if (!higherRolePermissions.includes(permission)) {
          // Critical permissions should always be inherited
          if (['read.team', 'read.project', 'read.vectors'].includes(permission)) {
            errors.push(
              `Critical permission ${permission} from ${lowerRole} not inherited by ${higherRole}`
            );
          } else {
            warnings.push(
              `Permission ${permission} from ${lowerRole} not inherited by ${higherRole}`
            );
          }
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
