/**
 * Permission Utilities
 *
 * Utility functions for team permission checking, role validation,
 * and access control operations.
 */

import type { IPermissionCheckResult, ITeamContext, TPermission, TTeamRole } from '@/types/teams';
import { ROLE_DEFAULT_PERMISSIONS } from './permission-constants';

// ============================================================================
// Permission Checking Functions
// ============================================================================

/**
 * Check if a user has a specific permission
 *
 * @param context - Team context with user role and permissions
 * @param requiredPermission - Permission to check
 * @returns Permission check result
 */
export function hasPermission(
  context: ITeamContext,
  requiredPermission: TPermission
): IPermissionCheckResult {
  // Check if user has the specific permission
  if (context.permissions.includes(requiredPermission)) {
    return { allowed: true };
  }

  return {
    allowed: false,
    reason: `Missing required permission: ${requiredPermission}`,
    requiredPermission,
  };
}

/**
 * Check if a user has any of the specified permissions
 *
 * @param context - Team context with user role and permissions
 * @param requiredPermissions - Array of permissions to check (OR logic)
 * @returns Permission check result
 */
export function hasAnyPermission(
  context: ITeamContext,
  requiredPermissions: TPermission[]
): IPermissionCheckResult {
  for (const permission of requiredPermissions) {
    if (context.permissions.includes(permission)) {
      return { allowed: true };
    }
  }

  return {
    allowed: false,
    reason: `Missing any of required permissions: ${requiredPermissions.join(', ')}`,
    requiredPermission: requiredPermissions[0],
  };
}

/**
 * Check if a user has all of the specified permissions
 *
 * @param context - Team context with user role and permissions
 * @param requiredPermissions - Array of permissions to check (AND logic)
 * @returns Permission check result
 */
export function hasAllPermissions(
  context: ITeamContext,
  requiredPermissions: TPermission[]
): IPermissionCheckResult {
  const missingPermissions = requiredPermissions.filter(
    (permission) => !context.permissions.includes(permission)
  );

  if (missingPermissions.length === 0) {
    return { allowed: true };
  }

  return {
    allowed: false,
    reason: `Missing required permissions: ${missingPermissions.join(', ')}`,
    requiredPermission: missingPermissions[0],
  };
}

// ============================================================================
// Role Checking Functions
// ============================================================================

/**
 * Check if a user has a minimum role level
 *
 * @param userRole - User's current role
 * @param minimumRole - Minimum required role
 * @returns True if user has sufficient role level
 */
export function hasMinimumRole(userRole: TTeamRole, minimumRole: TTeamRole): boolean {
  const roleHierarchy: Record<TTeamRole, number> = {
    owner: 5,
    admin: 4,
    manager: 3,
    member: 2,
    viewer: 1,
  };

  return roleHierarchy[userRole] >= roleHierarchy[minimumRole];
}

/**
 * Check if a user can manage another user based on role hierarchy
 *
 * @param managerRole - Role of the user attempting to manage
 * @param targetRole - Role of the user being managed
 * @returns True if manager can manage target user
 */
export function canManageUser(managerRole: TTeamRole, targetRole: TTeamRole): boolean {
  const roleHierarchy: Record<TTeamRole, number> = {
    owner: 5,
    admin: 4,
    manager: 3,
    member: 2,
    viewer: 1,
  };

  // Users can only manage users with lower role levels
  return roleHierarchy[managerRole] > roleHierarchy[targetRole];
}

// ============================================================================
// Permission Resolution Functions
// ============================================================================

/**
 * Get effective permissions for a role, including custom permissions
 *
 * @param role - Team role
 * @param customPermissions - Additional custom permissions
 * @returns Array of effective permissions
 */
export function getEffectivePermissions(
  role: TTeamRole,
  customPermissions: TPermission[] = []
): TPermission[] {
  // Use the comprehensive permission system from permission-constants
  const rolePermissions = ROLE_DEFAULT_PERMISSIONS[role] || [];

  // Combine role permissions with custom permissions, removing duplicates
  const allPermissions = [...new Set([...rolePermissions, ...customPermissions])];

  return allPermissions;
}

/**
 * Validate that custom permissions don't exceed role capabilities
 *
 * @param role - Team role
 * @param customPermissions - Custom permissions to validate
 * @returns Array of valid permissions (filtered)
 */
export function validateCustomPermissions(
  role: TTeamRole,
  customPermissions: TPermission[]
): TPermission[] {
  const maxPermissions = getEffectivePermissions('owner'); // Get all possible permissions
  const _rolePermissions = getEffectivePermissions(role);

  // Only allow custom permissions that are within the role's maximum scope
  // For now, we'll be permissive and allow any permission, but this could be restricted
  return customPermissions.filter((permission) => maxPermissions.includes(permission));
}

// ============================================================================
// Team Context Utilities
// ============================================================================

/**
 * Create team context from team member data
 *
 * @param teamId - Team ID
 * @param userId - User ID
 * @param role - User's role in the team
 * @param customPermissions - Custom permissions (optional)
 * @returns Team context object
 */
export function createTeamContext(
  teamId: string,
  userId: string,
  role: TTeamRole,
  customPermissions: TPermission[] = []
): ITeamContext {
  const effectivePermissions = getEffectivePermissions(role, customPermissions);

  return {
    teamId,
    userId,
    role,
    permissions: effectivePermissions,
  };
}

/**
 * Check if a team context is valid
 *
 * @param context - Team context to validate
 * @returns True if context is valid
 */
export function isValidTeamContext(context: ITeamContext): boolean {
  return !!(context.teamId && context.userId && context.role && Array.isArray(context.permissions));
}

// ============================================================================
// Permission String Utilities
// ============================================================================

/**
 * Parse permission string into action and category
 *
 * @param permission - Permission string (e.g., 'read.team')
 * @returns Object with action and category
 */
export function parsePermission(permission: TPermission): { action: string; category: string } {
  const [action, category] = permission.split('.');
  return { action, category };
}

/**
 * Create permission string from action and category
 *
 * @param action - Permission action
 * @param category - Permission category
 * @returns Permission string
 */
export function createPermission(action: string, category: string): string {
  return `${action}.${category}`;
}

/**
 * Get all permissions for a specific category
 *
 * @param category - Permission category
 * @param allPermissions - Array of all permissions to filter
 * @returns Array of permissions for the category
 */
export function getPermissionsByCategory(
  category: string,
  allPermissions: TPermission[]
): TPermission[] {
  return allPermissions.filter((permission) => {
    const { category: permCategory } = parsePermission(permission);
    return permCategory === category;
  });
}
