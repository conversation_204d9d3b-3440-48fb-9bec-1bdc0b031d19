// Re-export core utilities from dedicated files to avoid service import cascade

// Export services for backward compatibility - TEMPORARILY DISABLED TO AVOID GLOBAL SCOPE ASYNC OPERATIONS
// export {
//   ChunkingService,
//   CodeDetectionService,
//   chunkingService,
//   codeDetectionService,
//   EmbeddingService,
//   embeddingService,
// } from '@services';
// Export types
export type {
  IBaseChunkMetadata,
  IChunkingConfig,
  ICodeDetectionResult,
  IEmbeddingConfig,
  IEmbeddingResult,
  ITextChunk,
  TEmbeddingProvider,
} from '@types';
export {
  getAi,
  getBrowser,
  getDatabase,
  getDB,
  getKV,
  getOpenAI,
  getR2,
  getVDB,
  getVoyageAI,
} from './database';
// Export document configuration utilities
export * from './document-config';
export { getConfig, getCtx, getEnv, type IAppConfig } from './env';
