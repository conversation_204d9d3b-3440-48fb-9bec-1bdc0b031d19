import type { IMCPParams, IMCPRequest, IMCPResponse, TEmbeddingProvider } from '@types';
import { createSearchDocumentConfig, getDocumentQueryConfig, getEnv, getKV, getVDB } from '@utils';
import type { Context } from 'hono';
import { ZodError, z } from 'zod';

// Cloudflare Workers global types
declare const WebSocketPair: {
  new (): { 0: WebSocket; 1: WebSocket };
};

// Using imported MCP Protocol types from @types

// Tool-specific interfaces
interface IToolCallParams extends IMCPParams {
  name: string;
  arguments: unknown;
}

// Tool schemas
const SearchToolSchema = z.object({
  query: z.string().min(1, 'Query cannot be empty'),
  collection: z.string().optional(),
  limit: z.number().min(1).max(20).default(5),
  embedding_provider: z.enum(['openai', 'voyageai', 'cloudflare']).optional(),
  embedding_model: z.string().optional(),
});

const CollectionInfoToolSchema = z.object({
  collection_name: z.string().min(1, 'Collection name cannot be empty'),
  include_versions: z.boolean().default(false),
});

/**
 * MCP (Model Context Protocol) WebSocket Handler
 *
 * Handles WebSocket connections and implements the MCP protocol for
 * documentation search and collection information retrieval.
 *
 * @class MCPWebSocketHandler
 *
 * @example
 * ```typescript
 * const handler = new MCPWebSocketHandler();
 * await handler.handleConnection(websocket);
 * ```
 */
export class MCPWebSocketHandler {
  public connections: Set<WebSocket> = new Set();

  public async handleMCPMessage(ws: WebSocket, message: IMCPRequest): Promise<void> {
    try {
      // Validate basic MCP request structure
      if (message.jsonrpc !== '2.0') {
        const errorResponse: IMCPResponse = {
          jsonrpc: '2.0',
          id: message.id || 0,
          error: {
            code: -32600,
            message: "Invalid Request: jsonrpc field must be '2.0'",
          },
        };
        ws.send(JSON.stringify(errorResponse));
        return;
      }

      let response: IMCPResponse;

      switch (message.method) {
        case 'tools/list':
          response = {
            jsonrpc: '2.0',
            id: message.id,
            result: {
              tools: [
                {
                  name: 'search_documentation',
                  description: 'Search collection documentation using semantic similarity',
                  inputSchema: {
                    type: 'object',
                    properties: {
                      query: {
                        type: 'string',
                        description: 'Search query',
                      },
                      collection: {
                        type: 'string',
                        description: 'Specific collection to search (optional)',
                      },
                      limit: {
                        type: 'number',
                        description: 'Maximum number of results (1-20)',
                        minimum: 1,
                        maximum: 20,
                        default: 5,
                      },
                    },
                    required: ['query'],
                  },
                },
                {
                  name: 'get_collection_info',
                  description: 'Get detailed information about a specific collection',
                  inputSchema: {
                    type: 'object',
                    properties: {
                      collection_name: {
                        type: 'string',
                        description: 'Collection name or ID',
                      },
                      include_versions: {
                        type: 'boolean',
                        description: 'Include version information',
                        default: false,
                      },
                    },
                    required: ['collection_name'],
                  },
                },
              ],
            },
          };
          break;

        case 'tools/call': {
          const params = message.params as IToolCallParams;
          const { name, arguments: args } = params;
          let toolResult: { content: Array<{ type: string; text: string }> };

          try {
            switch (name) {
              case 'search_documentation':
                toolResult = await this.handleSearchDocumentation(args);
                break;
              case 'get_collection_info':
                toolResult = await this.handleGetCollectionInfo(args);
                break;
              default:
                throw new Error(`Unknown tool: ${name}`);
            }

            response = {
              jsonrpc: '2.0',
              id: message.id,
              result: toolResult,
            };
          } catch (error) {
            // Handle validation errors specially
            if (error instanceof Error && error.message.includes('validation')) {
              response = {
                jsonrpc: '2.0',
                id: message.id,
                error: {
                  code: -32602,
                  message: error.message,
                },
              };
            } else {
              throw error; // Re-throw to be handled by outer catch
            }
          }
          break;
        }

        case 'initialize':
          response = {
            jsonrpc: '2.0',
            id: message.id,
            result: {
              protocolVersion: '2024-11-05',
              capabilities: {
                tools: {},
              },
              serverInfo: {
                name: 'collection-docs-search',
                version: '1.0.0',
              },
            },
          };
          break;

        default:
          response = {
            jsonrpc: '2.0',
            id: message.id,
            error: {
              code: -32601,
              message: `Method not found: ${message.method}`,
            },
          };
      }

      ws.send(JSON.stringify(response));
    } catch (error) {
      const errorResponse: IMCPResponse = {
        jsonrpc: '2.0',
        id: message.id,
        error: {
          code: -32603,
          message: error instanceof Error ? error.message : String(error),
        },
      };
      ws.send(JSON.stringify(errorResponse));
    }
  }

  async handleWebSocketUpgrade(c: Context): Promise<Response> {
    const env = await getEnv();
    const maxConnections = parseInt(env.MCP_MAX_CONNECTIONS || '100');

    // Check connection limit
    if (this.connections.size >= maxConnections) {
      return new Response('Too many connections', { status: 429 });
    }

    // Upgrade to WebSocket
    const upgradeHeader = c.req.header('upgrade');
    if (upgradeHeader !== 'websocket') {
      return new Response('Expected websocket', { status: 400 });
    }

    const webSocketPair = new WebSocketPair();
    const [client, server] = Object.values(webSocketPair);

    // Accept the WebSocket connection
    server.accept();
    this.connections.add(server);

    // Handle incoming messages
    server.addEventListener('message', async (event) => {
      try {
        const message = JSON.parse(event.data as string) as IMCPRequest;
        await this.handleMCPMessage(server, message);
      } catch (error) {
        console.error('Error handling MCP message:', error);
        const errorResponse: IMCPResponse = {
          jsonrpc: '2.0',
          id: 0,
          error: {
            code: -32700,
            message: 'Parse error',
          },
        };
        server.send(JSON.stringify(errorResponse));
      }
    });

    // Handle connection lifecycle
    server.addEventListener('close', () => {
      this.connections.delete(server);
    });

    server.addEventListener('error', (event) => {
      console.error('WebSocket error:', event);
      this.connections.delete(server);
    });

    return new Response(null, {
      status: 101,
      webSocket: client,
    });
  }

  public async handleSearchDocumentation(args: unknown) {
    // Validate input using existing Zod integration
    let params: {
      query: string;
      collection?: string;
      limit: number;
      embedding_provider?: string;
      embedding_model?: string;
    };
    try {
      params = SearchToolSchema.parse(args);
    } catch (error) {
      if (error instanceof ZodError) {
        const issues = error.issues
          .map((issue) => `${issue.path.join('.')}: ${issue.message}`)
          .join(', ');
        throw new Error(`Validation failed: ${issues}`);
      }
      throw new Error(
        `Validation error: ${error instanceof Error ? error.message : String(error)}`
      );
    }

    const startTime = Date.now();

    try {
      // Use existing utility to get vector database
      const vdb = await getVDB();

      // Generate embedding for the query using existing AI integration
      const embedding = await this.generateEmbedding(
        params.query,
        params.embedding_provider,
        params.embedding_model
      );

      // Get document query configuration for search
      const documentConfig = createSearchDocumentConfig({
        limit: params.limit,
        returnMetadata: true,
        returnValues: false,
      });

      // Perform document search using existing VectorizeIndex interface
      const results = await vdb.query(embedding, {
        topK: documentConfig.topK,
        returnMetadata: documentConfig.returnMetadata,
        returnValues: documentConfig.returnValues,
        filter: params.collection ? { collection: params.collection } : undefined,
      });

      const searchTime = Date.now() - startTime;

      // Format response for MCP
      return {
        content: [
          {
            type: 'text',
            text: this.formatSearchResults(results, params.query, searchTime),
          },
        ],
      };
    } catch (error) {
      throw new Error(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleGetCollectionInfo(args: unknown) {
    // Validate input
    let params: { collection_name: string; include_versions: boolean };
    try {
      params = CollectionInfoToolSchema.parse(args);
    } catch (error) {
      if (error instanceof ZodError) {
        const issues = error.issues
          .map((issue) => `${issue.path.join('.')}: ${issue.message}`)
          .join(', ');
        throw new Error(`Validation failed: ${issues}`);
      }
      throw new Error(
        `Validation error: ${error instanceof Error ? error.message : String(error)}`
      );
    }

    try {
      // Use existing utilities
      const vdb = await getVDB();
      const kv = await getKV();
      const env = await getEnv();

      // Check cache first using existing KV utility
      const cacheKey = `collection_info:${params.collection_name}:${params.include_versions}`;
      const cacheTtl = parseInt(env.MCP_CACHE_TTL || '3600');

      const cached = await kv.get(cacheKey, 'json');
      if (cached) {
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(cached, null, 2),
            },
          ],
        };
      }

      // Get document query configuration for collection info
      const documentConfig = getDocumentQueryConfig({
        topK: 1,
        returnMetadata: true,
        returnValues: false,
      });

      // Query vector database for collection information
      // Use dummy embedding since we're just getting metadata
      const dummyEmbedding = Array(1024).fill(0);
      const results = await vdb.query(dummyEmbedding, {
        topK: documentConfig.topK,
        returnMetadata: documentConfig.returnMetadata,
        returnValues: documentConfig.returnValues,
        filter: { collection: params.collection_name },
      });

      if (results.matches.length === 0) {
        throw new Error(`Collection '${params.collection_name}' not found`);
      }

      const collectionInfo = this.processCollectionInfo(
        results.matches[0].metadata || {},
        params.include_versions
      );

      // Cache the result using existing KV utility
      await kv.put(cacheKey, JSON.stringify(collectionInfo), { expirationTtl: cacheTtl });

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(collectionInfo, null, 2),
          },
        ],
      };
    } catch (error) {
      throw new Error(
        `Failed to get collection info: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async generateEmbedding(
    text: string,
    provider?: string,
    model?: string
  ): Promise<number[]> {
    if (provider || model) {
      // Validate provider/model combination if both are provided
      if (provider && model) {
        const { EmbeddingService } = await import('@services');
        if (!EmbeddingService.validateProviderModel(provider as TEmbeddingProvider, model)) {
          throw new Error(`Model '${model}' is not compatible with provider '${provider}'`);
        }
      }

      // Create a custom embedding service with the specified configuration
      const { EmbeddingService } = await import('@services');
      const customService = EmbeddingService.withConfig({
        provider: provider as TEmbeddingProvider,
        model: model,
      });

      const result = await customService.generateEmbedding(text);
      return result.embedding;
    }

    const { embeddingService } = await import('@services');
    const result = await embeddingService.generateEmbedding(text);
    return result.embedding;
  }

  private formatSearchResults(
    results: VectorizeMatches,
    query: string,
    searchTime: number
  ): string {
    const formattedResults = results.matches
      .map((match, index: number) => {
        return `## Result ${index + 1} (Score: ${match.score.toFixed(3)})

**Collection**: ${match.metadata?.collection || 'Unknown'}
**Title**: ${match.metadata?.title || 'Untitled'}

${match.metadata?.content || 'No content available'}

---`;
      })
      .join('\n\n');

    const metadata = {
      query,
      total_results: results.count,
      search_time_ms: searchTime,
      collections_searched: [
        ...new Set(
          results.matches.map((m) => String(m.metadata?.collection || '')).filter(Boolean)
        ),
      ],
      relevance_threshold: 0.7,
    };

    return `# Search Results for: "${query}"

${formattedResults}

## Search Metadata
\`\`\`json
${JSON.stringify(metadata, null, 2)}
\`\`\``;
  }

  private processCollectionInfo(
    metadata: Record<string, VectorizeVectorMetadata>,
    includeVersions: boolean
  ): Record<string, unknown> {
    const info: Record<string, unknown> = {
      name: String(metadata?.collection_name || metadata?.collection || ''),
      description: metadata?.description ? String(metadata.description) : undefined,
      documentation_url: metadata?.docs_url ? String(metadata.docs_url) : undefined,
      repository_url: metadata?.repo_url ? String(metadata.repo_url) : undefined,
      latest_version: metadata?.version ? String(metadata.version) : undefined,
      tags: metadata?.tags || [],
      last_updated: metadata?.updated_at,
    };

    if (includeVersions && metadata?.versions) {
      info.versions = metadata.versions;
    }

    return info;
  }

  /**
   * Gets the current number of active WebSocket connections
   *
   * @returns The number of active connections
   *
   * @example
   * ```typescript
   * const handler = new MCPWebSocketHandler();
   * console.log(`Active connections: ${handler.getConnectionCount()}`);
   * ```
   */
  getConnectionCount(): number {
    return this.connections.size;
  }
}
