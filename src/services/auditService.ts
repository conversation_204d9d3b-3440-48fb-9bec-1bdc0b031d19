/**
 * Audit Service
 *
 * Service for logging and tracking all team management actions and system events.
 * Provides comprehensive audit trail for security and compliance.
 */

import type { TAuditLog, TNewAuditLog } from '@dbSchema';
import * as schema from '@dbSchema';
import type { IUnifiedHonoEnv } from '@middleware/index';
import getDB from '@utils/getDB';
import { eq } from 'drizzle-orm';
import type { Context } from 'hono';

// ============================================================================
// Audit Action Types
// ============================================================================

/**
 * Available audit actions
 */
export type TAuditAction =
  // Team actions
  | 'team.create'
  | 'team.update'
  | 'team.delete'
  | 'team.restore'
  // Team member actions
  | 'team_member.invite'
  | 'team_member.add'
  | 'team_member.update_role'
  | 'team_member.update_permissions'
  | 'team_member.remove'
  | 'team_member.leave'
  // Project actions
  | 'project.create'
  | 'project.update'
  | 'project.delete'
  | 'project.restore'
  | 'project.share'
  | 'project.unshare'
  // Collection actions
  | 'collection.create'
  | 'collection.update'
  | 'collection.delete'
  | 'collection.restore'
  // Document actions
  | 'document.upload'
  | 'document.delete'
  | 'document.restore'
  // API key actions
  | 'api_key.create'
  | 'api_key.update'
  | 'api_key.delete'
  | 'api_key.use'
  // Billing actions
  | 'billing.subscription_create'
  | 'billing.subscription_update'
  | 'billing.subscription_cancel'
  | 'billing.payment_success'
  | 'billing.payment_failed'
  | 'billing.api_requests_purchase'
  // Security actions
  | 'auth.login'
  | 'auth.logout'
  | 'auth.failed_login'
  | 'security.permission_denied'
  | 'security.suspicious_activity'
  // Permission actions
  | 'permission.check'
  | 'permission.granted'
  | 'permission.denied'
  | 'permission.escalation'
  | 'permission.override'
  // Search and vector actions
  | 'search.query'
  | 'search.results'
  | 'vector.create'
  | 'vector.read'
  | 'vector.update'
  | 'vector.delete'
  | 'vector.bulk_delete'
  // Upload actions
  | 'upload.file'
  | 'upload.text'
  | 'upload.process';

/**
 * Available audit resource types
 */
export type TAuditResource =
  | 'team'
  | 'team_member'
  | 'project'
  | 'collection'
  | 'document'
  | 'api_key'
  | 'billing_subscription'
  | 'billing_transaction'
  | 'user'
  | 'system'
  | 'permission'
  | 'search'
  | 'vector'
  | 'upload'
  | 'analytics'
  | 'notification'
  | 'notification_preferences';

/**
 * Audit log entry details
 */
export interface IAuditDetails {
  [key: string]: unknown;
  // Common fields
  old_values?: Record<string, unknown>;
  new_values?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
  // Security fields
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
  ip_address?: string;
  user_agent?: string;
  // Context fields
  team_id?: string;
  project_id?: string;
  collection_id?: string;
  // Permission-specific fields
  permission?: string;
  permissions?: string[];
  required_permission?: string;
  required_permissions?: string[];
  permission_result?: 'granted' | 'denied' | 'inherited' | 'overridden';
  role?: string;
  effective_permissions?: string[];
  inherited_from_role?: boolean;
  // Resource-specific fields
  resource_type?: string;
  resource_id?: string;
  resource_owner_id?: string;
  // Search-specific fields
  search_query?: string;
  search_results_count?: number;
  search_filters?: Record<string, unknown>;
  // Upload-specific fields
  file_name?: string;
  file_size?: number;
  file_type?: string;
  content_hash?: string;
}

/**
 * Audit log creation parameters
 */
export interface ICreateAuditLogParams {
  userId?: string;
  teamId?: string;
  action: TAuditAction;
  resource: TAuditResource;
  resourceId?: string;
  details?: IAuditDetails;
  ipAddress?: string;
  userAgent?: string;
}

// ============================================================================
// Audit Service Functions
// ============================================================================

/**
 * Create an audit log entry
 *
 * @param params - Audit log parameters
 * @returns Created audit log entry
 */
export async function createAuditLog(params: ICreateAuditLogParams): Promise<TAuditLog> {
  const db = await getDB();

  const auditLogData: TNewAuditLog = {
    userId: params.userId || null,
    teamId: params.teamId || null,
    action: params.action,
    resource: params.resource,
    resourceId: params.resourceId || null,
    details: params.details ? JSON.stringify(params.details) : null,
    ipAddress: params.ipAddress || null,
    userAgent: params.userAgent || null,
  };

  const [auditLog] = await db.insert(schema.auditLogs).values(auditLogData).returning();

  return auditLog;
}

/**
 * Create audit log from Hono context
 *
 * Extracts user, team, and request information from Hono context.
 *
 * @param c - Hono context
 * @param action - Audit action
 * @param resource - Resource type
 * @param resourceId - Resource ID (optional)
 * @param details - Additional details (optional)
 * @returns Created audit log entry
 */
export async function createAuditLogFromContext(
  c: Context<IUnifiedHonoEnv>,
  action: TAuditAction,
  resource: TAuditResource,
  resourceId?: string,
  details?: IAuditDetails
): Promise<TAuditLog> {
  const user = c.get('user');
  const teamId = c.get('teamId');
  const ipAddress = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || null;
  const userAgent = c.req.header('User-Agent') || null;

  return createAuditLog({
    userId: user?.id,
    teamId: teamId || undefined,
    action,
    resource,
    resourceId,
    details: {
      ...details,
      ip_address: ipAddress || undefined,
      user_agent: userAgent || undefined,
    },
    ipAddress: ipAddress || undefined,
    userAgent: userAgent || undefined,
  });
}

/**
 * Log team action
 *
 * Convenience function for logging team-related actions.
 *
 * @param c - Hono context
 * @param action - Team action
 * @param teamId - Team ID
 * @param details - Additional details
 * @returns Created audit log entry
 */
export async function logTeamAction(
  c: Context<IUnifiedHonoEnv>,
  action: TAuditAction,
  teamId: string,
  details?: IAuditDetails
): Promise<TAuditLog> {
  return createAuditLogFromContext(c, action, 'team', teamId, {
    ...details,
    team_id: teamId || undefined,
  });
}

/**
 * Log project action
 *
 * Convenience function for logging project-related actions.
 *
 * @param c - Hono context
 * @param action - Project action
 * @param projectId - Project ID
 * @param details - Additional details
 * @returns Created audit log entry
 */
export async function logProjectAction(
  c: Context<IUnifiedHonoEnv>,
  action: TAuditAction,
  projectId: string,
  details?: IAuditDetails
): Promise<TAuditLog> {
  const teamId = c.get('teamId');

  return createAuditLogFromContext(c, action, 'project', projectId, {
    ...details,
    team_id: teamId || undefined,
    project_id: projectId,
  });
}

/**
 * Log security event
 *
 * Convenience function for logging security-related events.
 *
 * @param c - Hono context
 * @param action - Security action
 * @param details - Security details
 * @returns Created audit log entry
 */
export async function logSecurityEvent(
  c: Context<IUnifiedHonoEnv>,
  action: TAuditAction,
  details?: IAuditDetails
): Promise<TAuditLog> {
  return createAuditLogFromContext(c, action, 'system', undefined, {
    ...details,
    risk_level: details?.risk_level || 'medium',
  });
}

/**
 * Log API key usage
 *
 * Convenience function for logging API key usage.
 *
 * @param apiKeyId - API key ID
 * @param userId - User ID
 * @param teamId - Team ID (optional)
 * @param endpoint - API endpoint accessed
 * @param ipAddress - IP address
 * @param userAgent - User agent
 * @returns Created audit log entry
 */
export async function logApiKeyUsage(
  apiKeyId: string,
  userId: string,
  teamId?: string,
  endpoint?: string,
  ipAddress?: string,
  userAgent?: string
): Promise<TAuditLog> {
  return createAuditLog({
    userId,
    teamId,
    action: 'api_key.use',
    resource: 'api_key',
    resourceId: apiKeyId,
    details: {
      endpoint,
      timestamp: new Date().toISOString(),
    },
    ipAddress,
    userAgent,
  });
}

// ============================================================================
// Audit Query Functions
// ============================================================================

/**
 * Get audit logs for a team
 *
 * @param teamId - Team ID
 * @param limit - Maximum number of logs to return (default: 100)
 * @param offset - Number of logs to skip (default: 0)
 * @returns Array of audit logs
 */
export async function getTeamAuditLogs(
  teamId: string,
  limit = 100,
  offset = 0
): Promise<TAuditLog[]> {
  const db = await getDB();

  return db.query.auditLogs.findMany({
    where: eq(schema.auditLogs.teamId, teamId),
    limit,
    offset,
    orderBy: (auditLogs, { desc }) => [desc(auditLogs.createdAt)],
  });
}

/**
 * Get audit logs for a user
 *
 * @param userId - User ID
 * @param limit - Maximum number of logs to return (default: 100)
 * @param offset - Number of logs to skip (default: 0)
 * @returns Array of audit logs
 */
export async function getUserAuditLogs(
  userId: string,
  limit = 100,
  offset = 0
): Promise<TAuditLog[]> {
  const db = await getDB();

  return db.query.auditLogs.findMany({
    where: eq(schema.auditLogs.userId, userId),
    limit,
    offset,
    orderBy: (auditLogs, { desc }) => [desc(auditLogs.createdAt)],
  });
}

/**
 * Get audit logs for a specific resource
 *
 * @param resource - Resource type
 * @param resourceId - Resource ID
 * @param limit - Maximum number of logs to return (default: 100)
 * @param offset - Number of logs to skip (default: 0)
 * @returns Array of audit logs
 */
export async function getResourceAuditLogs(
  resource: TAuditResource,
  resourceId: string,
  limit = 100,
  offset = 0
): Promise<TAuditLog[]> {
  const db = await getDB();

  return db.query.auditLogs.findMany({
    where: (auditLogs, { eq, and }) =>
      and(eq(auditLogs.resource, resource), eq(auditLogs.resourceId, resourceId)),
    limit,
    offset,
    orderBy: (auditLogs, { desc }) => [desc(auditLogs.createdAt)],
  });
}

// ============================================================================
// Specialized Audit Logging Functions
// ============================================================================

/**
 * Log permission check result
 *
 * @param c - Hono context
 * @param permission - Permission that was checked
 * @param result - Permission check result
 * @param resourceType - Type of resource being accessed
 * @param resourceId - ID of resource being accessed
 * @returns Created audit log entry
 */
export async function logPermissionCheck(
  c: Context<IUnifiedHonoEnv>,
  permission: string,
  result: 'granted' | 'denied' | 'inherited',
  resourceType?: string,
  resourceId?: string
): Promise<TAuditLog> {
  const _user = c.get('user');
  const teamRole = c.get('teamRole');
  const teamPermissions = c.get('teamPermissions');

  return createAuditLogFromContext(
    c,
    result === 'granted' ? 'permission.granted' : 'permission.denied',
    'permission',
    resourceId,
    {
      permission,
      permission_result: result,
      role: teamRole || undefined,
      effective_permissions: teamPermissions || undefined,
      resource_type: resourceType,
      resource_id: resourceId,
      risk_level: result === 'denied' ? 'medium' : 'low',
    }
  );
}

/**
 * Log search action
 *
 * @param c - Hono context
 * @param query - Search query
 * @param resultsCount - Number of results returned
 * @param filters - Search filters applied
 * @returns Created audit log entry
 */
export async function logSearchAction(
  c: Context<IUnifiedHonoEnv>,
  query: string,
  resultsCount: number,
  filters?: Record<string, unknown>
): Promise<TAuditLog> {
  return createAuditLogFromContext(c, 'search.query', 'search', undefined, {
    search_query: query,
    search_results_count: resultsCount,
    search_filters: filters,
    risk_level: 'low',
  });
}

/**
 * Log vector operation
 *
 * @param c - Hono context
 * @param action - Vector action performed
 * @param vectorId - Vector ID (if applicable)
 * @param details - Additional operation details
 * @returns Created audit log entry
 */
export async function logVectorAction(
  c: Context<IUnifiedHonoEnv>,
  action: 'create' | 'read' | 'update' | 'delete' | 'bulk_delete',
  vectorId?: string,
  details?: Record<string, unknown>
): Promise<TAuditLog> {
  const auditAction = `vector.${action}` as TAuditAction;

  return createAuditLogFromContext(c, auditAction, 'vector', vectorId, {
    ...details,
    risk_level: action === 'delete' || action === 'bulk_delete' ? 'medium' : 'low',
  });
}

/**
 * Log upload action
 *
 * @param c - Hono context
 * @param action - Upload action performed
 * @param fileName - Name of uploaded file
 * @param fileSize - Size of uploaded file
 * @param fileType - Type of uploaded file
 * @param contentHash - Hash of uploaded content
 * @returns Created audit log entry
 */
export async function logUploadAction(
  c: Context<IUnifiedHonoEnv>,
  action: 'file' | 'text' | 'process',
  fileName?: string,
  fileSize?: number,
  fileType?: string,
  contentHash?: string
): Promise<TAuditLog> {
  const auditAction = `upload.${action}` as TAuditAction;

  return createAuditLogFromContext(c, auditAction, 'upload', contentHash, {
    file_name: fileName,
    file_size: fileSize,
    file_type: fileType,
    content_hash: contentHash,
    risk_level: 'low',
  });
}

/**
 * Log enhanced security event
 *
 * @param c - Hono context
 * @param event - Security event type
 * @param details - Event details
 * @param riskLevel - Risk level of the event
 * @returns Created audit log entry
 */
export async function logEnhancedSecurityEvent(
  c: Context<IUnifiedHonoEnv>,
  event: 'permission_denied' | 'suspicious_activity' | 'failed_login',
  details: Record<string, unknown>,
  riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'medium'
): Promise<TAuditLog> {
  const auditAction = `security.${event}` as TAuditAction;

  return createAuditLogFromContext(c, auditAction, 'system', undefined, {
    ...details,
    risk_level: riskLevel,
  });
}
