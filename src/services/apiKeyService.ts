/**
 * API Key Service
 *
 * Enhanced API key management with team-based scoping, rate limiting,
 * and usage analytics. Integrates with RBAC system for permission control.
 */

// Web Crypto API is used for Cloudflare Workers compatibility
import type {
  TApiKey,
  TApiKeyRotationHistory,
  TNewApiKey,
  TNewApiKeyRotationHistory,
} from '@dbSchema';
import * as schema from '@dbSchema';
import getDB from '@utils/getDB';
import { and, desc, eq, gte, inArray, isNull, lte, sql } from 'drizzle-orm';
import type { IApiKeyEmailContext } from '@/types/email';
import type { TPermission } from '@/types/teams';
import { notificationService } from './notificationService';

// ============================================================================
// API Key Types
// ============================================================================

/**
 * API key creation parameters
 */
export interface ICreateApiKeyParams {
  userId: string;
  teamId?: string;
  name: string;
  permissions?: TPermission[];
  scopedPermissions?: TPermission[];
  usageLimit?: number;
  expiresAt?: Date;
}

/**
 * API key update parameters
 */
export interface IUpdateApiKeyParams {
  name?: string;
  permissions?: TPermission[];
  scopedPermissions?: TPermission[];
  usageLimit?: number;
  expiresAt?: Date;
  isActive?: boolean;
}

/**
 * API key with usage statistics
 */
export interface IApiKeyWithStats extends TApiKey {
  usageStats: {
    totalUsage: number;
    monthlyUsage: number;
    weeklyUsage: number;
    dailyUsage: number;
    lastUsed: Date | null;
    remainingUsage: number | null; // null if unlimited
  };
  effectivePermissions: TPermission[];
  user?: {
    id: string;
    email: string;
    name: string;
  };
  team?: {
    id: string;
    name: string;
    slug: string;
  };
}

/**
 * API key usage analytics
 */
export interface IApiKeyUsageAnalytics {
  keyId: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  topEndpoints: Array<{
    endpoint: string;
    count: number;
    percentage: number;
  }>;
  usageByDay: Array<{
    date: string;
    requests: number;
  }>;
  errorsByType: Array<{
    errorType: string;
    count: number;
    percentage: number;
  }>;
}

// ============================================================================
// API Key Generation
// ============================================================================

/**
 * Generate a secure API key using Web Crypto API
 *
 * @returns Object with API key and hash
 */
async function generateApiKey(): Promise<{ key: string; hash: string; prefix: string }> {
  // Generate random bytes for the key using Web Crypto API
  const keyBytes = new Uint8Array(32);
  crypto.getRandomValues(keyBytes);

  // Convert to hex string
  const hexString = Array.from(keyBytes)
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');

  const key = `ezc_${hexString}`;

  // Create hash for storage using Web Crypto API
  const encoder = new TextEncoder();
  const data = encoder.encode(key);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hash = Array.from(new Uint8Array(hashBuffer))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');

  // Extract prefix for identification
  const prefix = key.substring(0, 12);

  return { key, hash, prefix };
}

/**
 * Hash an API key for verification using Web Crypto API
 *
 * @param key - API key to hash
 * @returns SHA-256 hash of the key
 */
export async function hashApiKey(key: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(key);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  return Array.from(new Uint8Array(hashBuffer))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');
}

// ============================================================================
// API Key CRUD Operations
// ============================================================================

/**
 * Create a new API key
 *
 * @param params - API key creation parameters
 * @returns Created API key with the actual key value
 */
export async function createApiKey(
  params: ICreateApiKeyParams
): Promise<{ apiKey: TApiKey; key: string }> {
  const db = await getDB();

  // Generate secure API key
  const { key, hash, prefix } = await generateApiKey();

  // Validate permissions if provided
  let validatedPermissions: string | null = null;
  let validatedScopedPermissions: string | null = null;

  if (params.permissions) {
    validatedPermissions = JSON.stringify(params.permissions);
  }

  if (params.scopedPermissions) {
    validatedScopedPermissions = JSON.stringify(params.scopedPermissions);
  }

  // Create API key record
  const newApiKey: TNewApiKey = {
    userId: params.userId,
    teamId: params.teamId || null,
    name: params.name,
    keyHash: hash,
    keyPrefix: prefix,
    permissions: validatedPermissions,
    scopedPermissions: validatedScopedPermissions,
    usageLimit: params.usageLimit || null,
    usageCount: 0,
    expiresAt: params.expiresAt || null,
    isActive: true,
  };

  const [apiKey] = await db.insert(schema.apiKeys).values(newApiKey).returning();

  return { apiKey, key };
}

/**
 * Get API key by ID with statistics
 *
 * @param keyId - API key ID
 * @param includeStats - Whether to include usage statistics
 * @returns API key with statistics or null if not found
 */
export async function getApiKeyById(
  keyId: string,
  includeStats = true
): Promise<IApiKeyWithStats | null> {
  const db = await getDB();

  const apiKey = await db.query.apiKeys.findFirst({
    where: and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)),
    with: {
      team: {
        columns: { id: true, name: true, slug: true },
      },
    },
  });

  if (!apiKey) return null;

  // Calculate usage statistics
  let usageStats = {
    totalUsage: apiKey.usageCount,
    monthlyUsage: 0,
    weeklyUsage: 0,
    dailyUsage: 0,
    lastUsed: apiKey.lastUsedAt,
    remainingUsage: apiKey.usageLimit ? apiKey.usageLimit - apiKey.usageCount : null,
  };

  if (includeStats) {
    // Get usage statistics from audit logs
    const now = new Date();
    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const [monthlyUsage, weeklyUsage, dailyUsage] = await Promise.all([
      getApiKeyUsageCount(keyId, monthAgo),
      getApiKeyUsageCount(keyId, weekAgo),
      getApiKeyUsageCount(keyId, dayAgo),
    ]);

    usageStats = {
      ...usageStats,
      monthlyUsage,
      weeklyUsage,
      dailyUsage,
    };
  }

  // Get effective permissions
  const permissions: TPermission[] = apiKey.permissions ? JSON.parse(apiKey.permissions) : [];
  const scopedPermissions: TPermission[] = apiKey.scopedPermissions
    ? JSON.parse(apiKey.scopedPermissions)
    : [];

  const effectivePermissions = [...new Set([...permissions, ...scopedPermissions])];

  return {
    ...apiKey,
    usageStats,
    effectivePermissions,
    team: apiKey.team || undefined,
  };
}

/**
 * Get API key by hash (for authentication)
 *
 * @param keyHash - Hashed API key
 * @param useGracePeriod - Whether to allow grace period for expired keys (default: true)
 * @param gracePeriodDays - Grace period in days (default: 7)
 * @returns API key or null if not found/invalid
 */
export async function getApiKeyByHash(
  keyHash: string,
  useGracePeriod = true,
  gracePeriodDays = 7
): Promise<TApiKey | null> {
  if (useGracePeriod) {
    return checkApiKeyWithGracePeriod(keyHash, gracePeriodDays);
  }

  const db = await getDB();

  const apiKey = await db.query.apiKeys.findFirst({
    where: and(
      eq(schema.apiKeys.keyHash, keyHash),
      eq(schema.apiKeys.isActive, true),
      isNull(schema.apiKeys.deletedAt)
    ),
  });

  if (!apiKey) return null;

  // Check if key is expired (strict mode)
  if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
    return null;
  }

  // Check usage limit
  if (apiKey.usageLimit && apiKey.usageCount >= apiKey.usageLimit) {
    return null;
  }

  return apiKey;
}

/**
 * Update API key
 *
 * @param keyId - API key ID
 * @param updateParams - Update parameters
 * @returns Updated API key or null if not found
 */
export async function updateApiKey(
  keyId: string,
  updateParams: IUpdateApiKeyParams
): Promise<TApiKey | null> {
  const db = await getDB();

  const updateData: Partial<TApiKey> = {
    updatedAt: new Date(),
  };

  if (updateParams.name !== undefined) {
    updateData.name = updateParams.name;
  }

  if (updateParams.permissions !== undefined) {
    updateData.permissions = JSON.stringify(updateParams.permissions);
  }

  if (updateParams.scopedPermissions !== undefined) {
    updateData.scopedPermissions = JSON.stringify(updateParams.scopedPermissions);
  }

  if (updateParams.usageLimit !== undefined) {
    updateData.usageLimit = updateParams.usageLimit;
  }

  if (updateParams.expiresAt !== undefined) {
    updateData.expiresAt = updateParams.expiresAt;
  }

  if (updateParams.isActive !== undefined) {
    updateData.isActive = updateParams.isActive;
  }

  const [updatedApiKey] = await db
    .update(schema.apiKeys)
    .set(updateData)
    .where(and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)))
    .returning();

  return updatedApiKey || null;
}

/**
 * Soft delete API key
 *
 * @param keyId - API key ID
 * @returns True if deleted successfully
 */
export async function deleteApiKey(keyId: string): Promise<boolean> {
  const db = await getDB();

  const [deletedApiKey] = await db
    .update(schema.apiKeys)
    .set({
      deletedAt: new Date(),
      updatedAt: new Date(),
      isActive: false,
    })
    .where(and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)))
    .returning();

  return !!deletedApiKey;
}

// ============================================================================
// Usage Tracking
// ============================================================================

/**
 * Record API key usage
 *
 * @param keyId - API key ID
 * @returns Updated usage count
 */
export async function recordApiKeyUsage(keyId: string): Promise<number> {
  const db = await getDB();

  const [updatedApiKey] = await db
    .update(schema.apiKeys)
    .set({
      usageCount: sql`${schema.apiKeys.usageCount} + 1`,
      lastUsedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(schema.apiKeys.id, keyId))
    .returning({ usageCount: schema.apiKeys.usageCount });

  return updatedApiKey?.usageCount || 0;
}

/**
 * Increment API key usage count (alias for recordApiKeyUsage)
 *
 * @param keyId - API key ID
 * @returns Updated API key or null if not found
 */
export async function incrementApiKeyUsage(keyId: string): Promise<TApiKey | null> {
  const db = await getDB();

  const [updatedApiKey] = await db
    .update(schema.apiKeys)
    .set({
      usageCount: sql`${schema.apiKeys.usageCount} + 1`,
      lastUsedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(schema.apiKeys.id, keyId))
    .returning();

  return updatedApiKey || null;
}

/**
 * Get API key usage count within a time period
 *
 * @param keyId - API key ID
 * @param since - Start date for counting
 * @returns Usage count
 */
async function getApiKeyUsageCount(keyId: string, since: Date): Promise<number> {
  const db = await getDB();

  const result = await db
    .select({ count: sql<number>`count(*)` })
    .from(schema.auditLogs)
    .where(
      and(
        eq(schema.auditLogs.action, 'api_key.use'),
        eq(schema.auditLogs.resourceId, keyId),
        gte(schema.auditLogs.createdAt, since)
      )
    );

  return result[0]?.count || 0;
}

// ============================================================================
// Enhanced API Key Management Functions
// ============================================================================

/**
 * API key filtering options
 */
export interface IApiKeyFilters {
  userId?: string;
  teamId?: string;
  isActive?: boolean;
  hasExpired?: boolean;
  permissions?: TPermission[];
  usageThreshold?: number; // Filter by usage percentage
  createdAfter?: Date;
  createdBefore?: Date;
  lastUsedAfter?: Date;
  lastUsedBefore?: Date;
}

/**
 * API key listing options
 */
export interface IApiKeyListOptions {
  filters?: IApiKeyFilters;
  sortBy?: 'createdAt' | 'lastUsedAt' | 'usageCount' | 'name';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  includeStats?: boolean;
}

/**
 * List API keys with advanced filtering and pagination
 *
 * @param options - Listing options
 * @returns Paginated list of API keys
 */
export async function listApiKeys(options: IApiKeyListOptions = {}): Promise<{
  apiKeys: IApiKeyWithStats[];
  total: number;
  hasMore: boolean;
}> {
  const db = await getDB();
  const {
    filters = {},
    sortBy = 'createdAt',
    sortOrder = 'desc',
    limit = 50,
    offset = 0,
    includeStats = true,
  } = options;

  // Build where conditions
  const whereConditions = [isNull(schema.apiKeys.deletedAt)];

  if (filters.userId) {
    whereConditions.push(eq(schema.apiKeys.userId, filters.userId));
  }

  if (filters.teamId) {
    whereConditions.push(eq(schema.apiKeys.teamId, filters.teamId));
  }

  if (filters.isActive !== undefined) {
    whereConditions.push(eq(schema.apiKeys.isActive, filters.isActive));
  }

  if (filters.createdAfter) {
    whereConditions.push(gte(schema.apiKeys.createdAt, filters.createdAfter));
  }

  if (filters.createdBefore) {
    whereConditions.push(gte(schema.apiKeys.createdAt, filters.createdBefore));
  }

  // Get total count
  const totalResult = await db
    .select({ count: sql<number>`count(*)` })
    .from(schema.apiKeys)
    .where(and(...whereConditions));

  const total = totalResult[0]?.count || 0;

  // Get API keys
  const apiKeysQuery = db
    .select()
    .from(schema.apiKeys)
    .where(and(...whereConditions))
    .limit(limit)
    .offset(offset);

  // Apply sorting
  if (sortBy === 'createdAt') {
    apiKeysQuery.orderBy(
      sortOrder === 'desc' ? desc(schema.apiKeys.createdAt) : schema.apiKeys.createdAt
    );
  } else if (sortBy === 'lastUsedAt') {
    apiKeysQuery.orderBy(
      sortOrder === 'desc' ? desc(schema.apiKeys.lastUsedAt) : schema.apiKeys.lastUsedAt
    );
  } else if (sortBy === 'usageCount') {
    apiKeysQuery.orderBy(
      sortOrder === 'desc' ? desc(schema.apiKeys.usageCount) : schema.apiKeys.usageCount
    );
  } else if (sortBy === 'name') {
    apiKeysQuery.orderBy(sortOrder === 'desc' ? desc(schema.apiKeys.name) : schema.apiKeys.name);
  }

  const apiKeys = await apiKeysQuery;

  // Enhance with stats if requested
  const enhancedApiKeys: IApiKeyWithStats[] = [];
  for (const apiKey of apiKeys) {
    if (includeStats) {
      const enhanced = await enhanceApiKeyWithStats(apiKey);
      enhancedApiKeys.push(enhanced);
    } else {
      enhancedApiKeys.push({
        ...apiKey,
        usageStats: {
          totalUsage: apiKey.usageCount,
          monthlyUsage: 0,
          weeklyUsage: 0,
          dailyUsage: 0,
          lastUsed: apiKey.lastUsedAt,
          remainingUsage: apiKey.usageLimit ? apiKey.usageLimit - apiKey.usageCount : null,
        },
        effectivePermissions: [],
      });
    }
  }

  return {
    apiKeys: enhancedApiKeys,
    total,
    hasMore: offset + limit < total,
  };
}

/**
 * Enhance API key with usage statistics and team information
 *
 * @param apiKey - Base API key record
 * @returns Enhanced API key with stats
 */
async function enhanceApiKeyWithStats(apiKey: TApiKey): Promise<IApiKeyWithStats> {
  const db = await getDB();

  // Calculate usage statistics
  const now = new Date();
  const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  const [dailyUsage, weeklyUsage, monthlyUsage] = await Promise.all([
    getApiKeyUsageCount(apiKey.id, dayAgo),
    getApiKeyUsageCount(apiKey.id, weekAgo),
    getApiKeyUsageCount(apiKey.id, monthAgo),
  ]);

  // Get team information if teamId exists
  let team: { id: string; name: string; slug: string } | undefined;
  if (apiKey.teamId) {
    const teamRecord = await db.query.teams.findFirst({
      where: eq(schema.teams.id, apiKey.teamId),
      columns: { id: true, name: true, slug: true },
    });
    if (teamRecord) {
      team = teamRecord;
    }
  }

  // Parse effective permissions
  let effectivePermissions: TPermission[] = [];
  if (apiKey.permissions) {
    try {
      const parsed = JSON.parse(apiKey.permissions);
      if (Array.isArray(parsed)) {
        effectivePermissions = parsed.filter((p): p is TPermission => typeof p === 'string');
      }
    } catch (error) {
      console.error('Failed to parse API key permissions:', error);
    }
  }

  // Add scoped permissions
  if (apiKey.scopedPermissions) {
    try {
      const parsed = JSON.parse(apiKey.scopedPermissions);
      if (Array.isArray(parsed)) {
        const scopedPerms = parsed.filter((p): p is TPermission => typeof p === 'string');
        effectivePermissions = [...effectivePermissions, ...scopedPerms];
      }
    } catch (error) {
      console.error('Failed to parse API key scoped permissions:', error);
    }
  }

  return {
    ...apiKey,
    usageStats: {
      totalUsage: apiKey.usageCount,
      monthlyUsage,
      weeklyUsage,
      dailyUsage,
      lastUsed: apiKey.lastUsedAt,
      remainingUsage: apiKey.usageLimit ? apiKey.usageLimit - apiKey.usageCount : null,
    },
    effectivePermissions,
    team,
  };
}

/**
 * Validate API key permissions against RBAC system
 *
 * @param permissions - Permissions to validate
 * @param teamId - Optional team ID for team-scoped validation
 * @returns Validation result
 */
export async function validateApiKeyPermissions(
  permissions: TPermission[],
  teamId?: string
): Promise<{
  valid: boolean;
  invalidPermissions: string[];
  warnings: string[];
}> {
  const { isValidPermission } = await import('@utils/permission-constants');

  const invalidPermissions: string[] = [];
  const warnings: string[] = [];

  // Validate each permission
  for (const permission of permissions) {
    if (!isValidPermission(permission)) {
      invalidPermissions.push(permission);
    }
  }

  // Check for team-specific permission restrictions
  if (teamId) {
    const teamSpecificPermissions = permissions.filter(
      (p) => p.includes('.team') || p.includes('.billing')
    );

    if (teamSpecificPermissions.length > 0) {
      warnings.push(
        `Team-scoped permissions detected: ${teamSpecificPermissions.join(', ')}. ` +
          'Ensure the API key is properly scoped to the team.'
      );
    }
  }

  // Check for potentially dangerous permissions
  const dangerousPermissions = permissions.filter(
    (p) => p.includes('delete.') || p.includes('manage.')
  );

  if (dangerousPermissions.length > 0) {
    warnings.push(
      `High-privilege permissions detected: ${dangerousPermissions.join(', ')}. ` +
        'Consider using more specific permissions for better security.'
    );
  }

  return {
    valid: invalidPermissions.length === 0,
    invalidPermissions,
    warnings,
  };
}

/**
 * Rotate API key (generate new key, invalidate old one)
 *
 * @param keyId - API key ID to rotate
 * @returns New API key details
 */
export async function rotateApiKey(keyId: string): Promise<{
  apiKey: TApiKey;
  key: string;
  oldKeyId: string;
} | null> {
  const db = await getDB();

  // Get existing API key
  const existingKey = await db.query.apiKeys.findFirst({
    where: and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)),
  });

  if (!existingKey) {
    return null;
  }

  // Generate new API key
  const { key, hash, prefix } = await generateApiKey();

  // Create new API key with same properties
  const newApiKey: TNewApiKey = {
    userId: existingKey.userId,
    teamId: existingKey.teamId,
    name: `${existingKey.name} (Rotated)`,
    keyHash: hash,
    keyPrefix: prefix,
    permissions: existingKey.permissions,
    scopedPermissions: existingKey.scopedPermissions,
    usageLimit: existingKey.usageLimit,
    usageCount: 0,
    expiresAt: existingKey.expiresAt,
    isActive: true,
  };

  // Insert new key and soft delete old key in a transaction
  const [newKey] = await db.transaction(async (tx) => {
    // Insert new key
    const [inserted] = await tx.insert(schema.apiKeys).values(newApiKey).returning();

    // Soft delete old key
    await tx
      .update(schema.apiKeys)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
        isActive: false,
      })
      .where(eq(schema.apiKeys.id, keyId));

    return [inserted];
  });

  return {
    apiKey: newKey,
    key,
    oldKeyId: keyId,
  };
}

/**
 * Bulk operations for API keys
 */
export interface IBulkApiKeyOperation {
  keyIds: string[];
  operation:
    | 'activate'
    | 'deactivate'
    | 'delete'
    | 'update_usage_limit'
    | 'extend_expiration'
    | 'rotate';
  params?: {
    usageLimit?: number;
    expirationExtensionDays?: number;
    newExpirationDate?: Date;
  };
}

/**
 * Expiration management options
 */
export interface IExpirationManagementOptions {
  // Grace period settings
  gracePeriodDays?: number;
  allowGracePeriodUsage?: boolean;

  // Notification settings
  warningDays?: number[];
  notifyTeamAdmins?: boolean;

  // Auto-rotation settings
  autoRotateBeforeExpiration?: boolean;
  autoRotateDays?: number;
}

/**
 * API key expiration status
 */
export interface IApiKeyExpirationStatus {
  keyId: string;
  keyName: string;
  keyPrefix: string;
  userId: string;
  userEmail: string;
  teamId?: string;
  teamName?: string;

  // Expiration details
  expiresAt: Date;
  daysUntilExpiration: number;
  isExpired: boolean;
  isInGracePeriod: boolean;

  // Usage information
  lastUsed?: Date;
  usageCount: number;
  usageLimit?: number;

  // Notification status
  notificationsSent: string[]; // Array of notification types sent
  lastNotificationSent?: Date;
}

/**
 * Bulk expiration summary
 */
export interface IBulkExpirationSummary {
  totalKeys: number;
  expiredKeys: number;
  expiringIn7Days: number;
  expiringIn1Day: number;
  inGracePeriod: number;

  // Team breakdown
  teamsAffected: Array<{
    teamId: string;
    teamName: string;
    expiredCount: number;
    expiringCount: number;
  }>;

  // User breakdown
  usersAffected: Array<{
    userId: string;
    userEmail: string;
    expiredCount: number;
    expiringCount: number;
  }>;
}

/**
 * Perform bulk operations on API keys
 *
 * @param operation - Bulk operation details
 * @returns Operation results
 */
export async function bulkUpdateApiKeys(operation: IBulkApiKeyOperation): Promise<{
  success: number;
  failed: number;
  errors: Array<{ keyId: string; error: string }>;
}> {
  const db = await getDB();
  const { keyIds, operation: op, params } = operation;

  let success = 0;
  let failed = 0;
  const errors: Array<{ keyId: string; error: string }> = [];

  for (const keyId of keyIds) {
    try {
      switch (op) {
        case 'activate':
          await db
            .update(schema.apiKeys)
            .set({ isActive: true, updatedAt: new Date() })
            .where(and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)));
          break;

        case 'deactivate':
          await db
            .update(schema.apiKeys)
            .set({ isActive: false, updatedAt: new Date() })
            .where(and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)));
          break;

        case 'delete':
          await db
            .update(schema.apiKeys)
            .set({ deletedAt: new Date(), updatedAt: new Date(), isActive: false })
            .where(and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)));
          break;

        case 'update_usage_limit':
          if (params?.usageLimit !== undefined) {
            await db
              .update(schema.apiKeys)
              .set({ usageLimit: params.usageLimit, updatedAt: new Date() })
              .where(and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)));
          }
          break;

        case 'extend_expiration':
          if (params?.expirationExtensionDays || params?.newExpirationDate) {
            await extendApiKeyExpiration(
              [keyId],
              params.expirationExtensionDays,
              params.newExpirationDate
            );
          }
          break;

        case 'rotate': {
          const rotationResult = await rotateApiKey(keyId);
          if (!rotationResult) {
            throw new Error('Failed to rotate API key');
          }
          break;
        }

        default:
          throw new Error(`Unknown operation: ${op}`);
      }
      success++;
    } catch (error) {
      failed++;
      errors.push({
        keyId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  return { success, failed, errors };
}

/**
 * Get comprehensive usage analytics for an API key
 *
 * @param keyId - API key ID
 * @param days - Number of days to analyze (default: 30)
 * @returns Detailed usage analytics
 */
export async function getApiKeyAnalytics(
  keyId: string,
  days = 30
): Promise<IApiKeyUsageAnalytics | null> {
  const db = await getDB();

  // Check if API key exists
  const apiKey = await db.query.apiKeys.findFirst({
    where: and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)),
  });

  if (!apiKey) {
    return null;
  }

  const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

  // Get audit logs for this API key
  const auditLogs = await db.query.auditLogs.findMany({
    where: and(
      eq(schema.auditLogs.resourceId, keyId),
      eq(schema.auditLogs.action, 'api_key.use'),
      gte(schema.auditLogs.createdAt, since)
    ),
    orderBy: desc(schema.auditLogs.createdAt),
  });

  // Calculate analytics
  const totalRequests = auditLogs.length;
  const successfulRequests = auditLogs.filter(
    (log) => !log.details || !JSON.parse(log.details as string).error
  ).length;
  const failedRequests = totalRequests - successfulRequests;

  // Calculate average response time (if available in details)
  let averageResponseTime = 0;
  const responseTimes = auditLogs
    .map((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        return details.responseTime || 0;
      } catch {
        return 0;
      }
    })
    .filter((time) => time > 0);

  if (responseTimes.length > 0) {
    averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
  }

  // Group by endpoint
  const endpointCounts = new Map<string, number>();
  auditLogs.forEach((log) => {
    try {
      const details = JSON.parse((log.details as string) || '{}');
      const endpoint = details.endpoint || 'unknown';
      endpointCounts.set(endpoint, (endpointCounts.get(endpoint) || 0) + 1);
    } catch {
      endpointCounts.set('unknown', (endpointCounts.get('unknown') || 0) + 1);
    }
  });

  const topEndpoints = Array.from(endpointCounts.entries())
    .map(([endpoint, count]) => ({
      endpoint,
      count,
      percentage: totalRequests > 0 ? (count / totalRequests) * 100 : 0,
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);

  // Group by day
  const usageByDay = new Map<string, number>();
  auditLogs.forEach((log) => {
    const date = log.createdAt.toISOString().split('T')[0];
    usageByDay.set(date, (usageByDay.get(date) || 0) + 1);
  });

  const usageByDayArray = Array.from(usageByDay.entries())
    .map(([date, requests]) => ({ date, requests }))
    .sort((a, b) => a.date.localeCompare(b.date));

  // Group errors by type
  const errorCounts = new Map<string, number>();
  auditLogs.forEach((log) => {
    try {
      const details = JSON.parse((log.details as string) || '{}');
      if (details.error) {
        const errorType = details.errorType || 'unknown';
        errorCounts.set(errorType, (errorCounts.get(errorType) || 0) + 1);
      }
    } catch {
      // Ignore parsing errors
    }
  });

  const errorsByType = Array.from(errorCounts.entries())
    .map(([errorType, count]) => ({
      errorType,
      count,
      percentage: failedRequests > 0 ? (count / failedRequests) * 100 : 0,
    }))
    .sort((a, b) => b.count - a.count);

  return {
    keyId,
    totalRequests,
    successfulRequests,
    failedRequests,
    averageResponseTime,
    topEndpoints,
    usageByDay: usageByDayArray,
    errorsByType,
  };
}

/**
 * Get API keys for a specific team with enhanced filtering
 *
 * @param teamId - Team ID
 * @param options - Additional filtering options
 * @returns Team's API keys
 */
export async function getTeamApiKeys(
  teamId: string,
  options: Omit<IApiKeyListOptions, 'filters'> & {
    filters?: Omit<IApiKeyFilters, 'teamId'>;
  } = {}
): Promise<{
  apiKeys: IApiKeyWithStats[];
  total: number;
  hasMore: boolean;
}> {
  const filters = { ...options.filters, teamId };
  return listApiKeys({ ...options, filters });
}

/**
 * Get API keys for a specific user with enhanced filtering
 *
 * @param userId - User ID
 * @param options - Additional filtering options
 * @returns User's API keys
 */
export async function getUserApiKeys(
  userId: string,
  options: Omit<IApiKeyListOptions, 'filters'> & {
    filters?: Omit<IApiKeyFilters, 'userId'>;
  } = {}
): Promise<{
  apiKeys: IApiKeyWithStats[];
  total: number;
  hasMore: boolean;
}> {
  const filters = { ...options.filters, userId };
  return listApiKeys({ ...options, filters });
}

/**
 * Check if API key has specific permission with team context
 *
 * @param apiKey - API key record
 * @param permission - Permission to check
 * @param teamId - Optional team context
 * @returns Permission check result
 */
export async function checkApiKeyPermission(
  apiKey: TApiKey,
  permission: TPermission,
  teamId?: string
): Promise<{
  hasPermission: boolean;
  reason?: string;
  teamScoped?: boolean;
}> {
  // Parse permissions
  let permissions: TPermission[] = [];
  if (apiKey.permissions) {
    try {
      const parsed = JSON.parse(apiKey.permissions);
      if (Array.isArray(parsed)) {
        permissions = parsed.filter((p): p is TPermission => typeof p === 'string');
      }
    } catch {
      return {
        hasPermission: false,
        reason: 'Failed to parse API key permissions',
      };
    }
  }

  // Check direct permission
  if (permissions.includes(permission)) {
    return { hasPermission: true };
  }

  // Check scoped permissions if team context is provided
  if (teamId && apiKey.teamId === teamId && apiKey.scopedPermissions) {
    try {
      const scopedPerms = JSON.parse(apiKey.scopedPermissions);
      if (Array.isArray(scopedPerms) && scopedPerms.includes(permission)) {
        return { hasPermission: true, teamScoped: true };
      }
    } catch {
      return {
        hasPermission: false,
        reason: 'Failed to parse scoped permissions',
      };
    }
  }

  // Check for manage permissions (higher level)
  const category = permission.split('.')[1];
  const managePermission = `manage.${category}` as TPermission;
  if (permissions.includes(managePermission)) {
    return { hasPermission: true };
  }

  return {
    hasPermission: false,
    reason: `API key does not have required permission: ${permission}`,
  };
}

/**
 * Get expiring API keys (within specified days)
 *
 * @param days - Number of days to look ahead (default: 7)
 * @param teamId - Optional team filter
 * @returns Expiring API keys
 */
export async function getExpiringApiKeys(days = 7, teamId?: string): Promise<IApiKeyWithStats[]> {
  const db = await getDB();
  const expirationThreshold = new Date(Date.now() + days * 24 * 60 * 60 * 1000);

  const whereConditions = [
    isNull(schema.apiKeys.deletedAt),
    eq(schema.apiKeys.isActive, true),
    gte(schema.apiKeys.expiresAt, new Date()), // Not already expired
    lte(schema.apiKeys.expiresAt, expirationThreshold), // Expires within threshold
  ];

  if (teamId) {
    whereConditions.push(eq(schema.apiKeys.teamId, teamId));
  }

  const apiKeys = await db
    .select()
    .from(schema.apiKeys)
    .where(and(...whereConditions))
    .orderBy(schema.apiKeys.expiresAt);

  // Enhance with stats
  const enhancedApiKeys: IApiKeyWithStats[] = [];
  for (const apiKey of apiKeys) {
    const enhanced = await enhanceApiKeyWithStats(apiKey);
    enhancedApiKeys.push(enhanced);
  }

  return enhancedApiKeys;
}

/**
 * Get API key usage summary for billing purposes
 *
 * @param teamId - Team ID
 * @param startDate - Start date for usage calculation
 * @param endDate - End date for usage calculation
 * @returns Usage summary
 */
export async function getApiKeyUsageSummary(
  teamId: string,
  startDate: Date,
  endDate: Date
): Promise<{
  totalRequests: number;
  totalApiKeys: number;
  activeApiKeys: number;
  topApiKeys: Array<{
    keyId: string;
    name: string;
    requests: number;
    percentage: number;
  }>;
}> {
  const db = await getDB();

  // Get team's API keys
  const teamApiKeys = await db.query.apiKeys.findMany({
    where: and(eq(schema.apiKeys.teamId, teamId), isNull(schema.apiKeys.deletedAt)),
    columns: { id: true, name: true, isActive: true },
  });

  const totalApiKeys = teamApiKeys.length;
  const activeApiKeys = teamApiKeys.filter((key) => key.isActive).length;

  // Get usage for each API key
  const keyUsage = new Map<string, number>();
  let totalRequests = 0;

  for (const apiKey of teamApiKeys) {
    const usage = await db
      .select({ count: sql<number>`count(*)` })
      .from(schema.auditLogs)
      .where(
        and(
          eq(schema.auditLogs.resourceId, apiKey.id),
          eq(schema.auditLogs.action, 'api_key.use'),
          gte(schema.auditLogs.createdAt, startDate),
          lte(schema.auditLogs.createdAt, endDate)
        )
      );

    const requests = usage[0]?.count || 0;
    keyUsage.set(apiKey.id, requests);
    totalRequests += requests;
  }

  // Get top API keys by usage
  const topApiKeys = teamApiKeys
    .map((apiKey) => ({
      keyId: apiKey.id,
      name: apiKey.name,
      requests: keyUsage.get(apiKey.id) || 0,
      percentage: totalRequests > 0 ? ((keyUsage.get(apiKey.id) || 0) / totalRequests) * 100 : 0,
    }))
    .sort((a, b) => b.requests - a.requests)
    .slice(0, 10);

  return {
    totalRequests,
    totalApiKeys,
    activeApiKeys,
    topApiKeys,
  };
}

// ============================================================================
// Expiration Management Functions
// ============================================================================

/**
 * Get comprehensive expiration status for API keys
 *
 * @param options - Filtering and configuration options
 * @returns Detailed expiration status for matching keys
 */
export async function getApiKeyExpirationStatus(
  options: {
    userId?: string;
    teamId?: string;
    daysAhead?: number;
    includeExpired?: boolean;
    includeGracePeriod?: boolean;
    gracePeriodDays?: number;
  } = {}
): Promise<IApiKeyExpirationStatus[]> {
  const db = await getDB();
  const {
    userId,
    teamId,
    daysAhead = 30,
    includeExpired = true,
    includeGracePeriod = true,
    gracePeriodDays = 7,
  } = options;

  const now = new Date();
  const futureThreshold = new Date(now.getTime() + daysAhead * 24 * 60 * 60 * 1000);
  const gracePeriodThreshold = new Date(now.getTime() - gracePeriodDays * 24 * 60 * 60 * 1000);

  // Build where conditions
  const whereConditions = [isNull(schema.apiKeys.deletedAt), eq(schema.apiKeys.isActive, true)];

  if (userId) {
    whereConditions.push(eq(schema.apiKeys.userId, userId));
  }

  if (teamId) {
    whereConditions.push(eq(schema.apiKeys.teamId, teamId));
  }

  // Add expiration filter - only keys with expiration dates
  whereConditions.push(sql`${schema.apiKeys.expiresAt} IS NOT NULL`);

  // Add time-based conditions
  const timeConditions = [];

  if (includeExpired) {
    if (includeGracePeriod) {
      // Include all expired keys (both within and outside grace period)
      timeConditions.push(lte(schema.apiKeys.expiresAt, now));
    } else {
      // Include only expired keys within grace period
      timeConditions.push(
        and(lte(schema.apiKeys.expiresAt, now), gte(schema.apiKeys.expiresAt, gracePeriodThreshold))
      );
    }
  }

  // Keys expiring in the future within the threshold
  timeConditions.push(
    and(gte(schema.apiKeys.expiresAt, now), lte(schema.apiKeys.expiresAt, futureThreshold))
  );

  if (timeConditions.length > 0) {
    whereConditions.push(sql`(${timeConditions.map(() => '?').join(' OR ')})`);
  }

  // Get API keys with user and team information
  const apiKeys = await db.query.apiKeys.findMany({
    where: and(...whereConditions),
    with: {
      user: {
        columns: {
          email: true,
          name: true,
        },
      },
      team: {
        columns: {
          name: true,
        },
      },
    },
    orderBy: [schema.apiKeys.expiresAt],
  });

  // Transform to expiration status objects
  const expirationStatuses: IApiKeyExpirationStatus[] = [];

  for (const key of apiKeys) {
    if (!key.expiresAt) continue; // Skip keys without expiration

    const expiresAt = key.expiresAt;
    const daysUntilExpiration = Math.ceil(
      (expiresAt.getTime() - now.getTime()) / (24 * 60 * 60 * 1000)
    );
    const isExpired = expiresAt <= now;
    const isInGracePeriod = isExpired && expiresAt >= gracePeriodThreshold;

    // Get notification history (placeholder for now)
    const notificationsSent: string[] = [];

    expirationStatuses.push({
      keyId: key.id,
      keyName: key.name,
      keyPrefix: key.keyPrefix,
      userId: key.userId,
      userEmail: key.user?.email || '',
      teamId: key.teamId || undefined,
      teamName: key.team?.name || undefined,
      expiresAt,
      daysUntilExpiration,
      isExpired,
      isInGracePeriod,
      lastUsed: key.lastUsedAt || undefined,
      usageCount: key.usageCount,
      usageLimit: key.usageLimit || undefined,
      notificationsSent,
    });
  }

  return expirationStatuses;
}

/**
 * Get bulk expiration summary for dashboard/reporting
 *
 * @param teamId - Optional team filter
 * @returns Summary of expiration status across all keys
 */
export async function getBulkExpirationSummary(teamId?: string): Promise<IBulkExpirationSummary> {
  const expirationStatuses = await getApiKeyExpirationStatus({
    teamId,
    daysAhead: 30,
    includeExpired: true,
    includeGracePeriod: true,
  });

  const summary: IBulkExpirationSummary = {
    totalKeys: expirationStatuses.length,
    expiredKeys: 0,
    expiringIn7Days: 0,
    expiringIn1Day: 0,
    inGracePeriod: 0,
    teamsAffected: [],
    usersAffected: [],
  };

  const teamMap = new Map<
    string,
    { teamId: string; teamName: string; expiredCount: number; expiringCount: number }
  >();
  const userMap = new Map<
    string,
    { userId: string; userEmail: string; expiredCount: number; expiringCount: number }
  >();

  for (const status of expirationStatuses) {
    // Count by status
    if (status.isExpired) {
      summary.expiredKeys++;
    }
    if (status.isInGracePeriod) {
      summary.inGracePeriod++;
    }
    if (status.daysUntilExpiration <= 7 && status.daysUntilExpiration > 0) {
      summary.expiringIn7Days++;
    }
    if (status.daysUntilExpiration <= 1 && status.daysUntilExpiration > 0) {
      summary.expiringIn1Day++;
    }

    // Track by team
    if (status.teamId && status.teamName) {
      const teamKey = status.teamId;
      if (!teamMap.has(teamKey)) {
        teamMap.set(teamKey, {
          teamId: status.teamId,
          teamName: status.teamName,
          expiredCount: 0,
          expiringCount: 0,
        });
      }
      const teamData = teamMap.get(teamKey);
      if (teamData) {
        if (status.isExpired) {
          teamData.expiredCount++;
        } else if (status.daysUntilExpiration <= 7) {
          teamData.expiringCount++;
        }
      }
    }

    // Track by user
    const userKey = status.userId;
    if (!userMap.has(userKey)) {
      userMap.set(userKey, {
        userId: status.userId,
        userEmail: status.userEmail,
        expiredCount: 0,
        expiringCount: 0,
      });
    }
    const userData = userMap.get(userKey);
    if (userData) {
      if (status.isExpired) {
        userData.expiredCount++;
      } else if (status.daysUntilExpiration <= 7) {
        userData.expiringCount++;
      }
    }
  }

  summary.teamsAffected = Array.from(teamMap.values());
  summary.usersAffected = Array.from(userMap.values());

  return summary;
}

/**
 * Check and enforce API key expiration with grace period support
 *
 * @param keyHash - API key hash to check
 * @param gracePeriodDays - Grace period in days (default: 7)
 * @returns API key if valid, null if expired beyond grace period
 */
export async function checkApiKeyWithGracePeriod(
  keyHash: string,
  gracePeriodDays = 7
): Promise<TApiKey | null> {
  const db = await getDB();

  const apiKey = await db.query.apiKeys.findFirst({
    where: and(
      eq(schema.apiKeys.keyHash, keyHash),
      eq(schema.apiKeys.isActive, true),
      isNull(schema.apiKeys.deletedAt)
    ),
  });

  if (!apiKey) return null;

  // Check usage limit
  if (apiKey.usageLimit && apiKey.usageCount >= apiKey.usageLimit) {
    return null;
  }

  // If no expiration date, key is valid
  if (!apiKey.expiresAt) return apiKey;

  const now = new Date();
  const gracePeriodThreshold = new Date(now.getTime() - gracePeriodDays * 24 * 60 * 60 * 1000);

  // Check if key is within grace period
  if (apiKey.expiresAt < gracePeriodThreshold) {
    return null; // Expired beyond grace period
  }

  return apiKey;
}

/**
 * Extend expiration date for API keys
 *
 * @param keyIds - Array of API key IDs to extend
 * @param extensionDays - Number of days to extend (or new expiration date)
 * @param newExpirationDate - Specific new expiration date (overrides extensionDays)
 * @returns Updated API keys
 */
export async function extendApiKeyExpiration(
  keyIds: string[],
  extensionDays?: number,
  newExpirationDate?: Date
): Promise<TApiKey[]> {
  const db = await getDB();

  if (!extensionDays && !newExpirationDate) {
    throw new Error('Either extensionDays or newExpirationDate must be provided');
  }

  // Calculate new expiration date
  let expiresAt: Date;
  if (newExpirationDate) {
    expiresAt = newExpirationDate;
  } else {
    expiresAt = new Date(Date.now() + (extensionDays || 0) * 24 * 60 * 60 * 1000);
  }

  const updatedKeys = await db
    .update(schema.apiKeys)
    .set({
      expiresAt,
      updatedAt: new Date(),
    })
    .where(and(inArray(schema.apiKeys.id, keyIds), isNull(schema.apiKeys.deletedAt)))
    .returning();

  return updatedKeys;
}

/**
 * Automatically disable expired API keys beyond grace period
 *
 * @param gracePeriodDays - Grace period in days
 * @returns Number of keys disabled
 */
export async function disableExpiredApiKeys(gracePeriodDays = 7): Promise<{
  disabledCount: number;
  disabledKeys: Array<{ id: string; name: string; userEmail: string }>;
}> {
  const db = await getDB();

  const gracePeriodThreshold = new Date(Date.now() - gracePeriodDays * 24 * 60 * 60 * 1000);

  // Find expired keys beyond grace period
  const expiredKeys = await db.query.apiKeys.findMany({
    where: and(
      eq(schema.apiKeys.isActive, true),
      isNull(schema.apiKeys.deletedAt),
      lte(schema.apiKeys.expiresAt, gracePeriodThreshold)
    ),
    with: {
      user: {
        columns: {
          email: true,
        },
      },
    },
  });

  if (expiredKeys.length === 0) {
    return { disabledCount: 0, disabledKeys: [] };
  }

  const keyIds = expiredKeys.map((key) => key.id);

  // Disable the expired keys
  await db
    .update(schema.apiKeys)
    .set({
      isActive: false,
      updatedAt: new Date(),
    })
    .where(inArray(schema.apiKeys.id, keyIds));

  const disabledKeys = expiredKeys.map((key) => ({
    id: key.id,
    name: key.name,
    userEmail: key.user?.email || '',
  }));

  return {
    disabledCount: expiredKeys.length,
    disabledKeys,
  };
}

/**
 * Send expiration notifications for API keys
 *
 * @param warningDays - Array of days before expiration to send warnings (e.g., [7, 1])
 * @returns Summary of notifications sent
 */
export async function sendExpirationNotifications(warningDays: number[] = [7, 1]): Promise<{
  totalNotifications: number;
  successfulNotifications: number;
  failedNotifications: number;
  notificationsByType: Record<string, number>;
}> {
  const summary = {
    totalNotifications: 0,
    successfulNotifications: 0,
    failedNotifications: 0,
    notificationsByType: {} as Record<string, number>,
  };

  for (const days of warningDays) {
    const expiringKeys = await getExpiringApiKeys(days);

    for (const keyWithStats of expiringKeys) {
      if (!keyWithStats.expiresAt) continue;

      summary.totalNotifications++;

      try {
        // Build email context
        const context: IApiKeyEmailContext = {
          userName: keyWithStats.user?.name || 'User',
          userEmail: keyWithStats.user?.email || '',
          keyName: keyWithStats.name,
          keyPrefix: keyWithStats.keyPrefix,
          keyId: keyWithStats.id,
          teamName: keyWithStats.team?.name,
          teamId: keyWithStats.teamId || undefined,
          expiresAt: keyWithStats.expiresAt,
          daysUntilExpiration: days,
          manageUrl: `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/api-keys/${keyWithStats.id}`,
          rotateUrl: `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/api-keys/${keyWithStats.id}/rotate`,
          teamUrl: keyWithStats.teamId
            ? `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/teams/${keyWithStats.teamId}`
            : undefined,
          permissions: keyWithStats.effectivePermissions,
          usageStats: keyWithStats.usageStats,
        };

        // Determine notification type
        const notificationType = days === 1 ? 'api_key_expiring_1_day' : 'api_key_expiring_7_days';

        // Queue notification
        await notificationService.queueNotification(
          notificationType,
          keyWithStats.userId,
          keyWithStats.user?.email || '',
          context,
          {
            teamId: keyWithStats.teamId || undefined,
            priority: days === 1 ? 'high' : 'normal',
          }
        );

        summary.successfulNotifications++;
        summary.notificationsByType[notificationType] =
          (summary.notificationsByType[notificationType] || 0) + 1;
      } catch (error) {
        console.error(`Failed to queue notification for API key ${keyWithStats.id}:`, error);
        summary.failedNotifications++;
      }
    }
  }

  return summary;
}

/**
 * Enhanced API key rotation with scheduling and overlap support
 *
 * @param keyId - API key ID to rotate
 * @param options - Rotation options
 * @returns Enhanced rotation result
 */
export async function rotateApiKeyEnhanced(
  keyId: string,
  options: {
    overlapPeriodHours?: number;
    rotationType?: 'automatic' | 'policy' | 'expiration' | 'manual';
    reason?: string;
    rotatedBy?: string;
    ipAddress?: string;
    userAgent?: string;
    sendNotification?: boolean;
    newExpirationDate?: Date;
  } = {}
): Promise<{
  success: boolean;
  apiKey?: TApiKey;
  key?: string;
  oldKeyId?: string;
  rotationHistory?: TApiKeyRotationHistory;
  overlapEndsAt?: Date;
  error?: string;
}> {
  const db = await getDB();

  try {
    // Get existing API key
    const existingKey = await db.query.apiKeys.findFirst({
      where: and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)),
      with: {
        user: {
          columns: {
            email: true,
            name: true,
          },
        },
        team: {
          columns: {
            name: true,
            slug: true,
          },
        },
      },
    });

    if (!existingKey) {
      return { success: false, error: 'API key not found' };
    }

    // Generate new API key
    const { key, hash, prefix } = await generateApiKey();

    // Calculate new expiration date
    let newExpiresAt = existingKey.expiresAt;
    if (options.newExpirationDate) {
      newExpiresAt = options.newExpirationDate;
    } else if (existingKey.expiresAt) {
      // Extend expiration by the same duration as the original key
      const originalDuration = existingKey.expiresAt.getTime() - existingKey.createdAt.getTime();
      newExpiresAt = new Date(Date.now() + originalDuration);
    }

    // Calculate overlap end time
    const overlapPeriodHours = options.overlapPeriodHours || 24;
    const overlapEndsAt = new Date(Date.now() + overlapPeriodHours * 60 * 60 * 1000);

    // Create new API key with same properties
    const newApiKey: TNewApiKey = {
      userId: existingKey.userId,
      teamId: existingKey.teamId,
      name: `${existingKey.name} (Rotated)`,
      keyHash: hash,
      keyPrefix: prefix,
      permissions: existingKey.permissions,
      scopedPermissions: existingKey.scopedPermissions,
      usageLimit: existingKey.usageLimit,
      usageCount: 0,
      expiresAt: newExpiresAt,
      isActive: true,
    };

    // Perform rotation in transaction with overlap support
    const result = await db.transaction(async (tx) => {
      // Insert new key
      const [inserted] = await tx.insert(schema.apiKeys).values(newApiKey).returning();

      // Instead of immediately disabling the old key, schedule it for deactivation
      // after the overlap period (for seamless transition)
      if (overlapPeriodHours > 0) {
        // Mark old key for deactivation after overlap period
        await tx
          .update(schema.apiKeys)
          .set({
            // Add a custom field to track overlap end time
            // For now, we'll just update the updatedAt field
            updatedAt: new Date(),
          })
          .where(eq(schema.apiKeys.id, keyId));
      } else {
        // Immediate deactivation (no overlap)
        await tx
          .update(schema.apiKeys)
          .set({
            deletedAt: new Date(),
            updatedAt: new Date(),
            isActive: false,
          })
          .where(eq(schema.apiKeys.id, keyId));
      }

      // Create rotation history record
      const rotationHistoryData: TNewApiKeyRotationHistory = {
        oldKeyId: keyId,
        newKeyId: inserted.id,
        userId: existingKey.userId,
        teamId: existingKey.teamId,
        rotationType: options.rotationType || 'manual',
        reason: options.reason || 'Manual rotation',
        oldKeyName: existingKey.name,
        oldKeyPrefix: existingKey.keyPrefix,
        newKeyPrefix: prefix,
        rotatedBy: options.rotatedBy || null,
        ipAddress: options.ipAddress || null,
        userAgent: options.userAgent || null,
      };

      const [rotationHistory] = await tx
        .insert(schema.apiKeyRotationHistory)
        .values(rotationHistoryData)
        .returning();

      return { newKey: inserted, rotationHistory };
    });

    // Send notification if requested
    if (options.sendNotification !== false && existingKey.user?.email) {
      try {
        const context: IApiKeyEmailContext = {
          userName: existingKey.user.name || 'User',
          userEmail: existingKey.user.email,
          keyName: existingKey.name,
          keyPrefix: existingKey.keyPrefix,
          keyId: existingKey.id,
          teamName: existingKey.team?.name,
          teamId: existingKey.teamId || undefined,
          rotatedAt: new Date(),
          newKeyPrefix: prefix,
          expiresAt: newExpiresAt || undefined,
          manageUrl: `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/api-keys/${result.newKey.id}`,
          rotateUrl: `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/api-keys/${result.newKey.id}/rotate`,
          teamUrl: existingKey.teamId
            ? `${process.env.BASE_URL || 'http://localhost:8787'}/dashboard/teams/${existingKey.teamId}`
            : undefined,
        };

        await notificationService.queueNotification(
          'api_key_rotated',
          existingKey.userId,
          existingKey.user.email,
          context,
          {
            teamId: existingKey.teamId || undefined,
            priority: 'normal',
          }
        );
      } catch (notificationError) {
        console.error('Failed to queue rotation notification:', notificationError);
        // Don't fail the rotation due to notification errors
      }
    }

    return {
      success: true,
      apiKey: result.newKey,
      key,
      oldKeyId: keyId,
      rotationHistory: result.rotationHistory,
      overlapEndsAt,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Clean up old API keys after overlap period expires
 * This should be run periodically (e.g., via cron job)
 *
 * @returns Number of keys cleaned up
 */
export async function cleanupExpiredOverlapKeys(): Promise<{
  cleanedUpCount: number;
  errors: string[];
}> {
  const db = await getDB();
  const errors: string[] = [];
  let cleanedUpCount = 0;

  try {
    // Find rotation history entries where overlap period should have ended
    // This is a simplified approach - in a full implementation, you'd track overlap end times
    const rotationHistory = await db.query.apiKeyRotationHistory.findMany({
      where: lte(
        schema.apiKeyRotationHistory.rotatedAt,
        new Date(Date.now() - 24 * 60 * 60 * 1000)
      ), // 24 hours ago
      limit: 100, // Process in batches
    });

    for (const rotation of rotationHistory) {
      try {
        // Check if the old key still exists and is active
        const oldKey = await db.query.apiKeys.findFirst({
          where: and(
            eq(schema.apiKeys.id, rotation.oldKeyId),
            eq(schema.apiKeys.isActive, true),
            isNull(schema.apiKeys.deletedAt)
          ),
        });

        if (oldKey) {
          // Deactivate the old key
          await db
            .update(schema.apiKeys)
            .set({
              isActive: false,
              deletedAt: new Date(),
              updatedAt: new Date(),
            })
            .where(eq(schema.apiKeys.id, rotation.oldKeyId));

          cleanedUpCount++;
        }
      } catch (error) {
        errors.push(
          `Failed to cleanup key ${rotation.oldKeyId}: ${error instanceof Error ? error.message : String(error)}`
        );
      }
    }
  } catch (error) {
    errors.push(
      `Failed to query rotation history: ${error instanceof Error ? error.message : String(error)}`
    );
  }

  return { cleanedUpCount, errors };
}
