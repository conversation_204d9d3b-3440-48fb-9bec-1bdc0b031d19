/**
 * API Key Analytics Service
 *
 * Enhanced analytics service for API key usage tracking, permission analytics,
 * billing integration, and comprehensive reporting capabilities.
 */

import type { TA<PERSON><PERSON><PERSON>, TAuditLog } from '@dbSchema';
import * as schema from '@dbSchema';
import getDB from '@utils/getDB';
import { and, desc, eq, gte, isNull, lte, sql } from 'drizzle-orm';
import type { TPermission } from '@/types/teams';

// ============================================================================
// Analytics Types
// ============================================================================

/**
 * Time period for analytics
 */
export type TAnalyticsPeriod = 'hour' | 'day' | 'week' | 'month' | 'year' | 'custom';

/**
 * Analytics aggregation level
 */
export type TAnalyticsAggregation = 'raw' | 'hourly' | 'daily' | 'weekly' | 'monthly';

/**
 * Permission usage analytics
 */
export interface IPermissionUsageAnalytics {
  permission: TPermission;
  requestCount: number;
  successCount: number;
  errorCount: number;
  averageResponseTime: number;
  lastUsed: Date | null;
  usagePercentage: number;
}

/**
 * Endpoint usage analytics
 */
export interface IEndpointUsageAnalytics {
  endpoint: string;
  method: string;
  requestCount: number;
  successCount: number;
  errorCount: number;
  averageResponseTime: number;
  usagePercentage: number;
  permissionsUsed: TPermission[];
}

/**
 * Error analytics
 */
export interface IErrorAnalytics {
  errorType: string;
  errorMessage: string;
  count: number;
  percentage: number;
  firstOccurrence: Date;
  lastOccurrence: Date;
  affectedEndpoints: string[];
}

/**
 * Usage pattern analytics
 */
export interface IUsagePatternAnalytics {
  hourlyDistribution: Array<{ hour: number; requests: number }>;
  dailyDistribution: Array<{ day: string; requests: number }>;
  weeklyDistribution: Array<{ week: string; requests: number }>;
  peakUsageHour: number;
  peakUsageDay: string;
  averageRequestsPerHour: number;
  averageRequestsPerDay: number;
}

/**
 * Billing analytics
 */
export interface IBillingAnalytics {
  totalRequests: number;
  billableRequests: number;
  freeRequests: number;
  overage: number;
  estimatedCost: number;
  costPerRequest: number;
  projectedMonthlyUsage: number;
  projectedMonthlyCost: number;
}

/**
 * Comprehensive API key analytics
 */
export interface IComprehensiveApiKeyAnalytics {
  keyId: string;
  keyName: string;
  period: {
    start: Date;
    end: Date;
    days: number;
  };
  overview: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    successRate: number;
    averageResponseTime: number;
    uniqueEndpoints: number;
    uniquePermissions: number;
  };
  permissionUsage: IPermissionUsageAnalytics[];
  endpointUsage: IEndpointUsageAnalytics[];
  errorAnalytics: IErrorAnalytics[];
  usagePatterns: IUsagePatternAnalytics;
  billingAnalytics: IBillingAnalytics;
  trends: {
    requestTrend: 'increasing' | 'decreasing' | 'stable';
    errorTrend: 'increasing' | 'decreasing' | 'stable';
    performanceTrend: 'improving' | 'degrading' | 'stable';
  };
}

/**
 * Team analytics summary
 */
export interface ITeamAnalyticsSummary {
  teamId: string;
  teamName: string;
  period: {
    start: Date;
    end: Date;
    days: number;
  };
  overview: {
    totalApiKeys: number;
    activeApiKeys: number;
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    successRate: number;
  };
  topApiKeys: Array<{
    keyId: string;
    keyName: string;
    requests: number;
    percentage: number;
  }>;
  topEndpoints: Array<{
    endpoint: string;
    requests: number;
    percentage: number;
  }>;
  topErrors: Array<{
    errorType: string;
    count: number;
    percentage: number;
  }>;
  billingAnalytics: IBillingAnalytics;
}

// ============================================================================
// Analytics Service Class
// ============================================================================

/**
 * API Key Analytics Service
 */
export class ApiKeyAnalyticsService {
  /**
   * Get comprehensive analytics for an API key
   *
   * @param keyId - API key ID
   * @param startDate - Start date for analytics
   * @param endDate - End date for analytics
   * @returns Comprehensive analytics
   */
  async getComprehensiveAnalytics(
    keyId: string,
    startDate: Date,
    endDate: Date
  ): Promise<IComprehensiveApiKeyAnalytics | null> {
    const db = await getDB();

    // Check if API key exists
    const apiKey = await db.query.apiKeys.findFirst({
      where: and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)),
    });

    if (!apiKey) {
      return null;
    }

    // Get audit logs for the period
    const auditLogs = await db.query.auditLogs.findMany({
      where: and(
        eq(schema.auditLogs.resourceId, keyId),
        eq(schema.auditLogs.action, 'api_key.use'),
        gte(schema.auditLogs.createdAt, startDate),
        lte(schema.auditLogs.createdAt, endDate)
      ),
      orderBy: desc(schema.auditLogs.createdAt),
    });

    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    // Calculate overview metrics
    const overview = this.calculateOverviewMetrics(auditLogs);

    // Calculate permission usage
    const permissionUsage = this.calculatePermissionUsage(auditLogs);

    // Calculate endpoint usage
    const endpointUsage = this.calculateEndpointUsage(auditLogs);

    // Calculate error analytics
    const errorAnalytics = this.calculateErrorAnalytics(auditLogs);

    // Calculate usage patterns
    const usagePatterns = this.calculateUsagePatterns(auditLogs, startDate, endDate);

    // Calculate billing analytics
    const billingAnalytics = await this.calculateBillingAnalytics(keyId, auditLogs, apiKey);

    // Calculate trends
    const trends = this.calculateTrends(auditLogs, days);

    return {
      keyId,
      keyName: apiKey.name,
      period: {
        start: startDate,
        end: endDate,
        days,
      },
      overview,
      permissionUsage,
      endpointUsage,
      errorAnalytics,
      usagePatterns,
      billingAnalytics,
      trends,
    };
  }

  /**
   * Get team analytics summary
   *
   * @param teamId - Team ID
   * @param startDate - Start date for analytics
   * @param endDate - End date for analytics
   * @returns Team analytics summary
   */
  async getTeamAnalyticsSummary(
    teamId: string,
    startDate: Date,
    endDate: Date
  ): Promise<ITeamAnalyticsSummary | null> {
    const db = await getDB();

    // Get team information
    const team = await db.query.teams.findFirst({
      where: and(eq(schema.teams.id, teamId), isNull(schema.teams.deletedAt)),
    });

    if (!team) {
      return null;
    }

    // Get team's API keys
    const teamApiKeys = await db.query.apiKeys.findMany({
      where: and(eq(schema.apiKeys.teamId, teamId), isNull(schema.apiKeys.deletedAt)),
    });

    const keyIds = teamApiKeys.map((key) => key.id);
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    // Get audit logs for all team API keys
    const auditLogs = await db.query.auditLogs.findMany({
      where: and(
        sql`${schema.auditLogs.resourceId} = ANY(${keyIds})`,
        eq(schema.auditLogs.action, 'api_key.use'),
        gte(schema.auditLogs.createdAt, startDate),
        lte(schema.auditLogs.createdAt, endDate)
      ),
      orderBy: desc(schema.auditLogs.createdAt),
    });

    // Calculate overview metrics
    const overview = {
      totalApiKeys: teamApiKeys.length,
      activeApiKeys: teamApiKeys.filter((key) => key.isActive).length,
      ...this.calculateOverviewMetrics(auditLogs),
    };

    // Calculate top API keys
    const topApiKeys = this.calculateTopApiKeys(auditLogs, teamApiKeys);

    // Calculate top endpoints
    const topEndpoints = this.calculateTopEndpoints(auditLogs);

    // Calculate top errors
    const topErrors = this.calculateTopErrors(auditLogs);

    // Calculate billing analytics for the team
    const billingAnalytics = await this.calculateTeamBillingAnalytics(teamId, auditLogs);

    return {
      teamId,
      teamName: team.name,
      period: {
        start: startDate,
        end: endDate,
        days,
      },
      overview,
      topApiKeys,
      topEndpoints,
      topErrors,
      billingAnalytics,
    };
  }

  // ============================================================================
  // Private Calculation Methods
  // ============================================================================

  /**
   * Calculate overview metrics from audit logs
   */
  private calculateOverviewMetrics(auditLogs: TAuditLog[]) {
    const totalRequests = auditLogs.length;
    const successfulRequests = auditLogs.filter((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        return !details.error;
      } catch {
        return true; // Assume success if details can't be parsed
      }
    }).length;
    const failedRequests = totalRequests - successfulRequests;
    const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0;

    // Calculate average response time
    let totalResponseTime = 0;
    let responseTimeCount = 0;
    auditLogs.forEach((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        if (details.responseTime && typeof details.responseTime === 'number') {
          totalResponseTime += details.responseTime;
          responseTimeCount++;
        }
      } catch {
        // Ignore parsing errors
      }
    });
    const averageResponseTime = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0;

    // Calculate unique endpoints and permissions
    const uniqueEndpoints = new Set();
    const uniquePermissions = new Set();
    auditLogs.forEach((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        if (details.endpoint) uniqueEndpoints.add(details.endpoint);
        if (details.permission) uniquePermissions.add(details.permission);
      } catch {
        // Ignore parsing errors
      }
    });

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      successRate,
      averageResponseTime,
      uniqueEndpoints: uniqueEndpoints.size,
      uniquePermissions: uniquePermissions.size,
    };
  }

  /**
   * Calculate permission usage analytics
   */
  private calculatePermissionUsage(auditLogs: TAuditLog[]): IPermissionUsageAnalytics[] {
    const permissionStats = new Map<
      TPermission,
      {
        total: number;
        success: number;
        error: number;
        responseTimes: number[];
        lastUsed: Date | null;
      }
    >();

    auditLogs.forEach((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        const permission = details.permission as TPermission;
        if (!permission) return;

        const stats = permissionStats.get(permission) || {
          total: 0,
          success: 0,
          error: 0,
          responseTimes: [],
          lastUsed: null,
        };

        stats.total++;
        if (details.error) {
          stats.error++;
        } else {
          stats.success++;
        }

        if (details.responseTime && typeof details.responseTime === 'number') {
          stats.responseTimes.push(details.responseTime);
        }

        if (!stats.lastUsed || log.createdAt > stats.lastUsed) {
          stats.lastUsed = log.createdAt;
        }

        permissionStats.set(permission, stats);
      } catch {
        // Ignore parsing errors
      }
    });

    const totalRequests = auditLogs.length;
    return Array.from(permissionStats.entries())
      .map(([permission, stats]) => ({
        permission,
        requestCount: stats.total,
        successCount: stats.success,
        errorCount: stats.error,
        averageResponseTime:
          stats.responseTimes.length > 0
            ? stats.responseTimes.reduce((sum, time) => sum + time, 0) / stats.responseTimes.length
            : 0,
        lastUsed: stats.lastUsed,
        usagePercentage: totalRequests > 0 ? (stats.total / totalRequests) * 100 : 0,
      }))
      .sort((a, b) => b.requestCount - a.requestCount);
  }

  /**
   * Calculate endpoint usage analytics
   */
  private calculateEndpointUsage(auditLogs: TAuditLog[]): IEndpointUsageAnalytics[] {
    const endpointStats = new Map<
      string,
      {
        method: string;
        total: number;
        success: number;
        error: number;
        responseTimes: number[];
        permissions: Set<TPermission>;
      }
    >();

    auditLogs.forEach((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        const endpoint = details.endpoint;
        const method = details.method || 'GET';
        if (!endpoint) return;

        const key = `${method} ${endpoint}`;
        const stats = endpointStats.get(key) || {
          method,
          total: 0,
          success: 0,
          error: 0,
          responseTimes: [] as number[],
          permissions: new Set<TPermission>(),
        };

        stats.total++;
        if (details.error) {
          stats.error++;
        } else {
          stats.success++;
        }

        if (details.responseTime && typeof details.responseTime === 'number') {
          stats.responseTimes.push(details.responseTime as number);
        }

        if (details.permission) {
          stats.permissions.add(details.permission as TPermission);
        }

        endpointStats.set(key, stats);
      } catch {
        // Ignore parsing errors
      }
    });

    const totalRequests = auditLogs.length;
    return Array.from(endpointStats.entries())
      .map(([endpointKey, stats]) => {
        const [method, ...endpointParts] = endpointKey.split(' ');
        const endpoint = endpointParts.join(' ');

        return {
          endpoint,
          method,
          requestCount: stats.total,
          successCount: stats.success,
          errorCount: stats.error,
          averageResponseTime:
            stats.responseTimes.length > 0
              ? stats.responseTimes.reduce((sum, time) => sum + time, 0) /
                stats.responseTimes.length
              : 0,
          usagePercentage: totalRequests > 0 ? (stats.total / totalRequests) * 100 : 0,
          permissionsUsed: Array.from(stats.permissions),
        };
      })
      .sort((a, b) => b.requestCount - a.requestCount);
  }

  /**
   * Calculate error analytics
   */
  private calculateErrorAnalytics(auditLogs: TAuditLog[]): IErrorAnalytics[] {
    const errorStats = new Map<
      string,
      {
        message: string;
        count: number;
        firstOccurrence: Date;
        lastOccurrence: Date;
        endpoints: Set<string>;
      }
    >();

    auditLogs.forEach((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        if (!details.error) return;

        const errorType = details.errorType || 'unknown';
        const errorMessage = details.error || 'Unknown error';
        const endpoint = details.endpoint || 'unknown';

        const stats = errorStats.get(errorType) || {
          message: errorMessage,
          count: 0,
          firstOccurrence: log.createdAt,
          lastOccurrence: log.createdAt,
          endpoints: new Set(),
        };

        stats.count++;
        stats.endpoints.add(endpoint);

        if (log.createdAt < stats.firstOccurrence) {
          stats.firstOccurrence = log.createdAt;
        }
        if (log.createdAt > stats.lastOccurrence) {
          stats.lastOccurrence = log.createdAt;
        }

        errorStats.set(errorType, stats);
      } catch {
        // Ignore parsing errors
      }
    });

    const totalErrors = Array.from(errorStats.values()).reduce(
      (sum, stats) => sum + stats.count,
      0
    );
    return Array.from(errorStats.entries())
      .map(([errorType, stats]) => ({
        errorType,
        errorMessage: stats.message,
        count: stats.count,
        percentage: totalErrors > 0 ? (stats.count / totalErrors) * 100 : 0,
        firstOccurrence: stats.firstOccurrence,
        lastOccurrence: stats.lastOccurrence,
        affectedEndpoints: Array.from(stats.endpoints),
      }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Calculate usage patterns
   */
  private calculateUsagePatterns(
    auditLogs: TAuditLog[],
    startDate: Date,
    endDate: Date
  ): IUsagePatternAnalytics {
    const hourlyStats = new Array(24).fill(0);
    const dailyStats = new Map<string, number>();
    const weeklyStats = new Map<string, number>();

    auditLogs.forEach((log) => {
      const date = new Date(log.createdAt);
      const hour = date.getHours();
      const day = date.toISOString().split('T')[0];
      const week = this.getWeekKey(date);

      hourlyStats[hour]++;
      dailyStats.set(day, (dailyStats.get(day) || 0) + 1);
      weeklyStats.set(week, (weeklyStats.get(week) || 0) + 1);
    });

    const hourlyDistribution = hourlyStats.map((requests, hour) => ({ hour, requests }));
    const dailyDistribution = Array.from(dailyStats.entries()).map(([day, requests]) => ({
      day,
      requests,
    }));
    const weeklyDistribution = Array.from(weeklyStats.entries()).map(([week, requests]) => ({
      week,
      requests,
    }));

    const peakUsageHour = hourlyStats.indexOf(Math.max(...hourlyStats));
    const peakUsageDay = Array.from(dailyStats.entries()).reduce(
      (max, [day, requests]) => (requests > max.requests ? { day, requests } : max),
      { day: '', requests: 0 }
    ).day;

    const totalHours = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60));
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const averageRequestsPerHour = totalHours > 0 ? auditLogs.length / totalHours : 0;
    const averageRequestsPerDay = totalDays > 0 ? auditLogs.length / totalDays : 0;

    return {
      hourlyDistribution,
      dailyDistribution,
      weeklyDistribution,
      peakUsageHour,
      peakUsageDay,
      averageRequestsPerHour,
      averageRequestsPerDay,
    };
  }

  /**
   * Calculate billing analytics
   */
  private async calculateBillingAnalytics(
    _keyId: string,
    auditLogs: TAuditLog[],
    apiKey: TApiKey
  ): Promise<IBillingAnalytics> {
    const totalRequests = auditLogs.length;
    const billableRequests = totalRequests; // All requests are billable for now
    const freeRequests = 0; // No free tier for now
    const overage = Math.max(0, totalRequests - (apiKey.usageLimit || 0));

    // Basic cost calculation - $0.001 per request
    const costPerRequest = 0.001;
    const estimatedCost = billableRequests * costPerRequest;

    // Project monthly usage based on current usage
    const daysInPeriod =
      auditLogs.length > 0
        ? Math.ceil(
            (Date.now() - auditLogs[auditLogs.length - 1].createdAt.getTime()) /
              (1000 * 60 * 60 * 24)
          )
        : 1;
    const dailyAverage = totalRequests / Math.max(daysInPeriod, 1);
    const projectedMonthlyUsage = dailyAverage * 30;
    const projectedMonthlyCost = projectedMonthlyUsage * costPerRequest;

    return {
      totalRequests,
      billableRequests,
      freeRequests,
      overage,
      estimatedCost,
      costPerRequest,
      projectedMonthlyUsage,
      projectedMonthlyCost,
    };
  }

  /**
   * Calculate trends
   */
  private calculateTrends(
    auditLogs: TAuditLog[],
    days: number
  ): {
    requestTrend: 'increasing' | 'decreasing' | 'stable';
    errorTrend: 'increasing' | 'decreasing' | 'stable';
    performanceTrend: 'improving' | 'degrading' | 'stable';
  } {
    if (auditLogs.length < 2 || days < 2) {
      return {
        requestTrend: 'stable',
        errorTrend: 'stable',
        performanceTrend: 'stable',
      };
    }

    const midPoint = Math.floor(auditLogs.length / 2);
    const firstHalf = auditLogs.slice(midPoint);
    const secondHalf = auditLogs.slice(0, midPoint);

    // Request trend
    const firstHalfRequests = firstHalf.length;
    const secondHalfRequests = secondHalf.length;
    const requestTrend =
      secondHalfRequests > firstHalfRequests * 1.1
        ? 'increasing'
        : secondHalfRequests < firstHalfRequests * 0.9
          ? 'decreasing'
          : 'stable';

    // Error trend
    const firstHalfErrors = firstHalf.filter((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        return !!details.error;
      } catch {
        return false;
      }
    }).length;
    const secondHalfErrors = secondHalf.filter((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        return !!details.error;
      } catch {
        return false;
      }
    }).length;
    const errorTrend =
      secondHalfErrors > firstHalfErrors * 1.1
        ? 'increasing'
        : secondHalfErrors < firstHalfErrors * 0.9
          ? 'decreasing'
          : 'stable';

    // Performance trend (response time)
    const getAverageResponseTime = (logs: TAuditLog[]) => {
      const responseTimes = logs
        .map((log) => {
          try {
            const details = JSON.parse((log.details as string) || '{}');
            return details.responseTime || 0;
          } catch {
            return 0;
          }
        })
        .filter((time) => time > 0);
      return responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0;
    };

    const firstHalfAvgTime = getAverageResponseTime(firstHalf);
    const secondHalfAvgTime = getAverageResponseTime(secondHalf);
    const performanceTrend =
      secondHalfAvgTime < firstHalfAvgTime * 0.9
        ? 'improving'
        : secondHalfAvgTime > firstHalfAvgTime * 1.1
          ? 'degrading'
          : 'stable';

    return {
      requestTrend,
      errorTrend,
      performanceTrend,
    };
  }

  /**
   * Calculate top API keys for team analytics
   */
  private calculateTopApiKeys(
    auditLogs: TAuditLog[],
    teamApiKeys: TApiKey[]
  ): Array<{
    keyId: string;
    keyName: string;
    requests: number;
    percentage: number;
  }> {
    const keyStats = new Map<string, number>();

    auditLogs.forEach((log) => {
      const keyId = log.resourceId;
      if (keyId) {
        keyStats.set(keyId, (keyStats.get(keyId) || 0) + 1);
      }
    });

    const totalRequests = auditLogs.length;
    return teamApiKeys
      .map((apiKey) => ({
        keyId: apiKey.id,
        keyName: apiKey.name,
        requests: keyStats.get(apiKey.id) || 0,
        percentage: totalRequests > 0 ? ((keyStats.get(apiKey.id) || 0) / totalRequests) * 100 : 0,
      }))
      .filter((item) => item.requests > 0)
      .sort((a, b) => b.requests - a.requests)
      .slice(0, 10);
  }

  /**
   * Calculate top endpoints for team analytics
   */
  private calculateTopEndpoints(auditLogs: TAuditLog[]): Array<{
    endpoint: string;
    requests: number;
    percentage: number;
  }> {
    const endpointStats = new Map<string, number>();

    auditLogs.forEach((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        const endpoint = details.endpoint;
        if (endpoint) {
          endpointStats.set(endpoint, (endpointStats.get(endpoint) || 0) + 1);
        }
      } catch {
        // Ignore parsing errors
      }
    });

    const totalRequests = auditLogs.length;
    return Array.from(endpointStats.entries())
      .map(([endpoint, requests]) => ({
        endpoint,
        requests,
        percentage: totalRequests > 0 ? (requests / totalRequests) * 100 : 0,
      }))
      .sort((a, b) => b.requests - a.requests)
      .slice(0, 10);
  }

  /**
   * Calculate top errors for team analytics
   */
  private calculateTopErrors(auditLogs: TAuditLog[]): Array<{
    errorType: string;
    count: number;
    percentage: number;
  }> {
    const errorStats = new Map<string, number>();

    auditLogs.forEach((log) => {
      try {
        const details = JSON.parse((log.details as string) || '{}');
        if (details.error) {
          const errorType = details.errorType || 'unknown';
          errorStats.set(errorType, (errorStats.get(errorType) || 0) + 1);
        }
      } catch {
        // Ignore parsing errors
      }
    });

    const totalErrors = Array.from(errorStats.values()).reduce((sum, count) => sum + count, 0);
    return Array.from(errorStats.entries())
      .map(([errorType, count]) => ({
        errorType,
        count,
        percentage: totalErrors > 0 ? (count / totalErrors) * 100 : 0,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Calculate team billing analytics
   */
  private async calculateTeamBillingAnalytics(
    _teamId: string,
    auditLogs: TAuditLog[]
  ): Promise<IBillingAnalytics> {
    const totalRequests = auditLogs.length;
    const billableRequests = totalRequests;
    const freeRequests = 0;
    const overage = 0; // Team-level overage calculation would need plan information

    const costPerRequest = 0.001;
    const estimatedCost = billableRequests * costPerRequest;

    const daysInPeriod =
      auditLogs.length > 0
        ? Math.ceil(
            (Date.now() - auditLogs[auditLogs.length - 1].createdAt.getTime()) /
              (1000 * 60 * 60 * 24)
          )
        : 1;
    const dailyAverage = totalRequests / Math.max(daysInPeriod, 1);
    const projectedMonthlyUsage = dailyAverage * 30;
    const projectedMonthlyCost = projectedMonthlyUsage * costPerRequest;

    return {
      totalRequests,
      billableRequests,
      freeRequests,
      overage,
      estimatedCost,
      costPerRequest,
      projectedMonthlyUsage,
      projectedMonthlyCost,
    };
  }

  /**
   * Get week key for grouping
   */
  private getWeekKey(date: Date): string {
    const year = date.getFullYear();
    const week = Math.ceil(((date.getTime() - new Date(year, 0, 1).getTime()) / 86400000 + 1) / 7);
    return `${year}-W${week.toString().padStart(2, '0')}`;
  }
}

// ============================================================================
// Service Instance
// ============================================================================

// Lazy singleton instance
let apiKeyAnalyticsServiceInstance: ApiKeyAnalyticsService | null = null;

/**
 * Get or create API key analytics service instance (lazy initialization)
 */
export const getApiKeyAnalyticsService = (): ApiKeyAnalyticsService => {
  if (!apiKeyAnalyticsServiceInstance) {
    apiKeyAnalyticsServiceInstance = new ApiKeyAnalyticsService();
  }
  return apiKeyAnalyticsServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const apiKeyAnalyticsService = new Proxy({} as ApiKeyAnalyticsService, {
  get(_target, prop) {
    const service = getApiKeyAnalyticsService();
    const value = service[prop as keyof ApiKeyAnalyticsService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  },
});
