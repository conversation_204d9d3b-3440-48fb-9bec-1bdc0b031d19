/**
 * Search Service
 *
 * Service for performing semantic search operations with vector embeddings.
 */

import type {
  ICollectionInfo,
  ICollectionInfoResponse,
  ISearchResponse,
  ISearchResult,
  TCollectionInfoRequest,
  TEmbeddingProvider,
  TSearchRequest,
} from '@types';
import { createSearchDocumentConfig, getKV, getVDB } from '@utils';
import { EmbeddingService } from './embedding-service';

/**
 * Search Service
 *
 * Provides semantic search capabilities using vector embeddings and Cloudflare Vectorize.
 * Supports library-specific searches, caching, and multiple embedding providers.
 */
export class SearchService {
  private embeddingService: EmbeddingService;

  /**
   * Creates a new SearchService instance
   */
  constructor() {
    this.embeddingService = new EmbeddingService();
  }

  /**
   * Performs a semantic search using vector embeddings
   *
   * @param params - Search parameters including query, library filter, and limits
   * @returns Promise resolving to search results with metadata
   * @throws Error if search fails or embedding generation fails
   *
   * @example
   * ```typescript
   * const results = await searchService.performSearch({
   *   query: "React hooks",
   *   library: "react",
   *   limit: 5
   * });
   * ```
   */
  async performSearch(params: TSearchRequest): Promise<ISearchResponse> {
    const startTime = Date.now();

    try {
      // Use existing utility to get vector database
      const vdb = await getVDB();

      // Generate embedding for the query using existing AI integration
      const embedding = await this.generateEmbedding(
        params.query,
        params.embedding_provider,
        params.embedding_model
      );

      // Build filter object with all available parameters
      const filter: Record<string, string> = {};
      if (params.collection) filter.collection = params.collection;
      if (params.userID) filter.userID = params.userID;
      if (params.projectID) filter.projectID = params.projectID;
      if (params.tenantID) filter.tenantID = params.tenantID;

      // Get document query configuration with dynamic parameters
      const documentConfig = createSearchDocumentConfig(params);

      // Perform document search using existing VectorizeIndex interface
      const results = await vdb.query(embedding, {
        topK: documentConfig.topK,
        returnMetadata: documentConfig.returnMetadata,
        returnValues: documentConfig.returnValues,
        filter: Object.keys(filter).length > 0 ? filter : undefined,
      });

      const searchTime = Date.now() - startTime;

      // Format results
      const formattedResults: ISearchResult[] = results.matches.map((match) => ({
        id: match.id,
        content: this.extractContent(match.metadata || {}),
        score: match.score || 0,
      }));

      return {
        status: 'success',
        data: formattedResults,
        metadata: {
          query: params.query,
          total_results: formattedResults.length,
          search_time_ms: searchTime,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Retrieves information about a specific collection
   *
   * @param params - Collection information request parameters
   * @returns Promise resolving to collection information including document count and metadata
   * @throws Error if collection is not found or query fails
   *
   * @example
   * ```typescript
   * const info = await searchService.getCollectionInfo({
   *   collection_name: "react",
   *   include_versions: true
   * });
   * ```
   */
  async getCollectionInfo(params: TCollectionInfoRequest): Promise<ICollectionInfoResponse> {
    try {
      // Use KV-based collection listing instead of dummy embedding query
      const kvDocumentService = new (await import('./kv-document-service')).KVDocumentService();
      const documentIds = await kvDocumentService.listDocumentsByCollection(
        params.collection_name,
        1000
      );

      // Get metadata for the documents
      const metadataMap = await kvDocumentService.getMultipleDocumentMetadata(documentIds);
      const results = {
        matches: documentIds.map((id) => ({
          id,
          metadata: metadataMap.get(id) || {},
          score: 1.0,
        })),
      };

      if (results.matches.length === 0) {
        // Return empty collection info instead of throwing error
        const collectionInfo: ICollectionInfo = {
          name: params.collection_name,
          description: `Collection '${params.collection_name}' contains no indexed documents`,
          versions: params.include_versions ? [] : undefined,
          total_documents: 0,
          categories: [],
          last_updated: new Date().toISOString(),
        };

        return {
          status: 'success',
          data: collectionInfo,
          timestamp: new Date().toISOString(),
        };
      }

      // Analyze the results to build library info
      const versions = new Set<string>();
      const categories = new Set<string>();
      let lastUpdated = '';

      results.matches.forEach((match: { metadata?: Record<string, unknown> }) => {
        const metadata = match.metadata as Record<string, string> | undefined;

        if (metadata?.version) {
          versions.add(metadata.version);
        }

        if (metadata?.category) {
          categories.add(metadata.category);
        }

        if (metadata?.upload_timestamp && metadata.upload_timestamp > lastUpdated) {
          lastUpdated = metadata.upload_timestamp;
        }
      });

      const collectionInfo: ICollectionInfo = {
        name: params.collection_name,
        description: `Collection with ${results.matches.length} indexed documents`,
        versions: params.include_versions ? Array.from(versions) : undefined,
        total_documents: results.matches.length,
        categories: Array.from(categories),
        last_updated: lastUpdated || new Date().toISOString(),
      };

      return {
        status: 'success',
        data: collectionInfo,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      throw new Error(
        `Failed to get collection info: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Retrieves a list of all available collections in the vector database
   *
   * @returns Promise resolving to object containing collection names and total count
   * @throws Error if query fails
   *
   * @example
   * ```typescript
   * const { collections, total_count } = await searchService.getAvailableCollections();
   * console.log(`Found ${total_count} collections: ${collections.join(', ')}`);
   * ```
   */
  async getAvailableCollections(): Promise<{ collections: string[]; total_count: number }> {
    try {
      // Use KV storage to efficiently get all collections without dummy embedding queries
      const kv = await getKV();
      const prefix = 'vector:';

      const listResult = await kv.list({ prefix, limit: 1000 });
      const collections = new Set<string>();

      for (const key of listResult.keys) {
        const metadata = key.metadata as Record<string, string> | undefined;
        if (metadata?.collection) {
          collections.add(metadata.collection);
        }
      }

      return {
        collections: Array.from(collections).sort(),
        total_count: collections.size,
      };
    } catch (error) {
      throw new Error(
        `Failed to get available collections: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async generateEmbedding(
    text: string,
    provider?: string,
    model?: string
  ): Promise<number[]> {
    try {
      // Create custom embedding service if provider/model specified
      const embeddingService =
        provider || model
          ? EmbeddingService.withConfig({
              provider: provider as TEmbeddingProvider,
              model: model,
            })
          : this.embeddingService;

      const result = await embeddingService.generateEmbedding(text);
      return result.embedding;
    } catch (error) {
      throw new Error(
        `Embedding generation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Search documentation - alias for performSearch for backward compatibility
   */
  async searchDocumentation(params: TSearchRequest): Promise<ISearchResponse> {
    return this.performSearch(params);
  }

  /**
   * List available collections - alias for getAvailableCollections for backward compatibility
   */
  async listCollections(): Promise<string[]> {
    const result = await this.getAvailableCollections();
    return result.collections;
  }

  /**
   * Retrieves cached search results for a query and collection
   *
   * @param query - The search query string
   * @param collection - Optional collection filter
   * @returns Promise resolving to cached results or null if not found
   *
   * @example
   * ```typescript
   * const cached = await searchService.getCachedResult('React hooks', 'react');
   * if (cached) {
   *   console.log('Using cached results');
   * }
   * ```
   */
  async getCachedResult(query: string, collection?: string): Promise<ISearchResult[] | null> {
    try {
      const kv = await getKV();
      const cacheKey = `search:${query}:${collection || 'all'}`;
      const cached = await kv.get(cacheKey, 'json');

      if (cached && Array.isArray(cached)) {
        return cached as ISearchResult[];
      }

      return null;
    } catch (error) {
      // Cache errors shouldn't break search
      console.warn('Cache retrieval failed:', error);
      return null;
    }
  }

  /**
   * Stores search results in cache for future retrieval
   *
   * @param query - The search query string
   * @param results - The search results to cache
   * @param collection - Optional collection filter
   * @returns Promise that resolves when caching is complete
   *
   * @example
   * ```typescript
   * await searchService.setCachedResult('React hooks', results, 'react');
   * console.log('Results cached for 1 hour');
   * ```
   */
  async setCachedResult(
    query: string,
    results: ISearchResult[],
    collection?: string
  ): Promise<void> {
    try {
      const kv = await getKV();
      const cacheKey = `search:${query}:${collection || 'all'}`;

      // Cache for 1 hour
      await kv.put(cacheKey, JSON.stringify(results), {
        expirationTtl: 3600,
      });
    } catch (error) {
      // Cache errors shouldn't break search
      console.warn('Cache storage failed:', error);
    }
  }

  /**
   * Retrieves comprehensive statistics about the vector database
   *
   * @returns Promise resolving to database statistics including vector counts and distributions
   * @throws Error if statistics retrieval fails
   *
   * @example
   * ```typescript
   * const stats = await searchService.getSearchStats();
   * console.log(`Total vectors: ${stats.total_vectors}`);
   * console.log(`Collections: ${stats.unique_libraries}`);
   * console.log('Categories:', stats.categories);
   * ```
   */
  async getSearchStats(): Promise<{
    total_vectors: number;
    unique_libraries: number;
    categories: Record<string, number>;
    embedding_providers: Record<string, number>;
  }> {
    try {
      // Use KV storage to efficiently get statistics without dummy embedding queries
      const kv = await getKV();
      const prefix = 'vector:';

      const listResult = await kv.list({ prefix, limit: 1000 });
      const results = {
        matches: listResult.keys.map((key) => ({
          id: key.name.replace(prefix, ''),
          metadata: (key.metadata as Record<string, unknown>) || {},
          score: 1.0,
        })),
      };

      const libraries = new Set<string>();
      const categories: Record<string, number> = {};
      const providers: Record<string, number> = {};

      results.matches.forEach((match) => {
        const metadata = match.metadata || {};

        if (metadata.library) {
          libraries.add(metadata.library as string);
        }

        if (metadata.category) {
          const category = metadata.category as string;
          categories[category] = (categories[category] || 0) + 1;
        }

        if (metadata.embedding_provider) {
          const provider = metadata.embedding_provider as string;
          providers[provider] = (providers[provider] || 0) + 1;
        }
      });

      return {
        total_vectors: results.matches.length,
        unique_libraries: libraries.size,
        categories,
        embedding_providers: providers,
      };
    } catch (error) {
      throw new Error(
        `Failed to get search stats: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Extract content from vector metadata, trying multiple possible fields
   */
  private extractContent(metadata: Record<string, VectorizeVectorMetadata>): string {
    // Try different content fields in order of preference
    const contentFields = ['content', 'original_content', 'text', 'body'];

    for (const field of contentFields) {
      const value = metadata[field];
      if (typeof value === 'string' && value.trim().length > 0) {
        return value;
      }
    }

    // If no content found, return empty string
    return '';
  }
}

// Lazy singleton instance
let searchServiceInstance: SearchService | null = null;

/**
 * Get or create search service instance (lazy initialization)
 */
export const getSearchService = (): SearchService => {
  if (!searchServiceInstance) {
    searchServiceInstance = new SearchService();
  }
  return searchServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const searchService = new Proxy({} as SearchService, {
  get(_target, prop) {
    const service = getSearchService();
    const value = service[prop as keyof SearchService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  },
});
