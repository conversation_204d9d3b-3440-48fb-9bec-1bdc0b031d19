import {
  DEFAULT_MODELS,
  type ICloudflareAIEmbeddingResponse,
  type IEmbeddingConfig,
  type IEmbeddingResult,
  MODEL_DIMENSIONS,
  PROVIDER_MODELS,
  type TEmbeddingProvider,
} from '@types';
import { getAi, getEnv, getOpenAI, getVoyageAI } from '@utils';

/**
 * Embedding Service
 *
 * Provides text embedding generation using multiple providers (OpenAI, VoyageAI, Cloudflare AI).
 * Supports automatic model selection based on content type and custom configurations.
 */
export class EmbeddingService {
  private config: IEmbeddingConfig | null = null;
  private customConfig: IEmbeddingConfig | null = null;

  /**
   * Creates a new EmbeddingService instance
   *
   * @param customConfig - Optional custom configuration to override defaults
   *
   * @example
   * ```typescript
   * // Use default configuration
   * const service = new EmbeddingService();
   *
   * // Use custom configuration
   * const service = new EmbeddingService({
   *   provider: 'openai',
   *   model: 'text-embedding-3-large'
   * });
   * ```
   */
  constructor(customConfig?: Partial<IEmbeddingConfig>) {
    if (customConfig) {
      this.customConfig = {
        provider: customConfig.provider || 'voyageai',
        model: customConfig.model || this.getDefaultModel(customConfig.provider || 'voyageai'),
        dimensions:
          customConfig.dimensions || this.getDefaultDimensions(customConfig.provider || 'voyageai'),
      };
    }
  }

  /**
   * Gets the current embedding configuration
   *
   * @returns Promise resolving to the embedding configuration
   * @throws Error if environment variables are invalid
   *
   * @example
   * ```typescript
   * const service = new EmbeddingService();
   * const config = await service.getConfig();
   * console.log(`Using ${config.provider} with model ${config.model}`);
   * ```
   */
  async getConfig(): Promise<IEmbeddingConfig> {
    if (this.customConfig) {
      return this.customConfig;
    }

    if (this.config) {
      return this.config;
    }

    const env = await getEnv();
    const provider = (env.EMBEDDING_PROVIDER as TEmbeddingProvider) || 'voyageai';
    const model = env.EMBEDDING_MODEL || this.getDefaultModel(provider);
    const dimensions = parseInt(
      env.VECTOR_DIMENSIONS ||
        env.EMBEDDING_DIMENSIONS ||
        this.getDefaultDimensions(provider).toString()
    );

    this.config = { provider, model, dimensions };
    return this.config;
  }

  /**
   * Creates a new EmbeddingService instance with custom configuration
   *
   * @param config - Partial embedding configuration to override defaults
   * @returns New EmbeddingService instance with custom configuration
   *
   * @example
   * ```typescript
   * const service = EmbeddingService.withConfig({
   *   provider: 'openai',
   *   model: 'text-embedding-3-large'
   * });
   * ```
   */
  static withConfig(config: Partial<IEmbeddingConfig>): EmbeddingService {
    return new EmbeddingService(config);
  }

  /**
   * Validates if a model is compatible with the specified provider
   *
   * @param provider - The embedding provider to check
   * @param model - The model name to validate
   * @returns True if the model is supported by the provider, false otherwise
   *
   * @example
   * ```typescript
   * const isValid = EmbeddingService.validateProviderModel('openai', 'text-embedding-3-large');
   * console.log(isValid); // true
   *
   * const isInvalid = EmbeddingService.validateProviderModel('openai', 'voyage-3-large');
   * console.log(isInvalid); // false
   * ```
   */
  static validateProviderModel(provider: TEmbeddingProvider, model: string): boolean {
    return PROVIDER_MODELS[provider]?.includes(model) || false;
  }

  /**
   * Gets the default model for a given provider
   *
   * @param provider - The embedding provider
   * @returns The default model name for the provider
   *
   * @private
   */
  private getDefaultModel(provider: TEmbeddingProvider): string {
    return DEFAULT_MODELS[provider] || DEFAULT_MODELS.cloudflare;
  }

  /**
   * Gets the default dimensions for a given provider's default model
   *
   * @param provider - The embedding provider
   * @returns The default vector dimensions for the provider
   *
   * @private
   */
  private getDefaultDimensions(provider: TEmbeddingProvider): number {
    const defaultModel = this.getDefaultModel(provider);
    return MODEL_DIMENSIONS[defaultModel] || 1024;
  }

  /**
   * Generates an embedding vector for the given text
   *
   * @param text - The text to generate an embedding for
   * @returns Promise resolving to embedding result with vector, model info, and metadata
   * @throws Error if embedding generation fails or provider is unsupported
   *
   * @example
   * ```typescript
   * const service = new EmbeddingService();
   * const result = await service.generateEmbedding("Hello world");
   * console.log(`Generated ${result.dimensions}D vector using ${result.provider}`);
   * ```
   */
  async generateEmbedding(text: string): Promise<IEmbeddingResult> {
    const config = await this.getConfig();

    switch (config.provider) {
      case 'openai':
        return this.generateOpenAIEmbedding(text, config);
      case 'voyageai':
        return this.generateVoyageAIEmbedding(text, config);
      case 'cloudflare':
        return this.generateCloudflareEmbedding(text, config);
      default:
        throw new Error(`Unsupported embedding provider: ${config.provider}`);
    }
  }

  private async generateOpenAIEmbedding(
    text: string,
    config: IEmbeddingConfig
  ): Promise<IEmbeddingResult> {
    try {
      const openai = await getOpenAI();

      const response = await openai.embeddings.create({
        model: config.model,
        input: text,
        encoding_format: 'float',
      });

      return {
        embedding: response.data[0].embedding,
        model: config.model,
        dimensions: config.dimensions,
        provider: 'openai',
        usage: {
          prompt_tokens: response.usage?.prompt_tokens || 0,
          total_tokens: response.usage?.total_tokens || 0,
        },
      };
    } catch (error) {
      throw new Error(
        `OpenAI embedding generation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async generateVoyageAIEmbedding(
    text: string,
    config: IEmbeddingConfig
  ): Promise<IEmbeddingResult> {
    try {
      const voyageai = await getVoyageAI();

      const response = await voyageai.embed({
        input: [text],
        model: config.model,
      });

      if (!response.data || response.data.length === 0) {
        throw new Error('No embedding data returned from VoyageAI');
      }

      return {
        embedding: response.data[0].embedding || [],
        model: config.model,
        dimensions: config.dimensions,
        provider: 'voyageai',
        usage: {
          total_tokens: response.usage?.totalTokens || 0,
        },
      };
    } catch (error) {
      throw new Error(
        `VoyageAI embedding generation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async generateCloudflareEmbedding(
    text: string,
    config: IEmbeddingConfig
  ): Promise<IEmbeddingResult> {
    try {
      const ai = await getAi();

      const response = (await ai.run(config.model as '@cf/baai/bge-large-en-v1.5', {
        text: text,
      })) as ICloudflareAIEmbeddingResponse;

      if (!response.data || response.data.length === 0) {
        throw new Error('No embedding data returned from Cloudflare AI');
      }

      return {
        embedding: response.data[0],
        model: config.model,
        dimensions: config.dimensions,
        provider: 'cloudflare',
      };
    } catch (error) {
      throw new Error(
        `Cloudflare AI embedding generation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  async generateBatchEmbeddings(texts: string[]): Promise<IEmbeddingResult[]> {
    const config = await this.getConfig();

    switch (config.provider) {
      case 'openai':
        return this.generateOpenAIBatchEmbeddings(texts, config);
      case 'voyageai':
        return this.generateVoyageAIBatchEmbeddings(texts, config);
      case 'cloudflare':
        return this.generateCloudflareBatchEmbeddings(texts, config);
      default:
        throw new Error(`Unsupported embedding provider: ${config.provider}`);
    }
  }

  private async generateOpenAIBatchEmbeddings(
    texts: string[],
    config: IEmbeddingConfig
  ): Promise<IEmbeddingResult[]> {
    try {
      const openai = await getOpenAI();

      const response = await openai.embeddings.create({
        model: config.model,
        input: texts,
        encoding_format: 'float',
      });

      return response.data.map((item) => ({
        embedding: item.embedding,
        model: config.model,
        dimensions: config.dimensions,
        provider: 'openai' as const,
      }));
    } catch (error) {
      throw new Error(
        `OpenAI batch embedding generation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async generateVoyageAIBatchEmbeddings(
    texts: string[],
    config: IEmbeddingConfig
  ): Promise<IEmbeddingResult[]> {
    try {
      const voyageai = await getVoyageAI();

      const response = await voyageai.embed({
        input: texts,
        model: config.model,
      });

      if (!response.data || response.data.length === 0) {
        throw new Error('No embedding data returned from VoyageAI');
      }

      return response.data.map((item) => ({
        embedding: item.embedding || [],
        model: config.model,
        dimensions: config.dimensions,
        provider: 'voyageai' as const,
      }));
    } catch (error) {
      throw new Error(
        `VoyageAI batch embedding generation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async generateCloudflareBatchEmbeddings(
    texts: string[],
    config: IEmbeddingConfig
  ): Promise<IEmbeddingResult[]> {
    try {
      const ai = await getAi();

      const response = (await ai.run(config.model as '@cf/baai/bge-large-en-v1.5', {
        text: texts,
      })) as ICloudflareAIEmbeddingResponse;

      if (!response.data || response.data.length === 0) {
        throw new Error('No embedding data returned from Cloudflare AI');
      }

      return response.data.map((embedding: number[]) => ({
        embedding,
        model: config.model,
        dimensions: config.dimensions,
        provider: 'cloudflare' as const,
      }));
    } catch (error) {
      throw new Error(
        `Cloudflare AI batch embedding generation failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * Gets information about the current provider and available providers
   *
   * @returns Promise resolving to provider information including current config and available options
   *
   * @example
   * ```typescript
   * const service = new EmbeddingService();
   * const info = await service.getProviderInfo();
   * console.log(`Current: ${info.provider} (${info.model})`);
   * console.log(`Available: ${info.available_providers.join(', ')}`);
   * ```
   */
  async getProviderInfo(): Promise<{
    provider: TEmbeddingProvider;
    model: string;
    dimensions: number;
    available_providers: TEmbeddingProvider[];
  }> {
    const config = await this.getConfig();

    return {
      provider: config.provider,
      model: config.model,
      dimensions: config.dimensions,
      available_providers: ['openai', 'voyageai', 'cloudflare'],
    };
  }
}

// Lazy singleton instance
let embeddingServiceInstance: EmbeddingService | null = null;

/**
 * Get or create embedding service instance (lazy initialization)
 */
export const getEmbeddingService = (): EmbeddingService => {
  if (!embeddingServiceInstance) {
    embeddingServiceInstance = new EmbeddingService();
  }
  return embeddingServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const embeddingService = new Proxy({} as EmbeddingService, {
  get(_target, prop) {
    const service = getEmbeddingService();
    const value = service[prop as keyof EmbeddingService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  },
});
