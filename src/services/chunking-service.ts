/**
 * Text Chunking Service
 *
 * Provides reusable text chunking functionality with configurable chunk size and overlap.
 * Used by upload endpoints and seeding scripts to maintain consistent chunking behavior.
 */

import {
  DEFAULT_CHUNKING_CONFIG,
  EXTENSION_TO_LANGUAGE,
  type IBaseChunkMetadata,
  type IChunkingConfig,
  type ITextChunk,
  type TContentType,
} from '@types';

export class ChunkingService {
  private readonly config: IChunkingConfig;

  constructor(config: Partial<IChunkingConfig> = DEFAULT_CHUNKING_CONFIG) {
    this.config = {
      chunkSize: config.chunkSize || DEFAULT_CHUNKING_CONFIG.chunkSize,
      chunkOverlap: config.chunkOverlap || DEFAULT_CHUNKING_CONFIG.chunkOverlap,
      strategy: config.strategy || DEFAULT_CHUNKING_CONFIG.strategy || 'word',
      preserveWordBoundaries:
        config.preserveWordBoundaries ?? DEFAULT_CHUNKING_CONFIG.preserveWordBoundaries ?? true,
      respectSentenceBoundaries:
        config.respectSentenceBoundaries ??
        DEFAULT_CHUNKING_CONFIG.respectSentenceBoundaries ??
        true,
      minChunkSize: config.minChunkSize || DEFAULT_CHUNKING_CONFIG.minChunkSize || 100,
    };
  }

  /**
   * Create chunks from text content with specified metadata using the configured strategy
   */
  createChunks<T extends IBaseChunkMetadata>(
    content: string,
    baseMetadata: Omit<T, keyof IBaseChunkMetadata>,
    idPrefix: string = 'chunk'
  ): ITextChunk<T>[] {
    if (!content || content.trim().length === 0) {
      return [];
    }

    // Choose chunking strategy
    switch (this.config.strategy) {
      case 'word':
        return this.createWordBasedChunks(content, baseMetadata, idPrefix);
      case 'sentence':
        return this.createSentenceBasedChunks(content, baseMetadata, idPrefix);
      case 'paragraph':
        return this.createParagraphBasedChunks(content, baseMetadata, idPrefix);
      default:
        return this.createCharacterBasedChunks(content, baseMetadata, idPrefix);
    }
  }

  /**
   * Create word-based chunks that preserve word boundaries and semantic meaning
   */
  private createWordBasedChunks<T extends IBaseChunkMetadata>(
    content: string,
    baseMetadata: Omit<T, keyof IBaseChunkMetadata>,
    idPrefix: string
  ): ITextChunk<T>[] {
    const chunks: ITextChunk<T>[] = [];
    const words = content.split(/\s+/);
    let currentChunk = '';
    let chunkIndex = 0;

    for (const word of words) {
      const testChunk = currentChunk ? `${currentChunk} ${word}` : word;

      // Check if adding this word would exceed chunk size
      if (testChunk.length > this.config.chunkSize && currentChunk.length > 0) {
        // Create chunk from current content
        if (currentChunk.trim().length >= (this.config.minChunkSize || 100)) {
          chunks.push(
            this.createChunkObject(currentChunk.trim(), chunkIndex, baseMetadata, idPrefix)
          );
          chunkIndex++;
        }

        // Start new chunk with overlap
        const overlapWords = this.getOverlapWords(currentChunk, this.config.chunkOverlap);
        currentChunk = overlapWords ? `${overlapWords} ${word}` : word;
      } else {
        currentChunk = testChunk;
      }
    }

    // Add final chunk if it has content
    if (currentChunk.trim().length >= (this.config.minChunkSize || 100)) {
      chunks.push(this.createChunkObject(currentChunk.trim(), chunkIndex, baseMetadata, idPrefix));
    }

    // Update total chunks count
    chunks.forEach((chunk) => {
      chunk.metadata.total_chunks = chunks.length;
    });

    return chunks;
  }

  /**
   * Create sentence-based chunks that respect sentence boundaries
   */
  private createSentenceBasedChunks<T extends IBaseChunkMetadata>(
    content: string,
    baseMetadata: Omit<T, keyof IBaseChunkMetadata>,
    idPrefix: string
  ): ITextChunk<T>[] {
    const chunks: ITextChunk<T>[] = [];
    const sentences = content.split(/[.!?]+\s+/).filter((s) => s.trim().length > 0);
    let currentChunk = '';
    let chunkIndex = 0;

    for (const sentence of sentences) {
      const testChunk = currentChunk ? `${currentChunk}. ${sentence}` : sentence;

      if (testChunk.length > this.config.chunkSize && currentChunk.length > 0) {
        if (currentChunk.trim().length >= (this.config.minChunkSize || 100)) {
          chunks.push(
            this.createChunkObject(currentChunk.trim(), chunkIndex, baseMetadata, idPrefix)
          );
          chunkIndex++;
        }

        // Start new chunk with overlap
        const overlapText = this.getOverlapText(currentChunk, this.config.chunkOverlap);
        currentChunk = overlapText ? `${overlapText}. ${sentence}` : sentence;
      } else {
        currentChunk = testChunk;
      }
    }

    if (currentChunk.trim().length >= (this.config.minChunkSize || 100)) {
      chunks.push(this.createChunkObject(currentChunk.trim(), chunkIndex, baseMetadata, idPrefix));
    }

    chunks.forEach((chunk) => {
      chunk.metadata.total_chunks = chunks.length;
    });

    return chunks;
  }

  /**
   * Create paragraph-based chunks
   */
  private createParagraphBasedChunks<T extends IBaseChunkMetadata>(
    content: string,
    baseMetadata: Omit<T, keyof IBaseChunkMetadata>,
    idPrefix: string
  ): ITextChunk<T>[] {
    const chunks: ITextChunk<T>[] = [];
    const paragraphs = content.split(/\n\s*\n/).filter((p) => p.trim().length > 0);
    let currentChunk = '';
    let chunkIndex = 0;

    for (const paragraph of paragraphs) {
      const testChunk = currentChunk ? `${currentChunk}\n\n${paragraph}` : paragraph;

      if (testChunk.length > this.config.chunkSize && currentChunk.length > 0) {
        if (currentChunk.trim().length >= (this.config.minChunkSize || 100)) {
          chunks.push(
            this.createChunkObject(currentChunk.trim(), chunkIndex, baseMetadata, idPrefix)
          );
          chunkIndex++;
        }
        currentChunk = paragraph;
      } else {
        currentChunk = testChunk;
      }
    }

    if (currentChunk.trim().length >= (this.config.minChunkSize || 100)) {
      chunks.push(this.createChunkObject(currentChunk.trim(), chunkIndex, baseMetadata, idPrefix));
    }

    chunks.forEach((chunk) => {
      chunk.metadata.total_chunks = chunks.length;
    });

    return chunks;
  }

  /**
   * Create character-based chunks (original implementation)
   */
  private createCharacterBasedChunks<T extends IBaseChunkMetadata>(
    content: string,
    baseMetadata: Omit<T, keyof IBaseChunkMetadata>,
    idPrefix: string
  ): ITextChunk<T>[] {
    const chunks: ITextChunk<T>[] = [];
    const totalLength = content.length;
    let chunkIndex = 0;

    for (let i = 0; i < totalLength; i += this.config.chunkSize - this.config.chunkOverlap) {
      const end = Math.min(i + this.config.chunkSize, totalLength);
      let chunkContent = content.substring(i, end);

      // Preserve word boundaries if configured
      if (this.config.preserveWordBoundaries && end < totalLength) {
        const lastSpaceIndex = chunkContent.lastIndexOf(' ');
        if (lastSpaceIndex > chunkContent.length * 0.8) {
          // Only if we don't lose too much content
          chunkContent = chunkContent.substring(0, lastSpaceIndex);
        }
      }

      if (chunkContent.trim().length >= (this.config.minChunkSize || 100)) {
        chunks.push(
          this.createChunkObject(chunkContent.trim(), chunkIndex, baseMetadata, idPrefix)
        );
        chunkIndex++;
      }
    }

    chunks.forEach((chunk) => {
      chunk.metadata.total_chunks = chunks.length;
    });

    return chunks;
  }

  /**
   * Helper method to create a chunk object with proper metadata
   */
  private createChunkObject<T extends IBaseChunkMetadata>(
    content: string,
    chunkIndex: number,
    baseMetadata: Omit<T, keyof IBaseChunkMetadata>,
    idPrefix: string
  ): ITextChunk<T> {
    const chunkId = `${idPrefix}-${chunkIndex}`;
    return {
      id: chunkId,
      content: content,
      metadata: {
        ...baseMetadata,
        chunk_index: chunkIndex,
        total_chunks: 0, // Will be updated later
      } as T,
    };
  }

  /**
   * Get overlap words from the end of a chunk
   */
  private getOverlapWords(text: string, overlapSize: number): string {
    if (!text || overlapSize <= 0) return '';

    const words = text.split(/\s+/);
    let overlapText = '';

    // Take words from the end until we reach the overlap size
    for (let i = words.length - 1; i >= 0; i--) {
      const word = words[i];
      const testText = overlapText ? `${word} ${overlapText}` : word;

      if (testText.length > overlapSize) break;

      overlapText = testText;
    }

    return overlapText;
  }

  /**
   * Get overlap text from the end of a chunk (character-based)
   */
  private getOverlapText(text: string, overlapSize: number): string {
    if (!text || overlapSize <= 0) return '';

    const startIndex = Math.max(0, text.length - overlapSize);
    let overlapText = text.substring(startIndex);

    // Try to start at a word boundary if possible
    if (this.config.preserveWordBoundaries) {
      const spaceIndex = overlapText.indexOf(' ');
      if (spaceIndex > 0 && spaceIndex < overlapText.length * 0.5) {
        overlapText = overlapText.substring(spaceIndex + 1);
      }
    }

    return overlapText.trim();
  }

  /**
   * Create chunks from structured sections (like from seeding script)
   */
  createChunksFromSections<T extends IBaseChunkMetadata>(
    sections: Array<{
      title?: string;
      description?: string;
      content: string;
      source?: string;
      language?: string;
      code?: boolean;
    }>,
    baseMetadata: Omit<T, keyof IBaseChunkMetadata>,
    idPrefix: string = 'section'
  ): ITextChunk<T>[] {
    const chunks: ITextChunk<T>[] = [];

    sections.forEach((section, index) => {
      const chunkId = `${idPrefix}-${index}`;
      const chunk: ITextChunk<T> = {
        id: chunkId,
        content: section.content,
        metadata: {
          ...baseMetadata,
          chunk_index: index,
          total_chunks: sections.length,
          content_type: section.code ? 'code' : 'documentation',
          // Add section-specific metadata if T supports it
          ...(section.title && { title: section.title }),
          ...(section.description && { description: section.description }),
          ...(section.source && { source: section.source }),
          ...(section.language && { language: section.language }),
        } as T,
      };
      chunks.push(chunk);
    });

    return chunks;
  }

  /**
   * Determine content type from file extension
   */
  getContentTypeFromExtension(filename: string): TContentType {
    const extension = `.${filename.split('.').pop()?.toLowerCase() || ''}`;

    // Use the mapping from types
    const contentType = EXTENSION_TO_LANGUAGE[extension];
    if (contentType) {
      return contentType as TContentType;
    }

    // Handle special cases
    switch (extension) {
      case '.md':
      case '.markdown':
        return 'markdown';
      case '.json':
        return 'json';
      case '.pdf':
        return 'pdf';
      case '.doc':
        return 'doc';
      case '.docx':
        return 'docx';
      case '.jsx':
        return 'javascript';
      case '.tsx':
        return 'typescript';
      case '.c':
      case '.h':
        return 'cpp';
      case '.rs':
        return 'rust';
      case '.swift':
        return 'swift';
      case '.kt':
        return 'kotlin';
      case '.scala':
        return 'scala';
      case '.r':
        return 'r';
      case '.sql':
        return 'sql';
      case '.sh':
      case '.bat':
      case '.ps1':
        return 'shell';
      case '.yaml':
      case '.yml':
        return 'yaml';
      case '.xml':
        return 'xml';
      case '.html':
        return 'html';
      case '.css':
      case '.scss':
      case '.less':
        return 'css';
      default:
        return 'text';
    }
  }

  /**
   * Parse structured content (like from seeding script format)
   */
  parseStructuredContent(content: string): Array<{
    title?: string;
    description?: string;
    content: string;
    source?: string;
    language?: string;
    code?: boolean;
  }> {
    const sections: Array<{
      title?: string;
      description?: string;
      content: string;
      source?: string;
      language?: string;
      code?: boolean;
    }> = [];

    // Split by section delimiter
    const sectionDelimiter = /^-{20,}$/gm;
    const rawSections = content.split(sectionDelimiter);

    for (const rawSection of rawSections) {
      const trimmedSection = rawSection.trim();
      if (!trimmedSection) continue;

      const section: {
        title?: string;
        description?: string;
        content: string;
        source?: string;
        language?: string;
        code?: boolean;
      } = { content: '' };

      // Parse metadata lines
      const lines = trimmedSection.split('\n');
      let contentStartIndex = 0;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        if (line.startsWith('TITLE:')) {
          section.title = line.substring(6).trim();
          contentStartIndex = i + 1;
        } else if (line.startsWith('DESCRIPTION:')) {
          section.description = line.substring(12).trim();
          contentStartIndex = i + 1;
        } else if (line.startsWith('SOURCE:')) {
          section.source = line.substring(7).trim();
          contentStartIndex = i + 1;
        } else if (line.startsWith('LANGUAGE:')) {
          section.language = line.substring(9).trim();
          contentStartIndex = i + 1;
        } else if (line.startsWith('CODE:')) {
          section.code = true;
          contentStartIndex = i + 1;
          break; // CODE: should be the last metadata line
        } else if (line && !line.includes(':')) {
          // First non-metadata line
          break;
        }
      }

      // Extract content
      section.content = lines.slice(contentStartIndex).join('\n').trim();

      if (section.content) {
        sections.push(section);
      }
    }

    return sections;
  }

  /**
   * Get chunking configuration
   */
  getConfig(): IChunkingConfig {
    return { ...this.config };
  }

  /**
   * Create a new chunking service with different configuration
   */
  static withConfig(config: Partial<IChunkingConfig>): ChunkingService {
    return new ChunkingService(config);
  }
}

// Lazy singleton instance
let chunkingServiceInstance: ChunkingService | null = null;

/**
 * Get or create chunking service instance (lazy initialization)
 */
export const getChunkingService = (): ChunkingService => {
  if (!chunkingServiceInstance) {
    chunkingServiceInstance = new ChunkingService();
  }
  return chunkingServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const chunkingService = new Proxy({} as ChunkingService, {
  get(_target, prop) {
    const service = getChunkingService();
    const value = service[prop as keyof ChunkingService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  },
});
