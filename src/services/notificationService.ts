/**
 * Notification Service
 *
 * Manages email notification preferences, queuing, and delivery
 * for API key expiration and rotation events.
 */

import type {
  TEmailNotificationPreferences,
  TNewEmailNotificationPreferences,
  TNewNotificationQueue,
  TNotificationQueue,
} from '@dbSchema';
import * as schema from '@dbSchema';
import getDB from '@utils/getDB';
import { and, eq, gte, isNull, lte, sql } from 'drizzle-orm';
import type {
  IApiKeyEmailContext,
  IEmailNotificationPreferences,
  TEmailTemplate,
} from '@/types/email';
import { emailService } from './emailService';
import { webhookService } from './webhookService';

// ============================================================================
// Notification Service Class
// ============================================================================

export class NotificationService {
  /**
   * Get user's notification preferences
   */
  async getNotificationPreferences(
    userId: string,
    teamId?: string
  ): Promise<IEmailNotificationPreferences> {
    const db = await getDB();

    // Try to get team-specific preferences first, then user preferences
    const whereConditions = [eq(schema.emailNotificationPreferences.userId, userId)];

    if (teamId) {
      whereConditions.push(eq(schema.emailNotificationPreferences.teamId, teamId));
    } else {
      whereConditions.push(isNull(schema.emailNotificationPreferences.teamId));
    }

    const preferences = await db.query.emailNotificationPreferences.findFirst({
      where: and(...whereConditions),
    });

    if (preferences) {
      return {
        userId: preferences.userId,
        teamId: preferences.teamId || undefined,
        expirationWarnings: preferences.expirationWarnings,
        expirationWarningDays: preferences.expirationWarningDays
          ? JSON.parse(preferences.expirationWarningDays)
          : [7, 3, 1],
        rotationNotifications: preferences.rotationNotifications,
        rotationFailureAlerts: preferences.rotationFailureAlerts,
        bulkOperationSummaries: preferences.bulkOperationSummaries,
        teamExpirationSummaries: preferences.teamExpirationSummaries,
        emailEnabled: preferences.emailEnabled,
        webhookEnabled: preferences.webhookEnabled,
        webhookUrl: preferences.webhookUrl || undefined,
        maxEmailsPerDay: preferences.maxEmailsPerDay,
        quietHours:
          preferences.quietHoursStart && preferences.quietHoursEnd
            ? {
                start: preferences.quietHoursStart,
                end: preferences.quietHoursEnd,
                timezone: preferences.quietHoursTimezone || 'UTC',
              }
            : undefined,
      };
    }

    // Return default preferences if none found
    return {
      userId,
      teamId,
      expirationWarnings: true,
      expirationWarningDays: [7, 1],
      rotationNotifications: true,
      rotationFailureAlerts: true,
      bulkOperationSummaries: true,
      teamExpirationSummaries: false,
      emailEnabled: true,
      webhookEnabled: false,
      maxEmailsPerDay: 10,
    };
  }

  /**
   * Update user's notification preferences
   */
  async updateNotificationPreferences(
    userId: string,
    preferences: Partial<IEmailNotificationPreferences>,
    teamId?: string
  ): Promise<TEmailNotificationPreferences> {
    const db = await getDB();

    const updateData: Partial<TNewEmailNotificationPreferences> = {
      expirationWarnings: preferences.expirationWarnings,
      expirationWarningDays: preferences.expirationWarningDays
        ? JSON.stringify(preferences.expirationWarningDays)
        : undefined,
      rotationNotifications: preferences.rotationNotifications,
      rotationFailureAlerts: preferences.rotationFailureAlerts,
      bulkOperationSummaries: preferences.bulkOperationSummaries,
      teamExpirationSummaries: preferences.teamExpirationSummaries,
      emailEnabled: preferences.emailEnabled,
      webhookEnabled: preferences.webhookEnabled,
      webhookUrl: preferences.webhookUrl,
      maxEmailsPerDay: preferences.maxEmailsPerDay,
      quietHoursStart: preferences.quietHours?.start,
      quietHoursEnd: preferences.quietHours?.end,
      quietHoursTimezone: preferences.quietHours?.timezone,
      updatedAt: new Date(),
    };

    // Try to update existing preferences
    const whereConditions = [eq(schema.emailNotificationPreferences.userId, userId)];

    if (teamId) {
      whereConditions.push(eq(schema.emailNotificationPreferences.teamId, teamId));
    } else {
      whereConditions.push(isNull(schema.emailNotificationPreferences.teamId));
    }

    const existing = await db.query.emailNotificationPreferences.findFirst({
      where: and(...whereConditions),
    });

    if (existing) {
      const [updated] = await db
        .update(schema.emailNotificationPreferences)
        .set(updateData)
        .where(eq(schema.emailNotificationPreferences.id, existing.id))
        .returning();

      return updated;
    }

    // Create new preferences if none exist
    const newPreferences: TNewEmailNotificationPreferences = {
      userId,
      teamId: teamId || null,
      expirationWarnings: preferences.expirationWarnings ?? true,
      expirationWarningDays: JSON.stringify(preferences.expirationWarningDays ?? [7, 1]),
      rotationNotifications: preferences.rotationNotifications ?? true,
      rotationFailureAlerts: preferences.rotationFailureAlerts ?? true,
      bulkOperationSummaries: preferences.bulkOperationSummaries ?? true,
      teamExpirationSummaries: preferences.teamExpirationSummaries ?? false,
      emailEnabled: preferences.emailEnabled ?? true,
      webhookEnabled: preferences.webhookEnabled ?? false,
      webhookUrl: preferences.webhookUrl || null,
      maxEmailsPerDay: preferences.maxEmailsPerDay ?? 10,
      quietHoursStart: preferences.quietHours?.start || null,
      quietHoursEnd: preferences.quietHours?.end || null,
      quietHoursTimezone: preferences.quietHours?.timezone || 'UTC',
    };

    const [created] = await db
      .insert(schema.emailNotificationPreferences)
      .values(newPreferences)
      .returning();

    return created;
  }

  /**
   * Queue a notification for delivery
   */
  async queueNotification(
    type: TEmailTemplate,
    userId: string,
    userEmail: string,
    context: IApiKeyEmailContext,
    options: {
      teamId?: string;
      priority?: 'low' | 'normal' | 'high' | 'urgent';
      scheduledAt?: Date;
      maxRetries?: number;
    } = {}
  ): Promise<TNotificationQueue> {
    const db = await getDB();

    // Check user preferences first
    const preferences = await this.getNotificationPreferences(userId, options.teamId);

    if (!preferences.emailEnabled) {
      throw new Error('Email notifications are disabled for this user');
    }

    // Check if this type of notification is enabled
    if (!this.isNotificationTypeEnabled(type, preferences)) {
      throw new Error(`Notification type ${type} is disabled for this user`);
    }

    // Check daily email limit
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayCount = await db
      .select({ count: sql<number>`count(*)` })
      .from(schema.notificationQueue)
      .where(
        and(
          eq(schema.notificationQueue.userId, userId),
          eq(schema.notificationQueue.status, 'sent'),
          gte(schema.notificationQueue.sentAt, today),
          lte(schema.notificationQueue.sentAt, tomorrow)
        )
      );

    if (todayCount[0]?.count >= preferences.maxEmailsPerDay) {
      throw new Error('Daily email limit reached for this user');
    }

    // Check quiet hours
    if (preferences.quietHours && this.isInQuietHours(preferences.quietHours)) {
      // Schedule for after quiet hours
      const scheduledAt = this.getNextAvailableTime(preferences.quietHours);
      options.scheduledAt = scheduledAt;
    }

    const notificationData: TNewNotificationQueue = {
      type,
      priority: options.priority || 'normal',
      userId,
      userEmail,
      teamId: options.teamId || null,
      context: JSON.stringify(context),
      scheduledAt: options.scheduledAt || new Date(),
      maxRetries: options.maxRetries || 3,
      retryCount: 0,
      status: 'pending',
    };

    const [notification] = await db
      .insert(schema.notificationQueue)
      .values(notificationData)
      .returning();

    return notification;
  }

  /**
   * Process pending notifications
   */
  async processPendingNotifications(limit = 50): Promise<{
    processed: number;
    successful: number;
    failed: number;
  }> {
    const db = await getDB();

    // Get pending notifications ordered by priority and scheduled time
    const pendingNotifications = await db.query.notificationQueue.findMany({
      where: and(
        eq(schema.notificationQueue.status, 'pending'),
        lte(schema.notificationQueue.scheduledAt, new Date())
      ),
      orderBy: [
        sql`CASE 
          WHEN priority = 'urgent' THEN 1
          WHEN priority = 'high' THEN 2
          WHEN priority = 'normal' THEN 3
          WHEN priority = 'low' THEN 4
        END`,
        schema.notificationQueue.scheduledAt,
      ],
      limit,
    });

    let processed = 0;
    let successful = 0;
    let failed = 0;

    for (const notification of pendingNotifications) {
      processed++;

      try {
        // Mark as processing
        await db
          .update(schema.notificationQueue)
          .set({
            status: 'processing',
            lastAttemptAt: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(schema.notificationQueue.id, notification.id));

        // Parse context and send notification
        const context: IApiKeyEmailContext = JSON.parse(notification.context);

        // Get user preferences to check if webhooks are enabled
        const preferences = await this.getNotificationPreferences(
          notification.userId,
          notification.teamId || undefined
        );

        let emailResult = null;
        let webhookResult = null;

        // Send email notification if enabled
        if (preferences.emailEnabled) {
          switch (notification.type) {
            case 'api_key_expiring_7_days':
            case 'api_key_expiring_1_day': {
              const daysUntilExpiration = notification.type === 'api_key_expiring_1_day' ? 1 : 7;
              emailResult = await emailService.sendExpirationWarning(
                context.userEmail,
                context.userName,
                context,
                daysUntilExpiration
              );
              break;
            }

            case 'api_key_rotated': {
              emailResult = await emailService.sendRotationNotification(
                context.userEmail,
                context.userName,
                context
              );
              break;
            }

            default:
              throw new Error(`Unsupported notification type: ${notification.type}`);
          }
        }

        // Send webhook notification if enabled and URL is configured
        if (preferences.webhookEnabled && preferences.webhookUrl) {
          try {
            switch (notification.type) {
              case 'api_key_expiring_7_days':
              case 'api_key_expiring_1_day': {
                const daysUntilExpiration = notification.type === 'api_key_expiring_1_day' ? 1 : 7;
                webhookResult = await webhookService.sendExpirationWebhook(
                  preferences.webhookUrl,
                  {
                    id: context.keyId,
                    name: context.keyName,
                    prefix: context.keyPrefix,
                    expiresAt: context.expiresAt || new Date(),
                    permissions: context.permissions,
                  },
                  {
                    id: notification.userId,
                    email: context.userEmail,
                    name: context.userName,
                  },
                  daysUntilExpiration,
                  context.teamId && context.teamName
                    ? {
                        id: context.teamId,
                        name: context.teamName,
                        slug: context.teamName.toLowerCase().replace(/\s+/g, '-'),
                      }
                    : undefined
                );
                break;
              }

              case 'api_key_rotated': {
                webhookResult = await webhookService.sendRotationWebhook(
                  preferences.webhookUrl,
                  {
                    id: context.keyId,
                    name: context.keyName,
                    prefix: context.keyPrefix,
                  },
                  {
                    id: context.keyId, // This would be the new key ID in a real implementation
                    name: context.keyName,
                    prefix: context.newKeyPrefix || context.keyPrefix,
                    expiresAt: context.expiresAt,
                  },
                  {
                    id: notification.userId,
                    email: context.userEmail,
                    name: context.userName,
                  },
                  context.teamId && context.teamName
                    ? {
                        id: context.teamId,
                        name: context.teamName,
                        slug: context.teamName.toLowerCase().replace(/\s+/g, '-'),
                      }
                    : undefined,
                  {
                    rotationType: 'manual',
                    reason: 'API key rotation',
                    rotatedAt: context.rotatedAt || new Date(),
                  }
                );
                break;
              }
            }
          } catch (webhookError) {
            console.error('Webhook delivery failed:', webhookError);
            // Don't fail the entire notification due to webhook errors
          }
        }

        // Consider notification successful if either email or webhook succeeded
        const success = (emailResult?.success ?? false) || (webhookResult?.success ?? false);
        if (!success) {
          throw new Error(
            `Notification delivery failed. Email: ${emailResult?.error || 'disabled'}, Webhook: ${webhookResult?.error || 'disabled'}`
          );
        }

        // Mark as sent
        await db
          .update(schema.notificationQueue)
          .set({
            status: 'sent',
            sentAt: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(schema.notificationQueue.id, notification.id));

        successful++;
      } catch (error) {
        // Handle failure
        const newRetryCount = notification.retryCount + 1;
        const shouldRetry = newRetryCount < notification.maxRetries;

        await db
          .update(schema.notificationQueue)
          .set({
            status: shouldRetry ? 'pending' : 'failed',
            retryCount: newRetryCount,
            error: error instanceof Error ? error.message : String(error),
            scheduledAt: shouldRetry
              ? new Date(Date.now() + 2 ** newRetryCount * 60000)
              : // Exponential backoff
                notification.scheduledAt,
            lastAttemptAt: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(schema.notificationQueue.id, notification.id));

        failed++;
      }
    }

    return { processed, successful, failed };
  }

  /**
   * Send bulk expiration notifications to team administrators
   */
  async sendBulkExpirationNotifications(
    teamId: string,
    expiringKeys: Array<{
      id: string;
      name: string;
      prefix: string;
      expiresAt: Date;
      ownerName: string;
      ownerEmail: string;
      userId: string;
    }>
  ): Promise<{
    emailsSent: number;
    webhooksSent: number;
    errors: string[];
  }> {
    const db = await getDB();
    const results = {
      emailsSent: 0,
      webhooksSent: 0,
      errors: [] as string[],
    };

    try {
      // Get team administrators
      const teamAdmins = await db.query.teamMembers.findMany({
        where: and(
          eq(schema.teamMembers.teamId, teamId),
          sql`${schema.teamMembers.role} IN ('owner', 'admin')`
        ),
        with: {
          user: {
            columns: {
              id: true,
              email: true,
              name: true,
            },
          },
          team: {
            columns: {
              name: true,
              slug: true,
            },
          },
        },
      });

      for (const admin of teamAdmins) {
        if (!admin.user) continue;

        try {
          // Get admin's notification preferences
          const preferences = await this.getNotificationPreferences(admin.user.id, teamId);

          if (!preferences.teamExpirationSummaries) {
            continue; // Skip if admin doesn't want team summaries
          }

          // Send email notification if enabled
          if (preferences.emailEnabled) {
            try {
              const emailResult = await emailService.sendBulkExpirationWarning(
                admin.user.email,
                admin.team?.name || 'Team',
                expiringKeys.map((key) => ({
                  name: key.name,
                  prefix: key.prefix,
                  expiresAt: key.expiresAt,
                  ownerName: key.ownerName,
                }))
              );

              if (emailResult.success) {
                results.emailsSent++;
              } else {
                results.errors.push(
                  `Failed to send email to ${admin.user.email}: ${emailResult.error}`
                );
              }
            } catch (emailError) {
              results.errors.push(
                `Email error for ${admin.user.email}: ${emailError instanceof Error ? emailError.message : String(emailError)}`
              );
            }
          }

          // Send webhook notification if enabled
          if (preferences.webhookEnabled && preferences.webhookUrl) {
            try {
              const webhookResult = await webhookService.sendBulkExpirationWebhook(
                preferences.webhookUrl,
                {
                  id: teamId,
                  name: admin.team?.name || 'Team',
                  slug: admin.team?.slug || 'team',
                },
                {
                  id: admin.user.id,
                  email: admin.user.email,
                  name: admin.user.name || 'Admin',
                },
                expiringKeys
              );

              if (webhookResult.success) {
                results.webhooksSent++;
              } else {
                results.errors.push(
                  `Failed to send webhook to ${admin.user.email}: ${webhookResult.error}`
                );
              }
            } catch (webhookError) {
              results.errors.push(
                `Webhook error for ${admin.user.email}: ${webhookError instanceof Error ? webhookError.message : String(webhookError)}`
              );
            }
          }
        } catch (adminError) {
          results.errors.push(
            `Error processing admin ${admin.user.email}: ${adminError instanceof Error ? adminError.message : String(adminError)}`
          );
        }
      }
    } catch (error) {
      results.errors.push(
        `Failed to get team administrators: ${error instanceof Error ? error.message : String(error)}`
      );
    }

    return results;
  }

  // ============================================================================
  // Private Helper Methods
  // ============================================================================

  /**
   * Check if a notification type is enabled for the user
   */
  private isNotificationTypeEnabled(
    type: TEmailTemplate,
    preferences: IEmailNotificationPreferences
  ): boolean {
    switch (type) {
      case 'api_key_expiring_7_days':
      case 'api_key_expiring_1_day':
      case 'api_key_expired':
        return preferences.expirationWarnings;

      case 'api_key_rotated':
      case 'api_key_rotation_scheduled':
        return preferences.rotationNotifications;

      case 'api_key_rotation_failed':
        return preferences.rotationFailureAlerts;

      case 'bulk_expiration_warning':
        return preferences.bulkOperationSummaries;

      case 'team_api_keys_expiring':
        return preferences.teamExpirationSummaries;

      default:
        return true;
    }
  }

  /**
   * Check if current time is within quiet hours
   */
  private isInQuietHours(quietHours: { start: string; end: string; timezone: string }): boolean {
    const now = new Date();
    const currentTime = now
      .toLocaleTimeString('en-US', {
        hour12: false,
        timeZone: quietHours.timezone,
      })
      .substring(0, 5); // HH:MM format

    return currentTime >= quietHours.start && currentTime <= quietHours.end;
  }

  /**
   * Get next available time after quiet hours
   */
  private getNextAvailableTime(quietHours: { start: string; end: string; timezone: string }): Date {
    const now = new Date();
    const endTime = new Date();
    const [endHour, endMinute] = quietHours.end.split(':').map(Number);

    endTime.setHours(endHour, endMinute, 0, 0);

    // If end time is before current time, schedule for tomorrow
    if (endTime <= now) {
      endTime.setDate(endTime.getDate() + 1);
    }

    return endTime;
  }
}

// ============================================================================
// Service Instance
// ============================================================================

export const notificationService = new NotificationService();
