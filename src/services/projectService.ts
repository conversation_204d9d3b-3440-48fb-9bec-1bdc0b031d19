/**
 * Project Service
 *
 * Enhanced project management with team-based ownership, multi-tenancy,
 * and collaboration features. Integrates with RBAC system for access control.
 */

import type { TNewProject, TProject } from '@dbSchema';
import * as schema from '@dbSchema';
import getDB from '@utils/getDB';
import { and, desc, eq, isNull, or } from 'drizzle-orm';
import type { ITeamContext } from '@/types/teams';

// ============================================================================
// Project Types
// ============================================================================

/**
 * Project creation parameters
 */
export interface ICreateProjectParams {
  userId: string;
  teamId?: string;
  name: string;
  description?: string;
  isPublic?: boolean;
}

/**
 * Project update parameters
 */
export interface IUpdateProjectParams {
  name?: string;
  description?: string;
  isPublic?: boolean;
}

/**
 * Project with team and collaboration details
 */
export interface IProjectWithDetails extends TProject {
  owner: {
    id: string;
    name: string;
    email: string;
  };
  team?: {
    id: string;
    name: string;
    slug: string;
  };
  collectionsCount: number;
  documentsCount: number;
  canEdit: boolean;
  canDelete: boolean;
  canShare: boolean;
}

/**
 * Project sharing parameters
 */
export interface IProjectSharingParams {
  projectId: string;
  teamId: string;
  sharedBy: string;
}

// ============================================================================
// Project CRUD Operations
// ============================================================================

/**
 * Create a new project
 *
 * @param params - Project creation parameters
 * @returns Created project with details
 */
export async function createProject(params: ICreateProjectParams): Promise<IProjectWithDetails> {
  const db = await getDB();

  const newProject: TNewProject = {
    userId: params.userId,
    teamId: params.teamId || null,
    name: params.name,
    description: params.description || null,
    isPublic: params.isPublic || false,
  };

  const [project] = await db.insert(schema.projects).values(newProject).returning();

  // Return project with details
  const projectWithDetails = await getProjectWithDetails(project.id, params.userId);
  if (!projectWithDetails) {
    throw new Error('Failed to retrieve created project');
  }

  return projectWithDetails;
}

/**
 * Get project by ID with details and permissions
 *
 * @param projectId - Project ID
 * @param userId - Current user ID for permission checking
 * @param teamContext - Optional team context for permission checking
 * @returns Project with details or null if not found
 */
export async function getProjectWithDetails(
  projectId: string,
  userId: string,
  teamContext?: ITeamContext
): Promise<IProjectWithDetails | null> {
  const db = await getDB();

  const project = await db.query.projects.findFirst({
    where: and(eq(schema.projects.id, projectId), isNull(schema.projects.deletedAt)),
    with: {
      user: {
        columns: { id: true, name: true, email: true },
      },
      team: {
        columns: { id: true, name: true, slug: true },
      },
      collections: {
        where: isNull(schema.collections.deletedAt),
        with: {
          documents: {
            where: isNull(schema.documents.deletedAt),
          },
        },
      },
    },
  });

  if (!project) return null;

  // Calculate counts
  const collectionsCount = project.collections.length;
  const documentsCount = project.collections.reduce(
    (total, collection) => total + collection.documents.length,
    0
  );

  // Determine permissions
  const isOwner = project.userId === userId;
  const isTeamMember = Boolean(teamContext && project.teamId === teamContext.teamId);
  const hasManagePermission = Boolean(teamContext?.permissions.includes('manage.project'));

  const canEdit = isOwner || (isTeamMember && hasManagePermission);
  const canDelete = isOwner || (isTeamMember && hasManagePermission);
  const canShare = isOwner || (isTeamMember && hasManagePermission);

  return {
    ...project,
    owner: project.user,
    team: project.team || undefined,
    collectionsCount,
    documentsCount,
    canEdit,
    canDelete,
    canShare,
  };
}

/**
 * Get projects for a user with team context
 *
 * @param userId - User ID
 * @param teamContext - Optional team context for filtering
 * @param includePublic - Whether to include public projects
 * @returns Array of projects with details
 */
export async function getUserProjects(
  userId: string,
  teamContext?: ITeamContext,
  includePublic = false
): Promise<IProjectWithDetails[]> {
  const db = await getDB();

  // Build where conditions
  const conditions = [isNull(schema.projects.deletedAt)];

  if (teamContext) {
    // If team context is provided, get team projects
    conditions.push(eq(schema.projects.teamId, teamContext.teamId));
  } else {
    // Get user's personal projects and optionally public projects
    const userConditions = [eq(schema.projects.userId, userId)];

    if (includePublic) {
      userConditions.push(eq(schema.projects.isPublic, true));
    }

    if (userConditions.length > 0) {
      const orCondition = or(...userConditions);
      if (orCondition) {
        conditions.push(orCondition);
      }
    }
  }

  const projects = await db.query.projects.findMany({
    where: and(...conditions),
    with: {
      user: {
        columns: { id: true, name: true, email: true },
      },
      team: {
        columns: { id: true, name: true, slug: true },
      },
      collections: {
        where: isNull(schema.collections.deletedAt),
        with: {
          documents: {
            where: isNull(schema.documents.deletedAt),
          },
        },
      },
    },
    orderBy: desc(schema.projects.updatedAt),
  });

  // Transform to detailed projects
  return projects.map((project) => {
    const collectionsCount = project.collections.length;
    const documentsCount = project.collections.reduce(
      (total, collection) => total + collection.documents.length,
      0
    );

    // Determine permissions
    const isOwner = project.userId === userId;
    const isTeamMember = Boolean(teamContext && project.teamId === teamContext.teamId);
    const hasManagePermission = Boolean(teamContext?.permissions.includes('manage.project'));

    const canEdit = isOwner || (isTeamMember && hasManagePermission);
    const canDelete = isOwner || (isTeamMember && hasManagePermission);
    const canShare = isOwner || (isTeamMember && hasManagePermission);

    return {
      ...project,
      owner: project.user,
      team: project.team || undefined,
      collectionsCount,
      documentsCount,
      canEdit,
      canDelete,
      canShare,
    };
  });
}

/**
 * Update project
 *
 * @param projectId - Project ID
 * @param updateParams - Update parameters
 * @param userId - Current user ID for permission checking
 * @param teamContext - Optional team context for permission checking
 * @returns Updated project with details or null if not found/unauthorized
 */
export async function updateProject(
  projectId: string,
  updateParams: IUpdateProjectParams,
  userId: string,
  teamContext?: ITeamContext
): Promise<IProjectWithDetails | null> {
  const db = await getDB();

  // First check if project exists and user has permission
  const existingProject = await getProjectWithDetails(projectId, userId, teamContext);
  if (!existingProject || !existingProject.canEdit) {
    return null;
  }

  const updateData: Partial<TProject> = {
    updatedAt: new Date(),
  };

  if (updateParams.name !== undefined) {
    updateData.name = updateParams.name;
  }

  if (updateParams.description !== undefined) {
    updateData.description = updateParams.description;
  }

  if (updateParams.isPublic !== undefined) {
    updateData.isPublic = updateParams.isPublic;
  }

  const [updatedProject] = await db
    .update(schema.projects)
    .set(updateData)
    .where(and(eq(schema.projects.id, projectId), isNull(schema.projects.deletedAt)))
    .returning();

  if (!updatedProject) return null;

  return getProjectWithDetails(projectId, userId, teamContext);
}

/**
 * Soft delete project
 *
 * @param projectId - Project ID
 * @param userId - Current user ID for permission checking
 * @param teamContext - Optional team context for permission checking
 * @returns True if deleted successfully
 */
export async function deleteProject(
  projectId: string,
  userId: string,
  teamContext?: ITeamContext
): Promise<boolean> {
  const db = await getDB();

  // First check if project exists and user has permission
  const existingProject = await getProjectWithDetails(projectId, userId, teamContext);
  if (!existingProject || !existingProject.canDelete) {
    return false;
  }

  const [deletedProject] = await db
    .update(schema.projects)
    .set({
      deletedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(and(eq(schema.projects.id, projectId), isNull(schema.projects.deletedAt)))
    .returning();

  return !!deletedProject;
}

// ============================================================================
// Project Sharing & Collaboration
// ============================================================================

/**
 * Share project with a team
 *
 * @param params - Project sharing parameters
 * @returns True if shared successfully
 */
export async function shareProjectWithTeam(params: IProjectSharingParams): Promise<boolean> {
  const db = await getDB();

  // Update project to associate with team
  const [updatedProject] = await db
    .update(schema.projects)
    .set({
      teamId: params.teamId,
      updatedAt: new Date(),
    })
    .where(and(eq(schema.projects.id, params.projectId), isNull(schema.projects.deletedAt)))
    .returning();

  return !!updatedProject;
}

/**
 * Remove project from team (unshare)
 *
 * @param projectId - Project ID
 * @param userId - Current user ID for permission checking
 * @returns True if unshared successfully
 */
export async function unshareProject(projectId: string, userId: string): Promise<boolean> {
  const db = await getDB();

  // Only project owner can unshare
  const [updatedProject] = await db
    .update(schema.projects)
    .set({
      teamId: null,
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(schema.projects.id, projectId),
        eq(schema.projects.userId, userId),
        isNull(schema.projects.deletedAt)
      )
    )
    .returning();

  return !!updatedProject;
}
