/**
 * Permission Service
 *
 * Comprehensive service for handling RBAC permissions, validation, and audit logging.
 * Provides centralized permission checking with resource-level access control.
 */

import * as schema from '@dbSchema';
import getDB from '@utils/getDB';
import {
  getEffectivePermissions,
  isValidPermission,
  parsePermission,
  ROLE_DEFAULT_PERMISSIONS,
  resolvePermissionInheritance,
} from '@utils/permission-constants';
import { and, eq, isNull } from 'drizzle-orm';
import type {
  IEnhancedPermissionCheckResult,
  IPermissionCheckOptions,
  IPermissionService,
  IPermissionValidationResult,
  IResourcePermissionContext,
  TPermission,
} from '@/types/permissions';
import type { TTeamRole } from '@/types/teams';

/**
 * Permission Service Implementation
 */
export class PermissionService implements IPermissionService {
  /**
   * Check if user has a specific permission
   */
  async hasPermission(
    userId: string,
    permission: TPermission,
    context?: IResourcePermissionContext,
    options: IPermissionCheckOptions = {}
  ): Promise<IEnhancedPermissionCheckResult> {
    try {
      // Validate permission
      if (!isValidPermission(permission)) {
        return {
          allowed: false,
          reason: `Invalid permission: ${permission}`,
          checkedPermissions: [permission],
        };
      }

      // Get user's effective permissions
      const userPermissions = await this.getUserPermissions(userId, context?.teamId, context);

      // Check if user has the specific permission
      const hasDirectPermission = userPermissions.includes(permission);

      if (hasDirectPermission) {
        return {
          allowed: true,
          checkedPermissions: [permission],
          effectivePermissions: userPermissions,
        };
      }

      // Check role inheritance if enabled
      if (options.allowInheritance !== false && context?.teamId) {
        const teamMember = await this.getTeamMember(userId, context.teamId);
        if (teamMember) {
          const userRole = teamMember.role as TTeamRole;

          // Use the comprehensive inheritance system
          const inheritanceResult = resolvePermissionInheritance(userRole, permission);

          if (inheritanceResult.hasPermission) {
            return {
              allowed: true,
              checkedPermissions: [permission],
              userRole,
              effectivePermissions: userPermissions,
              inheritedFromRole: inheritanceResult.source === 'inherited',
            };
          }
        }
      }

      return {
        allowed: false,
        reason: `Missing required permission: ${permission}`,
        requiredPermission: permission,
        checkedPermissions: [permission],
        effectivePermissions: userPermissions,
      };
    } catch (error) {
      console.error('Permission check error:', error);
      return {
        allowed: false,
        reason: 'Permission check failed due to system error',
        checkedPermissions: [permission],
      };
    }
  }

  /**
   * Check if user has any of the specified permissions
   */
  async hasAnyPermission(
    userId: string,
    permissions: TPermission[],
    context?: IResourcePermissionContext,
    options: IPermissionCheckOptions = {}
  ): Promise<IEnhancedPermissionCheckResult> {
    try {
      const userPermissions = await this.getUserPermissions(userId, context?.teamId, context);

      // Check each permission
      for (const permission of permissions) {
        if (!isValidPermission(permission)) {
          continue;
        }

        if (userPermissions.includes(permission)) {
          return {
            allowed: true,
            checkedPermissions: permissions,
            effectivePermissions: userPermissions,
          };
        }
      }

      // Check role inheritance if enabled
      if (options.allowInheritance !== false && context?.teamId) {
        const teamMember = await this.getTeamMember(userId, context.teamId);
        if (teamMember) {
          const rolePermissions = ROLE_DEFAULT_PERMISSIONS[teamMember.role as TTeamRole];

          for (const permission of permissions) {
            if (rolePermissions.includes(permission)) {
              return {
                allowed: true,
                checkedPermissions: permissions,
                userRole: teamMember.role as TTeamRole,
                effectivePermissions: userPermissions,
                inheritedFromRole: true,
              };
            }
          }
        }
      }

      return {
        allowed: false,
        reason: `Missing any of required permissions: ${permissions.join(', ')}`,
        requiredPermissions: permissions,
        checkedPermissions: permissions,
        effectivePermissions: userPermissions,
      };
    } catch (error) {
      console.error('Permission check error:', error);
      return {
        allowed: false,
        reason: 'Permission check failed due to system error',
        checkedPermissions: permissions,
      };
    }
  }

  /**
   * Check if user has all specified permissions
   */
  async hasAllPermissions(
    userId: string,
    permissions: TPermission[],
    context?: IResourcePermissionContext,
    options: IPermissionCheckOptions = {}
  ): Promise<IEnhancedPermissionCheckResult> {
    try {
      const userPermissions = await this.getUserPermissions(userId, context?.teamId, context);

      // Check all permissions
      const missingPermissions = permissions.filter(
        (permission) => isValidPermission(permission) && !userPermissions.includes(permission)
      );

      if (missingPermissions.length === 0) {
        return {
          allowed: true,
          checkedPermissions: permissions,
          effectivePermissions: userPermissions,
        };
      }

      // Check role inheritance if enabled
      if (options.allowInheritance !== false && context?.teamId) {
        const teamMember = await this.getTeamMember(userId, context.teamId);
        if (teamMember) {
          const rolePermissions = ROLE_DEFAULT_PERMISSIONS[teamMember.role as TTeamRole];
          const stillMissing = missingPermissions.filter(
            (permission) => !rolePermissions.includes(permission)
          );

          if (stillMissing.length === 0) {
            return {
              allowed: true,
              checkedPermissions: permissions,
              userRole: teamMember.role as TTeamRole,
              effectivePermissions: userPermissions,
              inheritedFromRole: true,
            };
          }
        }
      }

      return {
        allowed: false,
        reason: `Missing required permissions: ${missingPermissions.join(', ')}`,
        requiredPermissions: missingPermissions,
        checkedPermissions: permissions,
        effectivePermissions: userPermissions,
      };
    } catch (error) {
      console.error('Permission check error:', error);
      return {
        allowed: false,
        reason: 'Permission check failed due to system error',
        checkedPermissions: permissions,
      };
    }
  }

  /**
   * Get user's effective permissions
   */
  async getUserPermissions(
    userId: string,
    teamId?: string,
    _context?: IResourcePermissionContext
  ): Promise<TPermission[]> {
    try {
      if (!teamId) {
        return [];
      }

      const teamMember = await this.getTeamMember(userId, teamId);
      if (!teamMember) {
        return [];
      }

      // Parse custom permissions
      const customPermissions: TPermission[] = teamMember.permissions
        ? JSON.parse(teamMember.permissions)
        : [];

      // Get effective permissions (role + custom)
      return getEffectivePermissions(teamMember.role as TTeamRole, customPermissions);
    } catch (error) {
      console.error('Get user permissions error:', error);
      return [];
    }
  }

  /**
   * Validate permission configuration
   */
  validatePermissions(permissions: string[]): IPermissionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    for (const permission of permissions) {
      if (!isValidPermission(permission)) {
        errors.push(`Invalid permission: ${permission}`);

        // Try to suggest similar permissions
        const { action, category } = parsePermission(permission as TPermission);
        if (action && category) {
          suggestions.push(`Did you mean: ${action}.${category}?`);
        }
      }
    }

    // Check for duplicate permissions
    const duplicates = permissions.filter((item, index) => permissions.indexOf(item) !== index);
    if (duplicates.length > 0) {
      warnings.push(`Duplicate permissions found: ${duplicates.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }

  /**
   * Log permission check for audit
   */
  async logPermissionCheck(
    userId: string,
    permission: TPermission,
    result: IEnhancedPermissionCheckResult,
    context?: IResourcePermissionContext
  ): Promise<void> {
    try {
      const db = await getDB();

      await db.insert(schema.auditLogs).values({
        userId,
        teamId: context?.teamId || null,
        action: result.allowed ? 'permission.granted' : 'permission.denied',
        resource: 'permission',
        resourceId: context?.resourceId || null,
        details: JSON.stringify({
          permission,
          permission_result: result.allowed ? 'granted' : 'denied',
          reason: result.reason,
          role: result.userRole,
          effective_permissions: result.effectivePermissions,
          inherited_from_role: result.inheritedFromRole,
          resource_type: context?.resourceType,
          resource_id: context?.resourceId,
          risk_level: result.allowed ? 'low' : 'medium',
        }),
        ipAddress: null,
        userAgent: null,
      });
    } catch (error) {
      console.error('Permission audit log error:', error);
      // Don't throw error for audit logging failures
    }
  }

  /**
   * Check permission inheritance for a user
   */
  async checkPermissionInheritance(
    userId: string,
    permission: TPermission,
    teamId: string
  ): Promise<{
    hasPermission: boolean;
    source: 'direct' | 'inherited' | 'custom' | 'none';
    sourceRole?: TTeamRole;
    inheritanceChain: TTeamRole[];
    userRole?: TTeamRole;
  }> {
    try {
      const teamMember = await this.getTeamMember(userId, teamId);
      if (!teamMember) {
        return {
          hasPermission: false,
          source: 'none',
          inheritanceChain: [],
        };
      }

      const userRole = teamMember.role as TTeamRole;

      // Check custom permissions first
      const customPermissions: TPermission[] = teamMember.permissions
        ? JSON.parse(teamMember.permissions)
        : [];

      if (customPermissions.includes(permission)) {
        return {
          hasPermission: true,
          source: 'custom',
          userRole,
          inheritanceChain: [userRole],
        };
      }

      // Check role-based inheritance
      const inheritanceResult = resolvePermissionInheritance(userRole, permission);

      return {
        hasPermission: inheritanceResult.hasPermission,
        source: inheritanceResult.source,
        sourceRole: inheritanceResult.sourceRole,
        inheritanceChain: inheritanceResult.inheritanceChain,
        userRole,
      };
    } catch (error) {
      console.error('Check permission inheritance error:', error);
      return {
        hasPermission: false,
        source: 'none',
        inheritanceChain: [],
      };
    }
  }

  /**
   * Get team member information
   */
  private async getTeamMember(userId: string, teamId: string) {
    try {
      const db = await getDB();

      const teamMember = await db
        .select()
        .from(schema.teamMembers)
        .where(
          and(
            eq(schema.teamMembers.userId, userId),
            eq(schema.teamMembers.teamId, teamId),
            isNull(schema.teamMembers.deletedAt)
          )
        )
        .limit(1);

      return teamMember[0] || null;
    } catch (error) {
      console.error('Get team member error:', error);
      return null;
    }
  }
}

// Lazy singleton instance
let permissionServiceInstance: PermissionService | null = null;

/**
 * Get or create permission service instance (lazy initialization)
 */
export const getPermissionService = (): PermissionService => {
  if (!permissionServiceInstance) {
    permissionServiceInstance = new PermissionService();
  }
  return permissionServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const permissionService = new Proxy({} as PermissionService, {
  get(_target, prop) {
    const service = getPermissionService();
    const value = service[prop as keyof PermissionService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  },
});
