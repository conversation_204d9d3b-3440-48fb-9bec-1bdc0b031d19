/**
 * Team Service
 *
 * Service layer for team management operations including CRUD operations,
 * member management, and permission handling.
 */

import type { TNewTeam, TNewTeamMember, TTeamMember } from '@dbSchema';
import * as schema from '@dbSchema';
import getDB from '@utils/getDB';
import { getEffectivePermissions } from '@utils/permissions';
import { and, desc, eq, isNull } from 'drizzle-orm';
import type {
  IAddTeamMemberRequest,
  ICreateTeamRequest,
  ITeamMemberWithDetails,
  ITeamWithDetails,
  IUpdateTeamMemberRequest,
  IUpdateTeamRequest,
  TTeamRole,
} from '@/types/teams';

// Re-export types for use in routes
export type {
  ICreateTeamRequest,
  IUpdateTeamRequest,
  IAddTeamMemberRequest,
  IUpdateTeamMemberRequest,
};

// ============================================================================
// Team CRUD Operations
// ============================================================================

/**
 * Create a new team
 *
 * @param ownerId - ID of the user creating the team
 * @param teamData - Team creation data
 * @returns Created team with details
 */
export async function createTeam(
  ownerId: string,
  teamData: ICreateTeamRequest
): Promise<ITeamWithDetails> {
  const db = await getDB();

  // Create the team
  const newTeam: TNewTeam = {
    name: teamData.name,
    slug: teamData.slug,
    description: teamData.description,
    ownerId,
    planType: teamData.planType || 'free',
  };

  const [team] = await db.insert(schema.teams).values(newTeam).returning();

  // Add the owner as a team member
  const ownerMember: TNewTeamMember = {
    teamId: team.id,
    userId: ownerId,
    role: 'owner',
    permissions: null, // Owner gets all permissions by default
    invitedBy: null,
  };

  await db.insert(schema.teamMembers).values(ownerMember);

  // Return team with details
  const teamWithDetails = await getTeamWithDetails(team.id);
  if (!teamWithDetails) {
    throw new Error('Failed to retrieve created team');
  }
  return teamWithDetails;
}

/**
 * Get team by ID with details
 *
 * @param teamId - Team ID
 * @returns Team with details or null if not found
 */
export async function getTeamWithDetails(teamId: string): Promise<ITeamWithDetails | null> {
  const db = await getDB();

  const team = await db.query.teams.findFirst({
    where: and(eq(schema.teams.id, teamId), isNull(schema.teams.deletedAt)),
    with: {
      owner: {
        columns: { id: true, name: true, email: true },
      },
      members: {
        where: isNull(schema.teamMembers.deletedAt),
      },
      projects: {
        where: isNull(schema.projects.deletedAt),
      },
      billingSubscriptions: {
        orderBy: desc(schema.billingSubscriptions.createdAt),
        limit: 1,
      },
    },
  });

  if (!team) return null;

  // Get plan limits
  const planLimits = getTeamPlanLimits(team.planType);

  return {
    ...team,
    memberCount: team.members.length,
    projectCount: team.projects.length,
    currentSubscription: team.billingSubscriptions[0] || undefined,
    planLimits,
  };
}

/**
 * Update team information
 *
 * @param teamId - Team ID
 * @param updateData - Data to update
 * @returns Updated team with details
 */
export async function updateTeam(
  teamId: string,
  updateData: IUpdateTeamRequest
): Promise<ITeamWithDetails | null> {
  const db = await getDB();

  const [updatedTeam] = await db
    .update(schema.teams)
    .set({
      ...updateData,
      updatedAt: new Date(),
    })
    .where(and(eq(schema.teams.id, teamId), isNull(schema.teams.deletedAt)))
    .returning();

  if (!updatedTeam) return null;

  return getTeamWithDetails(teamId);
}

/**
 * Soft delete a team
 *
 * @param teamId - Team ID
 * @returns True if deleted successfully
 */
export async function deleteTeam(teamId: string): Promise<boolean> {
  const db = await getDB();

  const [deletedTeam] = await db
    .update(schema.teams)
    .set({
      deletedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(and(eq(schema.teams.id, teamId), isNull(schema.teams.deletedAt)))
    .returning();

  return !!deletedTeam;
}

// ============================================================================
// Team Member Operations
// ============================================================================

/**
 * Get team members with details
 *
 * @param teamId - Team ID
 * @returns Array of team members with user details
 */
export async function getTeamMembers(teamId: string): Promise<ITeamMemberWithDetails[]> {
  const db = await getDB();

  const members = await db.query.teamMembers.findMany({
    where: and(eq(schema.teamMembers.teamId, teamId), isNull(schema.teamMembers.deletedAt)),
    with: {
      user: {
        columns: { id: true, name: true, email: true, image: true },
      },
      invitedByUser: {
        columns: { id: true, name: true, email: true },
      },
    },
    orderBy: desc(schema.teamMembers.joinedAt),
  });

  return members.map((member) => ({
    ...member,
    user: {
      ...member.user,
      image: member.user.image || undefined,
    },
    invitedByUser: member.invitedByUser || undefined,
    effectivePermissions: getEffectivePermissions(
      member.role as TTeamRole,
      member.permissions ? JSON.parse(member.permissions) : []
    ),
  }));
}

/**
 * Add a member to a team
 *
 * @param teamId - Team ID
 * @param memberData - Member data
 * @param invitedBy - ID of user sending invitation
 * @returns Created team member with details
 */
export async function addTeamMember(
  teamId: string,
  memberData: IAddTeamMemberRequest,
  invitedBy: string
): Promise<ITeamMemberWithDetails | null> {
  const db = await getDB();

  // If adding by email, we need to find or create the user
  let userId = memberData.userId;
  if (!userId && memberData.email) {
    const user = await db.query.users.findFirst({
      where: eq(schema.users.email, memberData.email),
    });

    if (!user) {
      // For now, we'll require the user to exist
      // In a full implementation, you'd send an invitation email
      throw new Error('User not found. User must have an account to be added to team.');
    }

    userId = user.id;
  }

  if (!userId) {
    throw new Error('Either userId or email must be provided');
  }

  // Check if user is already a member
  const existingMember = await db.query.teamMembers.findFirst({
    where: and(
      eq(schema.teamMembers.teamId, teamId),
      eq(schema.teamMembers.userId, userId),
      isNull(schema.teamMembers.deletedAt)
    ),
  });

  if (existingMember) {
    throw new Error('User is already a member of this team');
  }

  // Create team member
  const newMember: TNewTeamMember = {
    teamId,
    userId,
    role: memberData.role,
    permissions: memberData.permissions ? JSON.stringify(memberData.permissions) : null,
    invitedBy,
  };

  const [member] = await db.insert(schema.teamMembers).values(newMember).returning();

  // Return member with details
  const memberWithDetails = await db.query.teamMembers.findFirst({
    where: eq(schema.teamMembers.id, member.id),
    with: {
      user: {
        columns: { id: true, name: true, email: true, image: true },
      },
      invitedByUser: {
        columns: { id: true, name: true, email: true },
      },
    },
  });

  if (!memberWithDetails) return null;

  return {
    ...memberWithDetails,
    user: {
      ...memberWithDetails.user,
      image: memberWithDetails.user.image || undefined,
    },
    invitedByUser: memberWithDetails.invitedByUser || undefined,
    effectivePermissions: getEffectivePermissions(
      memberWithDetails.role as TTeamRole,
      memberWithDetails.permissions ? JSON.parse(memberWithDetails.permissions) : []
    ),
  };
}

/**
 * Update team member role and permissions
 *
 * @param teamId - Team ID
 * @param userId - User ID
 * @param updateData - Update data
 * @returns Updated team member with details
 */
export async function updateTeamMember(
  teamId: string,
  userId: string,
  updateData: IUpdateTeamMemberRequest
): Promise<ITeamMemberWithDetails | null> {
  const db = await getDB();

  const updateValues: Partial<TTeamMember> = {
    updatedAt: new Date(),
  };

  if (updateData.role) {
    updateValues.role = updateData.role;
  }

  if (updateData.permissions) {
    updateValues.permissions = JSON.stringify(updateData.permissions);
  }

  const [updatedMember] = await db
    .update(schema.teamMembers)
    .set(updateValues)
    .where(
      and(
        eq(schema.teamMembers.teamId, teamId),
        eq(schema.teamMembers.userId, userId),
        isNull(schema.teamMembers.deletedAt)
      )
    )
    .returning();

  if (!updatedMember) return null;

  // Return member with details
  const memberWithDetails = await db.query.teamMembers.findFirst({
    where: eq(schema.teamMembers.id, updatedMember.id),
    with: {
      user: {
        columns: { id: true, name: true, email: true, image: true },
      },
      invitedByUser: {
        columns: { id: true, name: true, email: true },
      },
    },
  });

  if (!memberWithDetails) return null;

  return {
    ...memberWithDetails,
    user: {
      ...memberWithDetails.user,
      image: memberWithDetails.user.image || undefined,
    },
    invitedByUser: memberWithDetails.invitedByUser || undefined,
    effectivePermissions: getEffectivePermissions(
      memberWithDetails.role as TTeamRole,
      memberWithDetails.permissions ? JSON.parse(memberWithDetails.permissions) : []
    ),
  };
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Get team plan limits (placeholder implementation)
 */
function getTeamPlanLimits(planType: string) {
  // This would be imported from types/teams.ts in a real implementation
  const limits = {
    free: { maxMembers: 3, maxProjects: 2, maxVectorStorage: 100, maxApiCalls: 1000, features: [] },
    pro: {
      maxMembers: 10,
      maxProjects: 10,
      maxVectorStorage: 1000,
      maxApiCalls: 10000,
      features: [],
    },
    enterprise: {
      maxMembers: -1,
      maxProjects: -1,
      maxVectorStorage: -1,
      maxApiCalls: -1,
      features: [],
    },
  };

  return limits[planType as keyof typeof limits] || limits.free;
}
