/**
 * Paddle Service
 *
 * Paddle payment provider integration for subscription management,
 * webhook handling, and billing operations.
 */

import { Environment, Paddle } from '@paddle/paddle-node-sdk';
import { getEnv } from '@utils';
import type {
  ICreateSubscriptionParams,
  IUpdateSubscriptionParams,
  IWebhookEvent,
} from '@/types/billing';
import { createAuditLog } from './auditService';
import { billingService } from './billingService';

// ============================================================================
// Paddle Service Class
// ============================================================================

export class PaddleService {
  private paddle: Paddle | null = null;

  /**
   * Initialize Paddle SDK
   */
  private async initializePaddle(): Promise<Paddle> {
    if (this.paddle) {
      return this.paddle;
    }

    const env = await getEnv();

    if (!env.PADDLE_API_KEY) {
      throw new Error('PADDLE_API_KEY environment variable is required');
    }

    this.paddle = new Paddle(env.PADDLE_API_KEY, {
      environment:
        (env.PADDLE_ENVIRONMENT as string) === 'production'
          ? Environment.production
          : Environment.sandbox,
    });

    return this.paddle;
  }

  /**
   * Create a subscription with Paddle
   */
  async createSubscription(params: ICreateSubscriptionParams): Promise<{
    localSubscription: {
      id: string;
      teamId: string;
      planId: string;
      status: string;
    };
    paddleSubscription: {
      id: string;
      status: string;
      items: Array<{ priceId: string; quantity: number }>;
      checkout_url?: string;
    };
  }> {
    await this.initializePaddle();

    try {
      // Get plan configuration
      const planConfig = billingService.getAvailablePlans().find((p) => p.id === params.planId);
      if (!planConfig) {
        throw new Error(`Plan ${params.planId} not found`);
      }

      // Get Paddle plan ID based on interval
      const paddlePlanId =
        params.interval === 'monthly'
          ? planConfig.pricing.monthly.paddlePlanId
          : planConfig.pricing.yearly.paddlePlanId;

      if (!paddlePlanId) {
        throw new Error(`Paddle plan ID not configured for ${params.planId} ${params.interval}`);
      }

      // Create subscription with Paddle
      // Note: Paddle subscriptions are typically created through checkout flows
      // For now, we'll create a placeholder subscription and handle the actual
      // Paddle subscription creation through webhooks
      const subscription = {
        id: `paddle_sub_${params.teamId}_${Date.now()}`,
        status: 'pending',
        items: [
          {
            priceId: paddlePlanId,
            quantity: 1,
          },
        ],
        customerId: params.teamId,
        billingDetails: {
          enableCheckout: true,
          purchaseOrderNumber: `team-${params.teamId}`,
        },
        ...(params.trialDays && {
          scheduledChange: {
            action: 'pause',
            effectiveAt: new Date(
              Date.now() + params.trialDays * 24 * 60 * 60 * 1000
            ).toISOString(),
          },
        }),
      };

      // Create local subscription record
      const localSubscription = await billingService.createSubscription({
        ...params,
        provider: 'paddle',
      });

      // Log the subscription creation
      await createAuditLog({
        teamId: params.teamId,
        action: 'billing.subscription_create',
        resource: 'billing_subscription',
        resourceId: localSubscription.id,
        details: {
          provider: 'paddle',
          planId: params.planId,
          interval: params.interval,
          paddleSubscriptionId: subscription.id,
        },
      });

      return {
        localSubscription,
        paddleSubscription: subscription,
      };
    } catch (error) {
      console.error('Paddle subscription creation failed:', error);
      throw new Error(
        `Failed to create Paddle subscription: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Update a subscription with Paddle
   */
  async updateSubscription(
    subscriptionId: string,
    params: IUpdateSubscriptionParams
  ): Promise<{
    id: string;
    teamId: string;
    planId: string;
    status: string;
  }> {
    const paddle = await this.initializePaddle();

    try {
      // Get local subscription
      const localSubscription = await billingService.getSubscriptionById(subscriptionId);
      if (!localSubscription) {
        throw new Error('Subscription not found');
      }

      // Update with Paddle if plan is changing
      if (params.planId) {
        const planConfig = billingService.getAvailablePlans().find((p) => p.id === params.planId);
        if (!planConfig) {
          throw new Error(`Plan ${params.planId} not found`);
        }

        // Get new Paddle plan ID
        const paddlePlanId = planConfig.pricing.monthly.paddlePlanId;
        if (!paddlePlanId) {
          throw new Error(`Paddle plan ID not configured for ${params.planId}`);
        }

        // Update subscription with Paddle
        await paddle.subscriptions.update(subscriptionId, {
          items: [
            {
              priceId: paddlePlanId,
              quantity: 1,
            },
          ],
        });
      }

      // Update local subscription
      const updatedSubscription = await billingService.updateSubscription(subscriptionId, params);

      // Log the subscription update
      await createAuditLog({
        teamId: localSubscription.teamId,
        action: 'billing.subscription_update',
        resource: 'billing_subscription',
        resourceId: subscriptionId,
        details: {
          provider: 'paddle',
          changes: params,
        },
      });

      if (!updatedSubscription) {
        throw new Error('Failed to update subscription');
      }

      return {
        id: updatedSubscription.id,
        teamId: updatedSubscription.teamId,
        planId: updatedSubscription.planId,
        status: updatedSubscription.status,
      };
    } catch (error) {
      console.error('Paddle subscription update failed:', error);
      throw new Error(
        `Failed to update Paddle subscription: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Cancel a subscription with Paddle
   */
  async cancelSubscription(subscriptionId: string): Promise<void> {
    const paddle = await this.initializePaddle();

    try {
      // Get local subscription
      const localSubscription = await billingService.getSubscriptionById(subscriptionId);
      if (!localSubscription) {
        throw new Error('Subscription not found');
      }

      // Cancel with Paddle
      await paddle.subscriptions.cancel(subscriptionId, {
        effectiveFrom: 'next_billing_period',
      });

      // Update local subscription
      await billingService.cancelSubscription(subscriptionId);

      // Log the subscription cancellation
      await createAuditLog({
        teamId: localSubscription.teamId,
        action: 'billing.subscription_cancel',
        resource: 'billing_subscription',
        resourceId: subscriptionId,
        details: {
          provider: 'paddle',
          effectiveFrom: 'next_billing_period',
        },
      });

      // Subscription canceled successfully
    } catch (error) {
      console.error('Paddle subscription cancellation failed:', error);
      throw new Error(
        `Failed to cancel Paddle subscription: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Handle Paddle webhook events
   */
  async handleWebhook(event: IWebhookEvent): Promise<void> {
    try {
      console.log(`Processing Paddle webhook: ${event.type}`);

      switch (event.type) {
        case 'subscription.created':
          await this.handleSubscriptionCreated(event);
          break;
        case 'subscription.updated':
          await this.handleSubscriptionUpdated(event);
          break;
        case 'subscription.canceled':
          await this.handleSubscriptionCanceled(event);
          break;
        case 'transaction.completed':
          await this.handleTransactionCompleted(event);
          break;
        case 'transaction.payment_failed':
          await this.handlePaymentFailed(event);
          break;
        default:
          console.log(`Unhandled Paddle webhook event: ${event.type}`);
      }
    } catch (error) {
      console.error('Paddle webhook handling failed:', error);
      throw error;
    }
  }

  /**
   * Verify Paddle webhook signature
   */
  async verifyWebhookSignature(payload: string, signature: string): Promise<boolean> {
    const env = await getEnv();

    if (!env.PADDLE_WEBHOOK_SECRET) {
      console.warn('PADDLE_WEBHOOK_SECRET not configured, skipping signature verification');
      return true; // Allow in development
    }

    try {
      // Paddle uses HMAC SHA256 for webhook signatures
      const encoder = new TextEncoder();
      const key = await crypto.subtle.importKey(
        'raw',
        encoder.encode(env.PADDLE_WEBHOOK_SECRET),
        { name: 'HMAC', hash: 'SHA-256' },
        false,
        ['sign']
      );

      const signatureBuffer = await crypto.subtle.sign('HMAC', key, encoder.encode(payload));
      const expectedSignature = Array.from(new Uint8Array(signatureBuffer))
        .map((b) => b.toString(16).padStart(2, '0'))
        .join('');

      return signature === expectedSignature;
    } catch (error) {
      console.error('Paddle webhook signature verification failed:', error);
      return false;
    }
  }

  // ============================================================================
  // Private Webhook Handlers
  // ============================================================================

  private async handleSubscriptionCreated(event: IWebhookEvent): Promise<void> {
    // Implementation for subscription created webhook
    console.log('Handling subscription created:', event.data);
  }

  private async handleSubscriptionUpdated(event: IWebhookEvent): Promise<void> {
    // Implementation for subscription updated webhook
    console.log('Handling subscription updated:', event.data);
  }

  private async handleSubscriptionCanceled(event: IWebhookEvent): Promise<void> {
    // Implementation for subscription canceled webhook
    console.log('Handling subscription canceled:', event.data);
  }

  private async handleTransactionCompleted(event: IWebhookEvent): Promise<void> {
    // Implementation for transaction completed webhook
    console.log('Handling transaction completed:', event.data);
  }

  private async handlePaymentFailed(event: IWebhookEvent): Promise<void> {
    // Implementation for payment failed webhook
    console.log('Handling payment failed:', event.data);
  }
}

// ============================================================================
// Service Instance
// ============================================================================

// Lazy singleton instance
let paddleServiceInstance: PaddleService | null = null;

/**
 * Get or create Paddle service instance (lazy initialization)
 */
export const getPaddleService = (): PaddleService => {
  if (!paddleServiceInstance) {
    paddleServiceInstance = new PaddleService();
  }
  return paddleServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const paddleService = new Proxy({} as PaddleService, {
  get(_target, prop) {
    const service = getPaddleService();
    const value = service[prop as keyof PaddleService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  },
});
