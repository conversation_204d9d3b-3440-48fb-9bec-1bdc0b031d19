/**
 * Billing Service
 *
 * Core billing service providing abstraction layer over multiple
 * payment providers (Paddle, PayPal) with subscription management,
 * usage tracking, and billing operations.
 */

import { getPlanConfig, SUBSCRIPTION_PLANS } from '@config/billing-plans';
import type {
  TAdditionalApiRequest,
  TBillingSubscription,
  TBillingTransaction,
  TNewAdditionalApiRequest,
  TNewBillingSubscription,
  TNewBillingTransaction,
  TNewPaymentMethod,
  TPaymentMethod as TPaymentMethodDB,
} from '@dbSchema';
import * as schema from '@dbSchema';
import getDB from '@utils/getDB';
import { and, desc, eq, gte, isNull, lte, sql } from 'drizzle-orm';
import type {
  IAdditionalApiRequestsData,
  IBillingDashboard,
  IBillingError,
  ICreateSubscriptionParams,
  IPurchaseApiRequestsParams,
  ISubscriptionPlanConfig,
  IUpdateSubscriptionParams,
  IUsageData,
  TBillingProvider,
  TPaymentMethod,
  TSubscriptionPlan,
  TSubscriptionStatus,
} from '@/types/billing';

// ============================================================================
// Billing Service Class
// ============================================================================

export class BillingService {
  /**
   * Create a new subscription
   */
  async createSubscription(params: ICreateSubscriptionParams): Promise<TBillingSubscription> {
    const db = await getDB();

    // Validate plan exists
    const planConfig = getPlanConfig(params.planId);
    if (!planConfig) {
      throw this.createBillingError('plan_not_found', `Plan ${params.planId} not found`);
    }

    // Check if team already has an active subscription
    const existingSubscription = await this.getActiveSubscription(params.teamId);
    if (existingSubscription) {
      throw this.createBillingError(
        'subscription_not_found',
        'Team already has an active subscription'
      );
    }

    // Calculate subscription period
    const now = new Date();
    const periodEnd = new Date(now);

    if (params.interval === 'monthly') {
      periodEnd.setMonth(periodEnd.getMonth() + 1);
    } else {
      periodEnd.setFullYear(periodEnd.getFullYear() + 1);
    }

    // Create subscription record
    const subscriptionData: TNewBillingSubscription = {
      teamId: params.teamId,
      provider: params.provider,
      planId: params.planId,
      status: params.trialDays ? 'trialing' : 'active',
      currentPeriodStart: now,
      currentPeriodEnd: periodEnd,
    };

    const [subscription] = await db
      .insert(schema.billingSubscriptions)
      .values(subscriptionData)
      .returning();

    return subscription;
  }

  /**
   * Update an existing subscription
   */
  async updateSubscription(
    subscriptionId: string,
    params: IUpdateSubscriptionParams
  ): Promise<TBillingSubscription | null> {
    const db = await getDB();

    const updateData: Partial<TBillingSubscription> = {
      updatedAt: new Date(),
    };

    if (params.planId) {
      // Validate new plan exists
      const planConfig = getPlanConfig(params.planId);
      if (!planConfig) {
        throw this.createBillingError('plan_not_found', `Plan ${params.planId} not found`);
      }
      updateData.planId = params.planId;
    }

    const [updatedSubscription] = await db
      .update(schema.billingSubscriptions)
      .set(updateData)
      .where(eq(schema.billingSubscriptions.id, subscriptionId))
      .returning();

    return updatedSubscription || null;
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(subscriptionId: string): Promise<TBillingSubscription | null> {
    const db = await getDB();

    const [canceledSubscription] = await db
      .update(schema.billingSubscriptions)
      .set({
        status: 'canceled',
        updatedAt: new Date(),
      })
      .where(eq(schema.billingSubscriptions.id, subscriptionId))
      .returning();

    return canceledSubscription || null;
  }

  /**
   * Get active subscription for a team
   */
  async getActiveSubscription(teamId: string): Promise<TBillingSubscription | null> {
    const db = await getDB();

    const subscription = await db.query.billingSubscriptions.findFirst({
      where: and(
        eq(schema.billingSubscriptions.teamId, teamId),
        eq(schema.billingSubscriptions.status, 'active')
      ),
    });

    return subscription || null;
  }

  /**
   * Get subscription by ID
   */
  async getSubscriptionById(subscriptionId: string): Promise<TBillingSubscription | null> {
    const db = await getDB();

    const subscription = await db.query.billingSubscriptions.findFirst({
      where: eq(schema.billingSubscriptions.id, subscriptionId),
    });

    return subscription || null;
  }

  /**
   * Record a billing transaction
   */
  async recordTransaction(
    teamId: string,
    provider: TBillingProvider,
    transactionId: string,
    amount: number,
    currency: string,
    status: string
  ): Promise<TBillingTransaction> {
    const db = await getDB();

    const transactionData: TNewBillingTransaction = {
      teamId,
      provider,
      transactionId,
      amount,
      currency,
      status,
    };

    const [transaction] = await db
      .insert(schema.billingTransactions)
      .values(transactionData)
      .returning();

    return transaction;
  }

  /**
   * Get billing dashboard data for a team
   */
  async getBillingDashboard(teamId: string): Promise<IBillingDashboard> {
    const db = await getDB();

    // Get active subscription
    const subscription = await this.getActiveSubscription(teamId);

    // Get usage data
    const usage = await this.getUsageData(teamId);

    // Get recent transactions (invoices)
    const transactions = await db.query.billingTransactions.findMany({
      where: eq(schema.billingTransactions.teamId, teamId),
      orderBy: [desc(schema.billingTransactions.createdAt)],
      limit: 10,
    });

    const invoices = transactions.map((transaction) => ({
      id: transaction.id,
      amount: transaction.amount,
      currency: transaction.currency,
      status: transaction.status,
      createdAt: transaction.createdAt,
      paidAt: transaction.status === 'completed' ? transaction.createdAt : undefined,
    }));

    return {
      subscription: subscription
        ? {
            id: subscription.id,
            planId: subscription.planId as TSubscriptionPlan,
            status: subscription.status as TSubscriptionStatus,
            currentPeriodStart: subscription.currentPeriodStart,
            currentPeriodEnd: subscription.currentPeriodEnd,
            cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
          }
        : undefined,
      usage,
      paymentMethods: await this.getPaymentMethods(teamId),
      invoices,
    };
  }

  /**
   * Get usage data for a team
   */
  async getUsageData(teamId: string, startDate?: Date, endDate?: Date): Promise<IUsageData> {
    const db = await getDB();

    // Default to current month if no dates provided
    const now = new Date();
    const start = startDate || new Date(now.getFullYear(), now.getMonth(), 1);
    const end = endDate || new Date(now.getFullYear(), now.getMonth() + 1, 0);

    // Get team data
    const team = await db.query.teams.findFirst({
      where: eq(schema.teams.id, teamId),
      with: {
        members: true,
        projects: true,
        collections: true,
        apiKeys: true,
      },
    });

    if (!team) {
      throw this.createBillingError('subscription_not_found', 'Team not found');
    }

    // Get usage metrics from audit logs
    const apiRequestsResult = await db
      .select({ count: schema.auditLogs.id })
      .from(schema.auditLogs)
      .where(
        and(
          eq(schema.auditLogs.teamId, teamId),
          gte(schema.auditLogs.createdAt, start),
          lte(schema.auditLogs.createdAt, end),
          eq(schema.auditLogs.action, 'api.request')
        )
      );

    const searchRequestsResult = await db
      .select({ count: schema.auditLogs.id })
      .from(schema.auditLogs)
      .where(
        and(
          eq(schema.auditLogs.teamId, teamId),
          gte(schema.auditLogs.createdAt, start),
          lte(schema.auditLogs.createdAt, end),
          eq(schema.auditLogs.action, 'vector.search')
        )
      );

    // Get additional API requests data
    const additionalApiRequests = await this.getAdditionalApiRequests(teamId);
    const totalAdditionalRequests = additionalApiRequests.reduce(
      (sum, req) => sum + req.requestCount,
      0
    );
    const usedAdditionalRequests = additionalApiRequests.reduce(
      (sum, req) => sum + req.usedCount,
      0
    );
    const remainingAdditionalRequests = totalAdditionalRequests - usedAdditionalRequests;

    return {
      teamId,
      period: { start, end },
      metrics: {
        apiRequests: apiRequestsResult.length,
        searchRequests: searchRequestsResult.length,
        documentsStored: await this.getDocumentCount(teamId),
        storageUsedGB: await this.getStorageUsage(teamId),
        teamMembers: team.members.length,
        projects: team.projects.length,
        collections: team.collections.length,
      },
      additionalApiRequests: {
        total: totalAdditionalRequests,
        used: usedAdditionalRequests,
        remaining: remainingAdditionalRequests,
        packages: additionalApiRequests,
      },
    };
  }

  /**
   * Check if team has exceeded plan limits
   */
  async checkPlanLimits(teamId: string): Promise<{
    withinLimits: boolean;
    exceededLimits: string[];
    currentUsage: IUsageData;
    planLimits: ISubscriptionPlanConfig['limits'];
  }> {
    const subscription = await this.getActiveSubscription(teamId);
    const planId = (subscription?.planId as TSubscriptionPlan) || 'free';
    const planConfig = getPlanConfig(planId);
    const usage = await this.getUsageData(teamId);

    const exceededLimits: string[] = [];

    // Check each limit
    if (
      planConfig.limits.maxTeamMembers !== -1 &&
      usage.metrics.teamMembers > planConfig.limits.maxTeamMembers
    ) {
      exceededLimits.push('team_members');
    }
    if (
      planConfig.limits.maxProjects !== -1 &&
      usage.metrics.projects > planConfig.limits.maxProjects
    ) {
      exceededLimits.push('projects');
    }
    if (
      planConfig.limits.maxCollections !== -1 &&
      usage.metrics.collections > planConfig.limits.maxCollections
    ) {
      exceededLimits.push('collections');
    }

    // Check API requests with additional requests consideration
    if (planConfig.limits.maxApiRequests !== -1) {
      const totalAvailableRequests =
        planConfig.limits.maxApiRequests + (usage.additionalApiRequests?.remaining || 0);

      if (usage.metrics.apiRequests > totalAvailableRequests) {
        exceededLimits.push('api_requests');
      }
    }

    if (
      planConfig.limits.maxSearchRequests !== -1 &&
      usage.metrics.searchRequests > planConfig.limits.maxSearchRequests
    ) {
      exceededLimits.push('search_requests');
    }
    if (
      planConfig.limits.maxDocuments !== -1 &&
      usage.metrics.documentsStored > planConfig.limits.maxDocuments
    ) {
      exceededLimits.push('documents');
    }

    return {
      withinLimits: exceededLimits.length === 0,
      exceededLimits,
      currentUsage: usage,
      planLimits: planConfig.limits,
    };
  }

  /**
   * Record API request usage for a team
   *
   * @param teamId - Team ID
   * @param endpoint - API endpoint accessed
   * @param userId - User ID making the request
   * @param ipAddress - IP address of the request
   * @param userAgent - User agent of the request
   * @returns Promise resolving when usage is recorded
   */
  async recordApiRequestUsage(
    teamId: string,
    endpoint: string,
    userId?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    const db = await getDB();

    // Create audit log for API request
    await db.insert(schema.auditLogs).values({
      userId,
      teamId,
      action: 'api.request',
      resource: 'api',
      resourceId: endpoint,
      details: JSON.stringify({
        endpoint,
        timestamp: new Date().toISOString(),
      }),
      ipAddress,
      userAgent,
    });

    // Get current usage and plan information
    const usage = await this.getUsageData(teamId);
    const subscription = await this.getActiveSubscription(teamId);
    const planId = (subscription?.planId as TSubscriptionPlan) || 'free';
    const planConfig = getPlanConfig(planId);

    // Check if this request exceeds the plan limit and we need to consume additional requests
    // We check against the current usage + 1 (this request) to see if we need additional requests
    const currentRequestCount = usage.metrics.apiRequests + 1;

    if (
      planConfig.limits.maxApiRequests !== -1 &&
      currentRequestCount > planConfig.limits.maxApiRequests
    ) {
      // Calculate how many additional requests we need for this request
      const additionalRequestsNeeded = currentRequestCount - planConfig.limits.maxApiRequests;

      // Only consume if we haven't already consumed for this usage level
      if (additionalRequestsNeeded === 1) {
        await this.consumeAdditionalApiRequest(teamId);
      }
    }
  }

  /**
   * Consume one additional API request from available packages
   *
   * @param teamId - Team ID
   * @returns Promise resolving when additional request is consumed
   */
  private async consumeAdditionalApiRequest(teamId: string): Promise<void> {
    const db = await getDB();

    // Find the oldest active package with remaining requests
    const availablePackage = await db.query.additionalApiRequests.findFirst({
      where: and(
        eq(schema.additionalApiRequests.teamId, teamId),
        eq(schema.additionalApiRequests.isActive, true),
        gte(schema.additionalApiRequests.expiresAt, new Date()),
        sql`${schema.additionalApiRequests.usedCount} < ${schema.additionalApiRequests.requestCount}`
      ),
      orderBy: [schema.additionalApiRequests.createdAt], // Use oldest first
    });

    if (availablePackage) {
      // Increment used count
      await db
        .update(schema.additionalApiRequests)
        .set({
          usedCount: availablePackage.usedCount + 1,
          updatedAt: new Date(),
        })
        .where(eq(schema.additionalApiRequests.id, availablePackage.id));
    }
  }

  /**
   * Get current additional API request balance for a team
   *
   * @param teamId - Team ID
   * @returns Promise resolving to balance information
   */
  async getAdditionalApiRequestBalance(teamId: string): Promise<{
    totalPurchased: number;
    totalUsed: number;
    totalRemaining: number;
    activePackages: number;
    expiringSoon: number; // Expiring within 7 days
  }> {
    const db = await getDB();

    const packages = await db.query.additionalApiRequests.findMany({
      where: and(
        eq(schema.additionalApiRequests.teamId, teamId),
        eq(schema.additionalApiRequests.isActive, true),
        gte(schema.additionalApiRequests.expiresAt, new Date())
      ),
    });

    const sevenDaysFromNow = new Date();
    sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

    const totalPurchased = packages.reduce((sum, pkg) => sum + pkg.requestCount, 0);
    const totalUsed = packages.reduce((sum, pkg) => sum + pkg.usedCount, 0);
    const totalRemaining = totalPurchased - totalUsed;
    const activePackages = packages.length;
    const expiringSoon = packages.filter((pkg) => pkg.expiresAt <= sevenDaysFromNow).length;

    return {
      totalPurchased,
      totalUsed,
      totalRemaining,
      activePackages,
      expiringSoon,
    };
  }

  /**
   * Purchase additional API requests
   *
   * @param params - Purchase parameters
   * @returns Promise resolving to transaction and additional requests data
   */
  async purchaseAdditionalApiRequests(params: IPurchaseApiRequestsParams): Promise<{
    transaction: TBillingTransaction;
    additionalRequests: TAdditionalApiRequest;
  }> {
    const db = await getDB();

    // Calculate amount: $5 per 1,000 requests
    const amount = params.requestCount * 500; // $5.00 in cents per 1,000 requests
    const actualRequestCount = params.requestCount * 1000; // Convert to actual request count

    // Create transaction record
    const transactionData: TNewBillingTransaction = {
      teamId: params.teamId,
      provider: params.provider,
      transactionId: `api_req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      amount,
      currency: 'USD',
      status: 'completed', // In production, this would be 'pending' until payment is confirmed
      type: 'api_requests',
    };

    const [transaction] = await db
      .insert(schema.billingTransactions)
      .values(transactionData)
      .returning();

    // Create additional API requests record with 30-day expiration
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 30);

    const additionalRequestsData: TNewAdditionalApiRequest = {
      teamId: params.teamId,
      transactionId: transaction.id,
      requestCount: actualRequestCount,
      usedCount: 0,
      expiresAt,
      isActive: true,
    };

    const [additionalRequests] = await db
      .insert(schema.additionalApiRequests)
      .values(additionalRequestsData)
      .returning();

    return {
      transaction,
      additionalRequests,
    };
  }

  /**
   * Get additional API requests for a team
   *
   * @param teamId - Team ID
   * @returns Promise resolving to additional API requests data
   */
  async getAdditionalApiRequests(teamId: string): Promise<IAdditionalApiRequestsData[]> {
    const db = await getDB();

    const additionalRequests = await db.query.additionalApiRequests.findMany({
      where: and(
        eq(schema.additionalApiRequests.teamId, teamId),
        eq(schema.additionalApiRequests.isActive, true),
        gte(schema.additionalApiRequests.expiresAt, new Date())
      ),
      orderBy: [desc(schema.additionalApiRequests.createdAt)],
    });

    return additionalRequests.map((req) => ({
      id: req.id,
      teamId: req.teamId,
      requestCount: req.requestCount,
      usedCount: req.usedCount,
      remainingCount: req.requestCount - req.usedCount,
      expiresAt: req.expiresAt,
      isActive: req.isActive,
      createdAt: req.createdAt,
    }));
  }

  /**
   * Get purchase history for additional API requests
   *
   * @param teamId - Team ID
   * @param limit - Maximum number of records to return (default: 50)
   * @param offset - Number of records to skip (default: 0)
   * @returns Promise resolving to purchase history data
   */
  async getAdditionalApiRequestsPurchaseHistory(
    teamId: string,
    limit = 50,
    offset = 0
  ): Promise<{
    purchases: Array<{
      id: string;
      transactionId: string;
      requestCount: number;
      usedCount: number;
      remainingCount: number;
      amount: number;
      currency: string;
      status: string;
      provider: string;
      expiresAt: Date;
      isActive: boolean;
      createdAt: Date;
    }>;
    total: number;
    hasMore: boolean;
  }> {
    const db = await getDB();

    // Get purchase history with transaction details
    const purchases = await db.query.additionalApiRequests.findMany({
      where: eq(schema.additionalApiRequests.teamId, teamId),
      with: {
        transaction: true,
      },
      orderBy: [desc(schema.additionalApiRequests.createdAt)],
      limit: limit + 1, // Get one extra to check if there are more
      offset,
    });

    // Get total count for pagination
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(schema.additionalApiRequests)
      .where(eq(schema.additionalApiRequests.teamId, teamId));

    const total = totalResult[0]?.count || 0;
    const hasMore = purchases.length > limit;
    const actualPurchases = hasMore ? purchases.slice(0, -1) : purchases;

    return {
      purchases: actualPurchases.map((purchase) => ({
        id: purchase.id,
        transactionId: purchase.transaction.id,
        requestCount: purchase.requestCount,
        usedCount: purchase.usedCount,
        remainingCount: purchase.requestCount - purchase.usedCount,
        amount: purchase.transaction.amount,
        currency: purchase.transaction.currency,
        status: purchase.transaction.status,
        provider: purchase.transaction.provider,
        expiresAt: purchase.expiresAt,
        isActive: purchase.isActive,
        createdAt: purchase.createdAt,
      })),
      total,
      hasMore,
    };
  }

  /**
   * Get all available subscription plans
   */
  getAvailablePlans(): ISubscriptionPlanConfig[] {
    return Object.values(SUBSCRIPTION_PLANS);
  }

  /**
   * Create a billing error
   */
  private createBillingError(
    type: IBillingError['type'],
    message: string,
    provider?: TBillingProvider
  ): IBillingError {
    const error = new Error(message) as IBillingError;
    error.type = type;
    error.provider = provider;
    return error;
  }

  /**
   * Get document count for a team
   */
  private async getDocumentCount(teamId: string): Promise<number> {
    try {
      const db = await getDB();

      // Count documents in collections owned by the team
      const result = await db
        .select({ count: sql<number>`count(*)` })
        .from(schema.documents)
        .innerJoin(schema.collections, eq(schema.documents.collectionId, schema.collections.id))
        .where(
          and(
            eq(schema.collections.teamId, teamId),
            isNull(schema.documents.deletedAt),
            isNull(schema.collections.deletedAt)
          )
        );

      return result[0]?.count || 0;
    } catch (error) {
      console.error('Error counting documents:', error);
      return 0;
    }
  }

  /**
   * Get storage usage for a team (in GB)
   */
  private async getStorageUsage(teamId: string): Promise<number> {
    try {
      const db = await getDB();

      // Sum file sizes from documents in team collections
      const result = await db
        .select({ totalSize: sql<number>`sum(${schema.documents.fileSize})` })
        .from(schema.documents)
        .innerJoin(schema.collections, eq(schema.documents.collectionId, schema.collections.id))
        .where(
          and(
            eq(schema.collections.teamId, teamId),
            isNull(schema.documents.deletedAt),
            isNull(schema.collections.deletedAt)
          )
        );

      const totalBytes = result[0]?.totalSize || 0;
      // Convert bytes to GB
      return Number((totalBytes / (1024 * 1024 * 1024)).toFixed(2));
    } catch (error) {
      console.error('Error calculating storage usage:', error);
      return 0;
    }
  }

  /**
   * Cancel subscription at period end
   */
  async cancelSubscriptionAtPeriodEnd(teamId: string): Promise<{
    success: boolean;
    subscription?: TBillingSubscription;
    error?: string;
  }> {
    try {
      const db = await getDB();

      // Get current subscription
      const subscription = await db.query.billingSubscriptions.findFirst({
        where: eq(schema.billingSubscriptions.teamId, teamId),
      });

      if (!subscription) {
        return {
          success: false,
          error: 'No active subscription found',
        };
      }

      if (subscription.status === 'canceled') {
        return {
          success: false,
          error: 'Subscription is already canceled',
        };
      }

      // Update subscription to cancel at period end
      const [updatedSubscription] = await db
        .update(schema.billingSubscriptions)
        .set({
          cancelAtPeriodEnd: true,
          updatedAt: new Date(),
        })
        .where(eq(schema.billingSubscriptions.id, subscription.id))
        .returning();

      return {
        success: true,
        subscription: updatedSubscription,
      };
    } catch (error) {
      console.error('Error canceling subscription at period end:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to cancel subscription',
      };
    }
  }

  /**
   * Reactivate subscription (remove cancel at period end)
   */
  async reactivateSubscription(teamId: string): Promise<{
    success: boolean;
    subscription?: TBillingSubscription;
    error?: string;
  }> {
    try {
      const db = await getDB();

      // Get current subscription
      const subscription = await db.query.billingSubscriptions.findFirst({
        where: eq(schema.billingSubscriptions.teamId, teamId),
      });

      if (!subscription) {
        return {
          success: false,
          error: 'No subscription found',
        };
      }

      if (subscription.status === 'canceled') {
        return {
          success: false,
          error: 'Cannot reactivate a canceled subscription',
        };
      }

      if (!subscription.cancelAtPeriodEnd) {
        return {
          success: false,
          error: 'Subscription is not scheduled for cancellation',
        };
      }

      // Update subscription to remove cancel at period end
      const [updatedSubscription] = await db
        .update(schema.billingSubscriptions)
        .set({
          cancelAtPeriodEnd: false,
          updatedAt: new Date(),
        })
        .where(eq(schema.billingSubscriptions.id, subscription.id))
        .returning();

      return {
        success: true,
        subscription: updatedSubscription,
      };
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to reactivate subscription',
      };
    }
  }

  /**
   * Process subscriptions that should be canceled at period end
   */
  async processPendingCancellations(): Promise<{
    processed: number;
    canceled: number;
    errors: string[];
  }> {
    const results = {
      processed: 0,
      canceled: 0,
      errors: [] as string[],
    };

    try {
      const db = await getDB();
      const now = new Date();

      // Find subscriptions that should be canceled
      const subscriptionsToCancel = await db.query.billingSubscriptions.findMany({
        where: and(
          eq(schema.billingSubscriptions.cancelAtPeriodEnd, true),
          eq(schema.billingSubscriptions.status, 'active'),
          sql`${schema.billingSubscriptions.currentPeriodEnd} <= ${now}`
        ),
      });

      results.processed = subscriptionsToCancel.length;

      for (const subscription of subscriptionsToCancel) {
        try {
          // Update subscription status to canceled
          await db
            .update(schema.billingSubscriptions)
            .set({
              status: 'canceled',
              cancelAtPeriodEnd: false, // Reset the flag
              updatedAt: new Date(),
            })
            .where(eq(schema.billingSubscriptions.id, subscription.id));

          results.canceled++;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          results.errors.push(`Failed to cancel subscription ${subscription.id}: ${errorMessage}`);
          console.error(`Failed to cancel subscription ${subscription.id}:`, error);
        }
      }

      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Database query error';
      results.errors.push(`Failed to process pending cancellations: ${errorMessage}`);
      console.error('Failed to process pending cancellations:', error);
      return results;
    }
  }

  /**
   * Get payment methods for a team
   */
  async getPaymentMethods(teamId: string): Promise<
    Array<{
      id: string;
      type: TPaymentMethod;
      last4?: string;
      expiryMonth?: number;
      expiryYear?: number;
      isDefault: boolean;
    }>
  > {
    try {
      const db = await getDB();

      const paymentMethods = await db.query.paymentMethods.findMany({
        where: and(
          eq(schema.paymentMethods.teamId, teamId),
          eq(schema.paymentMethods.isActive, true)
        ),
        orderBy: [desc(schema.paymentMethods.isDefault), desc(schema.paymentMethods.createdAt)],
      });

      return paymentMethods.map((pm) => ({
        id: pm.id,
        type: pm.type as TPaymentMethod,
        last4: pm.cardLast4 || undefined,
        expiryMonth: pm.cardExpiryMonth || undefined,
        expiryYear: pm.cardExpiryYear || undefined,
        isDefault: pm.isDefault,
      }));
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      return [];
    }
  }

  /**
   * Add a payment method for a team
   */
  async addPaymentMethod(
    teamId: string,
    paymentMethodData: {
      provider: 'paddle' | 'paypal';
      providerPaymentMethodId: string;
      type: TPaymentMethod;
      cardLast4?: string;
      cardBrand?: string;
      cardExpiryMonth?: number;
      cardExpiryYear?: number;
      paypalEmail?: string;
      isDefault?: boolean;
    }
  ): Promise<TPaymentMethodDB> {
    const db = await getDB();

    // If this is being set as default, unset other default payment methods
    if (paymentMethodData.isDefault) {
      await db
        .update(schema.paymentMethods)
        .set({ isDefault: false, updatedAt: new Date() })
        .where(
          and(eq(schema.paymentMethods.teamId, teamId), eq(schema.paymentMethods.isDefault, true))
        );
    }

    const newPaymentMethod: TNewPaymentMethod = {
      teamId,
      provider: paymentMethodData.provider,
      providerPaymentMethodId: paymentMethodData.providerPaymentMethodId,
      type: paymentMethodData.type,
      cardLast4: paymentMethodData.cardLast4 || undefined,
      cardBrand: paymentMethodData.cardBrand || undefined,
      cardExpiryMonth: paymentMethodData.cardExpiryMonth || undefined,
      cardExpiryYear: paymentMethodData.cardExpiryYear || undefined,
      paypalEmail: paymentMethodData.paypalEmail || undefined,
      isDefault: paymentMethodData.isDefault || false,
      isActive: true,
    };

    const [paymentMethod] = await db
      .insert(schema.paymentMethods)
      .values(newPaymentMethod)
      .returning();

    return paymentMethod;
  }

  /**
   * Update a payment method
   */
  async updatePaymentMethod(
    paymentMethodId: string,
    teamId: string,
    updates: {
      isDefault?: boolean;
      isActive?: boolean;
    }
  ): Promise<TPaymentMethodDB | null> {
    const db = await getDB();

    // If setting as default, unset other default payment methods
    if (updates.isDefault) {
      await db
        .update(schema.paymentMethods)
        .set({ isDefault: false, updatedAt: new Date() })
        .where(
          and(eq(schema.paymentMethods.teamId, teamId), eq(schema.paymentMethods.isDefault, true))
        );
    }

    const [updatedPaymentMethod] = await db
      .update(schema.paymentMethods)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(
        and(eq(schema.paymentMethods.id, paymentMethodId), eq(schema.paymentMethods.teamId, teamId))
      )
      .returning();

    return updatedPaymentMethod || null;
  }

  /**
   * Delete a payment method
   */
  async deletePaymentMethod(paymentMethodId: string, teamId: string): Promise<boolean> {
    const db = await getDB();

    // Soft delete by setting isActive to false
    const [deletedPaymentMethod] = await db
      .update(schema.paymentMethods)
      .set({
        isActive: false,
        isDefault: false,
        updatedAt: new Date(),
      })
      .where(
        and(eq(schema.paymentMethods.id, paymentMethodId), eq(schema.paymentMethods.teamId, teamId))
      )
      .returning();

    return !!deletedPaymentMethod;
  }

  /**
   * Get default payment method for a team
   */
  async getDefaultPaymentMethod(teamId: string): Promise<TPaymentMethodDB | null> {
    try {
      const db = await getDB();

      const defaultPaymentMethod = await db.query.paymentMethods.findFirst({
        where: and(
          eq(schema.paymentMethods.teamId, teamId),
          eq(schema.paymentMethods.isDefault, true),
          eq(schema.paymentMethods.isActive, true)
        ),
      });

      return defaultPaymentMethod || null;
    } catch (error) {
      console.error('Error fetching default payment method:', error);
      return null;
    }
  }
}

// ============================================================================
// Service Instance
// ============================================================================

// Lazy singleton instance
let billingServiceInstance: BillingService | null = null;

/**
 * Get or create billing service instance (lazy initialization)
 */
export const getBillingService = (): BillingService => {
  if (!billingServiceInstance) {
    billingServiceInstance = new BillingService();
  }
  return billingServiceInstance;
};

// Create a proxy object that behaves like the service instance
export const billingService = new Proxy({} as BillingService, {
  get(_target, prop) {
    const service = getBillingService();
    const value = service[prop as keyof BillingService];
    if (typeof value === 'function') {
      return value.bind(service);
    }
    return value;
  },
});
