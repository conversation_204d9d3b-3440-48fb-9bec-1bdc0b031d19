export const openApiSpec = {
  openapi: '3.0.0',
  info: {
    title: 'EZContext Multi-Tenant SaaS API',
    version: '3.0.0',
    description:
      'Comprehensive multi-tenant SaaS backend API with team management, RBAC, billing integration (Paddle/PayPal), and semantic search capabilities. Features subscription management, usage tracking, plan enforcement, and comprehensive vector database operations.',
    contact: {
      name: 'API Support',
      email: '<EMAIL>',
    },
  },
  servers: [
    {
      url: 'https://your-worker.your-subdomain.workers.dev',
      description: 'Production server',
    },
    {
      url: 'http://localhost:8787',
      description: 'Development server',
    },
  ],
  paths: {
    '/api/search': {
      post: {
        summary: 'Search collection documentation',
        description:
          'Perform semantic search across collection documentation using vector similarity',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  query: {
                    type: 'string',
                    description: 'Search query text',
                    minLength: 1,
                    example: 'how to use React hooks',
                  },
                  collection: {
                    type: 'string',
                    description: 'Specific collection to search within (optional)',
                    example: 'react',
                  },
                  limit: {
                    type: 'number',
                    description: 'Maximum number of results to return',
                    minimum: 1,
                    maximum: 20,
                    default: 5,
                    example: 10,
                  },
                },
                required: ['query'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Search results',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: {
                      type: 'string',
                      enum: ['success'],
                      description: 'Response status',
                    },
                    data: {
                      type: 'array',
                      description: 'Search results array',
                      items: {
                        type: 'object',
                        properties: {
                          id: { type: 'string', description: 'Unique result identifier' },
                          content: { type: 'string', description: 'Documentation content' },
                          score: { type: 'number', description: 'Relevance score (0-1)' },
                        },
                        required: ['id', 'content', 'score'],
                      },
                    },
                    metadata: {
                      type: 'object',
                      description: 'Search metadata and statistics',
                      properties: {
                        query: { type: 'string', description: 'Original search query' },
                        total_results: {
                          type: 'number',
                          description: 'Total number of results found',
                        },
                        search_time_ms: {
                          type: 'number',
                          description: 'Search execution time in milliseconds',
                        },
                      },
                      required: ['query', 'total_results', 'search_time_ms'],
                    },
                    timestamp: {
                      type: 'string',
                      format: 'date-time',
                      description: 'Response timestamp',
                    },
                  },
                  required: ['status', 'data', 'metadata', 'timestamp'],
                },
              },
            },
          },
          '400': {
            description: 'Invalid request',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: { type: 'string' },
                    details: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          path: { type: 'array', items: { type: 'string' } },
                          message: { type: 'string' },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/search/collections': {
      get: {
        summary: 'List available collections',
        description: 'Get a list of all available collections in the documentation database',
        responses: {
          '200': {
            description: 'List of collections',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    collections: {
                      type: 'array',
                      items: { type: 'string' },
                      description: 'Array of collection names',
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/search/collection-info': {
      post: {
        summary: 'Get collection information',
        description: 'Get detailed information about a specific collection',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  collection_name: {
                    type: 'string',
                    description: 'Name of the collection to get information for',
                    minLength: 1,
                    example: 'react',
                  },
                  include_versions: {
                    type: 'boolean',
                    description: 'Whether to include version information',
                    default: false,
                  },
                },
                required: ['collection_name'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Collection information',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    name: { type: 'string', description: 'Collection name' },
                    description: { type: 'string', description: 'Collection description' },
                    documentation_url: { type: 'string', description: 'Documentation URL' },
                    repository_url: { type: 'string', description: 'Repository URL' },
                    latest_version: { type: 'string', description: 'Latest version' },
                    tags: {
                      type: 'array',
                      items: { type: 'string' },
                      description: 'Collection tags',
                    },
                    last_updated: { type: 'string', description: 'Last update timestamp' },
                    versions: {
                      type: 'array',
                      items: { type: 'string' },
                      description: 'Available versions (if include_versions is true)',
                    },
                  },
                },
              },
            },
          },
          '400': {
            description: 'Invalid request',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: { type: 'string' },
                    details: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          path: { type: 'array', items: { type: 'string' } },
                          message: { type: 'string' },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    error: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/health': {
      get: {
        summary: 'Health check',
        description: 'Check the health status of the API and its dependencies',
        responses: {
          '200': {
            description: 'Service is healthy',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', enum: ['healthy'] },
                    timestamp: { type: 'string', format: 'date-time' },
                    services: {
                      type: 'object',
                      properties: {
                        vectorize: { type: 'string' },
                        kv: { type: 'string' },
                        ai: { type: 'string' },
                      },
                    },
                  },
                },
              },
            },
          },
          '500': {
            description: 'Service is unhealthy',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', enum: ['unhealthy'] },
                    error: { type: 'string' },
                    timestamp: { type: 'string', format: 'date-time' },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/upload': {
      post: {
        summary: 'Upload and process files',
        description: 'Upload files for processing and vector storage with automatic code detection',
        requestBody: {
          required: true,
          content: {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  file: {
                    type: 'string',
                    format: 'binary',
                    description: 'File to upload (max 10MB)',
                  },
                  category: {
                    type: 'string',
                    enum: ['docs', 'code'],
                    description: 'Content category (auto-detected if not provided)',
                  },
                  collection: {
                    type: 'string',
                    description: 'Collection name for categorization',
                  },
                  embedding_provider: {
                    type: 'string',
                    enum: ['openai', 'voyageai', 'cloudflare'],
                    description: 'Embedding provider to use',
                  },
                  embedding_model: {
                    type: 'string',
                    description: 'Specific embedding model to use',
                  },
                },
                required: ['file'],
              },
            },
          },
        },
        responses: {
          '201': {
            description: 'File processed successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/UploadResponse' },
              },
            },
          },
          '400': {
            description: 'Invalid file or parameters',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
              },
            },
          },
          '409': {
            description: 'Duplicate content detected',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/DuplicateResponse' },
              },
            },
          },
        },
      },
    },
    '/api/upload/submit-text': {
      post: {
        summary: 'Submit text content',
        description: 'Submit raw text content for processing and vector storage',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/TextSubmissionRequest' },
            },
          },
        },
        responses: {
          '201': {
            description: 'Text processed successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/UploadResponse' },
              },
            },
          },
          '400': {
            description: 'Invalid request data',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
              },
            },
          },
          '409': {
            description: 'Duplicate content detected',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/DuplicateResponse' },
              },
            },
          },
        },
      },
    },
    '/api/upload/supported-types': {
      get: {
        summary: 'Get supported file types',
        description: 'Get list of supported file extensions and upload configuration',
        responses: {
          '200': {
            description: 'Supported file types and configuration',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', enum: ['success'] },
                    data: {
                      type: 'object',
                      properties: {
                        supported_extensions: {
                          type: 'array',
                          items: { type: 'string' },
                          description: 'List of supported file extensions',
                        },
                        max_file_size_mb: {
                          type: 'number',
                          description: 'Maximum file size in MB',
                        },
                        categories: {
                          type: 'array',
                          items: { type: 'string' },
                          description: 'Available content categories',
                        },
                        embedding_providers: {
                          type: 'array',
                          items: { type: 'string' },
                          description: 'Available embedding providers',
                        },
                      },
                    },
                    timestamp: { type: 'string', format: 'date-time' },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/vectors': {
      get: {
        summary: 'List vectors with pagination',
        description: 'Get paginated list of vectors with optional filtering',
        parameters: [
          {
            name: 'page',
            in: 'query',
            schema: { type: 'integer', minimum: 1, default: 1 },
            description: 'Page number',
          },
          {
            name: 'limit',
            in: 'query',
            schema: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
            description: 'Number of results per page',
          },
          {
            name: 'collection',
            in: 'query',
            schema: { type: 'string' },
            description: 'Filter by collection name',
          },
          {
            name: 'category',
            in: 'query',
            schema: { type: 'string', enum: ['docs', 'code'] },
            description: 'Filter by content category',
          },
        ],
        responses: {
          '200': {
            description: 'List of vectors with pagination',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/VectorListResponse' },
              },
            },
          },
        },
      },
    },
    '/api/vectors/{id}': {
      get: {
        summary: 'Get vector by ID',
        description: 'Retrieve a specific vector by its ID',
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string' },
            description: 'Vector ID',
          },
        ],
        responses: {
          '200': {
            description: 'Vector details',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/VectorResponse' },
              },
            },
          },
          '404': {
            description: 'Vector not found',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
              },
            },
          },
        },
      },
      put: {
        summary: 'Update vector metadata',
        description: 'Update metadata for a specific vector',
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string' },
            description: 'Vector ID',
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/VectorUpdateRequest' },
            },
          },
        },
        responses: {
          '200': {
            description: 'Vector updated successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/VectorResponse' },
              },
            },
          },
          '404': {
            description: 'Vector not found',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
              },
            },
          },
        },
      },
      delete: {
        summary: 'Delete vector',
        description: 'Delete a specific vector by its ID',
        parameters: [
          {
            name: 'id',
            in: 'path',
            required: true,
            schema: { type: 'string' },
            description: 'Vector ID',
          },
        ],
        responses: {
          '200': {
            description: 'Vector deleted successfully',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/VectorResponse' },
              },
            },
          },
          '404': {
            description: 'Vector not found',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
              },
            },
          },
        },
      },
    },
    '/api/vectors/bulk-delete': {
      post: {
        summary: 'Bulk delete vectors',
        description: 'Delete multiple vectors based on filter criteria',
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/BulkDeleteRequest' },
            },
          },
        },
        responses: {
          '200': {
            description: 'Bulk delete completed',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/BulkDeleteResponse' },
              },
            },
          },
        },
      },
    },
    '/mcp/websocket': {
      get: {
        summary: 'MCP WebSocket endpoint',
        description: 'WebSocket endpoint for Model Context Protocol (MCP) connections',
        parameters: [
          {
            name: 'upgrade',
            in: 'header',
            required: true,
            schema: { type: 'string', enum: ['websocket'] },
            description: 'WebSocket upgrade header',
          },
        ],
        responses: {
          '101': {
            description: 'WebSocket connection established',
          },
          '400': {
            description: 'Bad request - not a WebSocket upgrade',
          },
          '429': {
            description: 'Too many connections',
          },
          '500': {
            description: 'Failed to establish MCP connection',
          },
        },
      },
    },
    '/mcp/status': {
      get: {
        summary: 'MCP server status',
        description: 'Get the current status of the MCP server',
        responses: {
          '200': {
            description: 'MCP server status',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    active_connections: {
                      type: 'number',
                      description: 'Number of active WebSocket connections',
                    },
                    max_connections: { type: 'number', description: 'Maximum allowed connections' },
                    server_status: { type: 'string', enum: ['running'] },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/billing/plans': {
      get: {
        summary: 'Get subscription plans',
        description: 'Retrieve all available subscription plans with pricing and features',
        tags: ['Billing'],
        responses: {
          '200': {
            description: 'Subscription plans retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', enum: ['success'] },
                    data: {
                      type: 'object',
                      properties: {
                        plans: {
                          type: 'array',
                          items: { $ref: '#/components/schemas/SubscriptionPlan' },
                        },
                      },
                    },
                    timestamp: { type: 'string', format: 'date-time' },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/billing/teams/{teamId}/dashboard': {
      get: {
        summary: 'Get billing dashboard',
        description: 'Retrieve comprehensive billing dashboard data for a team',
        tags: ['Billing'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'teamId',
            in: 'path',
            required: true,
            schema: { type: 'string' },
            description: 'Team ID',
          },
        ],
        responses: {
          '200': {
            description: 'Billing dashboard data retrieved successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', enum: ['success'] },
                    data: {
                      type: 'object',
                      properties: {
                        dashboard: { $ref: '#/components/schemas/BillingDashboard' },
                      },
                    },
                    timestamp: { type: 'string', format: 'date-time' },
                  },
                },
              },
            },
          },
          '402': {
            description: 'Payment required or plan limit exceeded',
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/Error' },
              },
            },
          },
        },
      },
    },
    '/api/billing/teams/{teamId}/subscriptions': {
      post: {
        summary: 'Create subscription',
        description: 'Create a new subscription for a team with Paddle or PayPal',
        tags: ['Billing'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'teamId',
            in: 'path',
            required: true,
            schema: { type: 'string' },
            description: 'Team ID',
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  planId: {
                    type: 'string',
                    enum: ['starter', 'professional', 'enterprise'],
                    description: 'Subscription plan ID',
                  },
                  interval: {
                    type: 'string',
                    enum: ['monthly', 'yearly'],
                    description: 'Billing interval',
                  },
                  provider: {
                    type: 'string',
                    enum: ['paddle', 'paypal'],
                    description: 'Payment provider',
                  },
                  paymentMethodId: {
                    type: 'string',
                    description: 'Payment method ID (optional)',
                  },
                  couponCode: {
                    type: 'string',
                    description: 'Coupon code (optional)',
                  },
                },
                required: ['planId', 'interval', 'provider'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Subscription created successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', enum: ['success'] },
                    data: {
                      type: 'object',
                      properties: {
                        subscription: { $ref: '#/components/schemas/Subscription' },
                        checkoutUrl: {
                          type: 'string',
                          description: 'URL to complete payment',
                        },
                      },
                    },
                    timestamp: { type: 'string', format: 'date-time' },
                  },
                },
              },
            },
          },
        },
      },
    },
    '/api/billing/teams/{teamId}/payments': {
      post: {
        summary: 'Create payment',
        description: 'Create a one-time payment with PayPal',
        tags: ['Billing'],
        security: [{ bearerAuth: [] }],
        parameters: [
          {
            name: 'teamId',
            in: 'path',
            required: true,
            schema: { type: 'string' },
            description: 'Team ID',
          },
        ],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  amount: {
                    type: 'number',
                    minimum: 50,
                    maximum: 1000000,
                    description: 'Payment amount in cents',
                  },
                  currency: {
                    type: 'string',
                    enum: ['USD', 'EUR', 'GBP'],
                    description: 'Payment currency',
                  },
                  description: {
                    type: 'string',
                    minLength: 1,
                    maxLength: 255,
                    description: 'Payment description',
                  },
                  provider: {
                    type: 'string',
                    enum: ['paypal'],
                    description: 'Payment provider',
                  },
                },
                required: ['amount', 'currency', 'description', 'provider'],
              },
            },
          },
        },
        responses: {
          '200': {
            description: 'Payment created successfully',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', enum: ['success'] },
                    data: {
                      type: 'object',
                      properties: {
                        payment: { type: 'object' },
                        approvalUrl: {
                          type: 'string',
                          description: 'URL to approve payment',
                        },
                      },
                    },
                    timestamp: { type: 'string', format: 'date-time' },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  components: {
    schemas: {
      Error: {
        type: 'object',
        properties: {
          error: { type: 'string' },
        },
      },
      VectorDatabaseConfig: {
        type: 'object',
        description: 'Vector database configuration details',
        properties: {
          provider: {
            type: 'string',
            enum: ['cloudflare-vectorize'],
            description: 'Vector database provider',
          },
          dimensions: {
            type: 'integer',
            example: 1024,
            description: 'Embedding vector dimensions',
          },
          distance_metric: {
            type: 'string',
            enum: ['cosine'],
            description: 'Distance metric for similarity search',
          },
          embedding_model: {
            type: 'string',
            example: 'text-embedding-3-small',
            description: 'OpenAI embedding model used',
          },
          embedding_provider: {
            type: 'string',
            enum: ['openai', 'voyageai', 'cloudflare'],
            description: 'Embedding generation provider',
          },
        },
      },
      ErrorResponse: {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['error'] },
          error: { type: 'string', description: 'Error message' },
          error_type: {
            type: 'string',
            enum: ['validation', 'processing', 'database', 'not_found', 'unknown'],
            description: 'Error category',
          },
          details: {
            type: 'object',
            description: 'Additional error details',
            additionalProperties: true,
          },
          timestamp: { type: 'string', format: 'date-time' },
        },
        required: ['status', 'error', 'timestamp'],
      },
      TextSubmissionRequest: {
        type: 'object',
        properties: {
          text: {
            type: 'string',
            minLength: 1,
            description: 'Text content to process',
          },
          title: {
            type: 'string',
            minLength: 1,
            description: 'Title for the content',
          },
          category: {
            type: 'string',
            enum: ['docs', 'code'],
            default: 'docs',
            description: 'Content category',
          },
          library: {
            type: 'string',
            description: 'Library name for categorization',
          },
          embedding_provider: {
            type: 'string',
            enum: ['openai', 'voyageai', 'cloudflare'],
            description: 'Embedding provider to use',
          },
          embedding_model: {
            type: 'string',
            description: 'Specific embedding model to use',
          },
        },
        required: ['text', 'title'],
      },
      UploadResponse: {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['success'] },
          message: { type: 'string', description: 'Success message' },
          data: {
            type: 'object',
            properties: {
              file_name: { type: 'string', description: 'Name of processed file' },
              file_size: { type: 'number', description: 'File size in bytes' },
              content_hash: { type: 'string', description: 'SHA-256 hash of content' },
              category: { type: 'string', enum: ['docs', 'code'] },
              chunks_created: { type: 'number', description: 'Number of chunks created' },
              embedding_info: {
                type: 'object',
                properties: {
                  provider: { type: 'string' },
                  model: { type: 'string' },
                  dimensions: { type: 'number' },
                },
              },
              processing_time_ms: {
                type: 'number',
                description: 'Processing time in milliseconds',
              },
              upload_timestamp: { type: 'string', format: 'date-time' },
            },
          },
          timestamp: { type: 'string', format: 'date-time' },
        },
        required: ['status', 'data', 'timestamp'],
      },
      DuplicateResponse: {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['duplicate'] },
          message: { type: 'string', description: 'Duplicate content message' },
          existing_content: {
            type: 'object',
            properties: {
              content_hash: { type: 'string' },
              upload_timestamp: { type: 'string', format: 'date-time' },
              chunks_count: { type: 'number' },
            },
          },
        },
        required: ['status', 'message', 'existing_content'],
      },
      VectorListResponse: {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['success'] },
          data: {
            type: 'object',
            properties: {
              vectors: {
                type: 'array',
                items: { $ref: '#/components/schemas/VectorItem' },
              },
              pagination: { $ref: '#/components/schemas/PaginationInfo' },
            },
          },
          timestamp: { type: 'string', format: 'date-time' },
        },
        required: ['status', 'data', 'timestamp'],
      },
      VectorResponse: {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['success', 'error'] },
          data: { $ref: '#/components/schemas/VectorItem' },
          error: { type: 'string' },
          error_type: { type: 'string' },
          timestamp: { type: 'string', format: 'date-time' },
        },
        required: ['status', 'timestamp'],
      },
      VectorItem: {
        type: 'object',
        properties: {
          id: { type: 'string', description: 'Vector ID' },
          metadata: {
            type: 'object',
            description: 'Vector metadata',
            additionalProperties: true,
          },
          score: { type: 'number', description: 'Similarity score (if from search)' },
        },
        required: ['id', 'metadata'],
      },
      PaginationInfo: {
        type: 'object',
        properties: {
          current_page: { type: 'number' },
          total_pages: { type: 'number' },
          total_count: { type: 'number' },
          has_next: { type: 'boolean' },
          has_previous: { type: 'boolean' },
          limit: { type: 'number' },
        },
        required: [
          'current_page',
          'total_pages',
          'total_count',
          'has_next',
          'has_previous',
          'limit',
        ],
      },
      VectorUpdateRequest: {
        type: 'object',
        properties: {
          metadata: {
            type: 'object',
            description: 'Metadata updates to apply',
            additionalProperties: true,
          },
        },
      },
      BulkDeleteRequest: {
        type: 'object',
        properties: {
          collection: { type: 'string', description: 'Filter by collection name' },
          category: { type: 'string', enum: ['docs', 'code'], description: 'Filter by category' },
          content_hash: { type: 'string', description: 'Filter by content hash' },
        },
      },
      BulkDeleteResponse: {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['success'] },
          message: { type: 'string', description: 'Operation result message' },
          data: {
            type: 'object',
            properties: {
              deleted_count: { type: 'number', description: 'Number of vectors deleted' },
              filters_applied: {
                type: 'object',
                description: 'Filters that were applied',
                additionalProperties: true,
              },
            },
          },
          timestamp: { type: 'string', format: 'date-time' },
        },
        required: ['status', 'data', 'timestamp'],
      },
      SubscriptionPlan: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            enum: ['free', 'starter', 'professional', 'enterprise'],
            description: 'Plan identifier',
          },
          name: { type: 'string', description: 'Plan display name' },
          description: { type: 'string', description: 'Plan description' },
          features: {
            type: 'array',
            items: { type: 'string' },
            description: 'List of plan features',
          },
          pricing: {
            type: 'object',
            properties: {
              monthly: {
                type: 'object',
                properties: {
                  amount: { type: 'number', description: 'Monthly price in cents' },
                  currency: { type: 'string', description: 'Currency code' },
                },
              },
              yearly: {
                type: 'object',
                properties: {
                  amount: { type: 'number', description: 'Yearly price in cents' },
                  currency: { type: 'string', description: 'Currency code' },
                  discount: { type: 'number', description: 'Discount percentage' },
                },
              },
            },
          },
          popular: { type: 'boolean', description: 'Whether this plan is marked as popular' },
          trialDays: { type: 'number', description: 'Number of trial days offered' },
        },
        required: ['id', 'name', 'description', 'features', 'pricing'],
      },
      Subscription: {
        type: 'object',
        properties: {
          id: { type: 'string', description: 'Subscription ID' },
          teamId: { type: 'string', description: 'Team ID' },
          provider: {
            type: 'string',
            enum: ['paddle', 'paypal'],
            description: 'Payment provider',
          },
          planId: {
            type: 'string',
            enum: ['starter', 'professional', 'enterprise'],
            description: 'Subscription plan ID',
          },
          status: {
            type: 'string',
            enum: ['active', 'canceled', 'past_due', 'unpaid', 'trialing', 'paused'],
            description: 'Subscription status',
          },
          currentPeriodStart: {
            type: 'string',
            format: 'date-time',
            description: 'Current billing period start',
          },
          currentPeriodEnd: {
            type: 'string',
            format: 'date-time',
            description: 'Current billing period end',
          },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
        },
        required: ['id', 'teamId', 'provider', 'planId', 'status'],
      },
      BillingDashboard: {
        type: 'object',
        properties: {
          subscription: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              planId: { type: 'string' },
              status: { type: 'string' },
              currentPeriodStart: { type: 'string', format: 'date-time' },
              currentPeriodEnd: { type: 'string', format: 'date-time' },
              cancelAtPeriodEnd: { type: 'boolean' },
              trialEnd: { type: 'string', format: 'date-time' },
            },
          },
          usage: {
            type: 'object',
            properties: {
              teamId: { type: 'string' },
              period: {
                type: 'object',
                properties: {
                  start: { type: 'string', format: 'date-time' },
                  end: { type: 'string', format: 'date-time' },
                },
              },
              metrics: {
                type: 'object',
                properties: {
                  apiRequests: { type: 'number' },
                  searchRequests: { type: 'number' },
                  vectorsStored: { type: 'number' },
                  storageUsedGB: { type: 'number' },
                  teamMembers: { type: 'number' },
                  projects: { type: 'number' },
                  collections: { type: 'number' },
                },
              },
            },
          },
          upcomingInvoice: {
            type: 'object',
            properties: {
              amount: { type: 'number' },
              currency: { type: 'string' },
              dueDate: { type: 'string', format: 'date-time' },
            },
          },
          paymentMethods: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                type: { type: 'string' },
                last4: { type: 'string' },
                expiryMonth: { type: 'number' },
                expiryYear: { type: 'number' },
                isDefault: { type: 'boolean' },
              },
            },
          },
          invoices: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                amount: { type: 'number' },
                currency: { type: 'string' },
                status: { type: 'string' },
                createdAt: { type: 'string', format: 'date-time' },
                paidAt: { type: 'string', format: 'date-time' },
                downloadUrl: { type: 'string' },
              },
            },
          },
        },
        required: ['usage'],
      },
    },
  },
};
