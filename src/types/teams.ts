/**
 * Team Management Types
 *
 * TypeScript interfaces and types for team management, RBAC, and permissions.
 * Provides type safety for team operations, member management, and access control.
 */

import type { TBillingSubscription, TBillingTransaction, TTeam, TTeamMember } from '@dbSchema';

// ============================================================================
// Team Role Types
// ============================================================================

/**
 * Available team roles with hierarchical permissions
 */
export type TTeamRole = 'owner' | 'admin' | 'manager' | 'member' | 'viewer';

/**
 * Team role hierarchy for permission checking
 */
export const TEAM_ROLE_HIERARCHY: Record<TTeamRole, number> = {
  owner: 5,
  admin: 4,
  manager: 3,
  member: 2,
  viewer: 1,
} as const;

// ============================================================================
// Permission Types
// ============================================================================

/**
 * Available permission categories
 */
export type TPermissionCategory =
  | 'team'
  | 'project'
  | 'billing'
  | 'vectors'
  | 'analytics'
  | 'api_keys';

/**
 * Available permission actions
 */
export type TPermissionAction = 'read' | 'manage' | 'create' | 'update' | 'delete';

/**
 * Complete permission string format: {action}.{category}
 */
export type TPermission =
  // Team permissions
  | 'read.team'
  | 'manage.team'
  | 'create.team'
  | 'update.team'
  | 'delete.team'
  // Project permissions
  | 'read.project'
  | 'manage.project'
  | 'create.project'
  | 'update.project'
  | 'delete.project'
  // Billing permissions
  | 'read.billing'
  | 'manage.billing'
  | 'create.billing'
  | 'update.billing'
  | 'delete.billing'
  // Vector permissions
  | 'read.vectors'
  | 'manage.vectors'
  | 'create.vectors'
  | 'update.vectors'
  | 'delete.vectors'
  // Analytics permissions
  | 'read.analytics'
  | 'manage.analytics'
  | 'create.analytics'
  | 'update.analytics'
  | 'delete.analytics'
  // API Key permissions
  | 'read.api_keys'
  | 'manage.api_keys'
  | 'create.api_keys'
  | 'update.api_keys'
  | 'delete.api_keys';

/**
 * Default permissions for each team role
 */
export const DEFAULT_ROLE_PERMISSIONS: Record<TTeamRole, TPermission[]> = {
  owner: [
    'read.team',
    'manage.team',
    'create.team',
    'update.team',
    'delete.team',
    'read.project',
    'manage.project',
    'create.project',
    'update.project',
    'delete.project',
    'read.billing',
    'manage.billing',
    'create.billing',
    'update.billing',
    'delete.billing',
    'read.vectors',
    'manage.vectors',
    'create.vectors',
    'update.vectors',
    'delete.vectors',
    'read.analytics',
    'manage.analytics',
    'create.analytics',
    'update.analytics',
    'delete.analytics',
    'read.api_keys',
    'manage.api_keys',
    'create.api_keys',
    'update.api_keys',
    'delete.api_keys',
  ],
  admin: [
    'read.team',
    'manage.team',
    'update.team',
    'read.project',
    'manage.project',
    'create.project',
    'update.project',
    'delete.project',
    'read.billing',
    'read.vectors',
    'manage.vectors',
    'create.vectors',
    'update.vectors',
    'delete.vectors',
    'read.analytics',
    'manage.analytics',
    'read.api_keys',
    'manage.api_keys',
    'create.api_keys',
    'update.api_keys',
    'delete.api_keys',
  ],
  manager: [
    'read.team',
    'read.project',
    'manage.project',
    'create.project',
    'update.project',
    'delete.project',
    'read.vectors',
    'manage.vectors',
    'create.vectors',
    'update.vectors',
    'delete.vectors',
    'read.analytics',
    'read.api_keys',
    'create.api_keys',
    'update.api_keys',
  ],
  member: [
    'read.team',
    'read.project',
    'manage.project',
    'update.project',
    'read.vectors',
    'manage.vectors',
    'create.vectors',
    'update.vectors',
    'read.api_keys',
  ],
  viewer: ['read.team', 'read.project', 'read.vectors'],
} as const;

// ============================================================================
// Team Plan Types
// ============================================================================

/**
 * Available team plan types
 */
export type TTeamPlan = 'free' | 'pro' | 'enterprise';

/**
 * Team plan limits and features
 */
export interface ITeamPlanLimits {
  maxMembers: number;
  maxProjects: number;
  maxVectorStorage: number; // in MB
  maxApiCalls: number; // per month
  features: string[];
}

/**
 * Plan configuration
 */
export const TEAM_PLAN_LIMITS: Record<TTeamPlan, ITeamPlanLimits> = {
  free: {
    maxMembers: 3,
    maxProjects: 2,
    maxVectorStorage: 100, // 100MB
    maxApiCalls: 1000,
    features: ['Basic vector search', 'Email support'],
  },
  pro: {
    maxMembers: 10,
    maxProjects: 10,
    maxVectorStorage: 1000, // 1GB
    maxApiCalls: 10000,
    features: ['Advanced vector search', 'Priority support', 'Analytics dashboard'],
  },
  enterprise: {
    maxMembers: -1, // unlimited
    maxProjects: -1, // unlimited
    maxVectorStorage: -1, // unlimited
    maxApiCalls: -1, // unlimited
    features: ['All features', 'Dedicated support', 'Custom integrations', 'SLA'],
  },
} as const;

// ============================================================================
// Enhanced Team Types
// ============================================================================

/**
 * Team with computed fields and relations
 */
export interface ITeamWithDetails extends TTeam {
  memberCount: number;
  projectCount: number;
  owner: {
    id: string;
    name: string;
    email: string;
  };
  currentSubscription?: TBillingSubscription;
  planLimits: ITeamPlanLimits;
}

/**
 * Team member with user details and computed permissions
 */
export interface ITeamMemberWithDetails extends TTeamMember {
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  effectivePermissions: TPermission[];
  invitedByUser?: {
    id: string;
    name: string;
    email: string;
  };
}

/**
 * Team invitation data
 */
export interface ITeamInvitation {
  email: string;
  role: TTeamRole;
  permissions?: TPermission[];
  message?: string;
}

// ============================================================================
// Billing Types
// ============================================================================

/**
 * Billing provider types
 */
export type TBillingProvider = 'paddle' | 'paypal';

/**
 * Subscription status types
 */
export type TSubscriptionStatus = 'active' | 'canceled' | 'past_due' | 'unpaid';

/**
 * Transaction status types
 */
export type TTransactionStatus = 'completed' | 'pending' | 'failed' | 'refunded';

/**
 * Enhanced billing subscription with team details
 */
export interface IBillingSubscriptionWithDetails extends TBillingSubscription {
  team: {
    id: string;
    name: string;
    slug: string;
  };
}

/**
 * Enhanced billing transaction with team details
 */
export interface IBillingTransactionWithDetails extends TBillingTransaction {
  team: {
    id: string;
    name: string;
    slug: string;
  };
}

// ============================================================================
// API Request/Response Types
// ============================================================================

/**
 * Create team request
 */
export interface ICreateTeamRequest {
  name: string;
  slug: string;
  description?: string;
  planType?: TTeamPlan;
}

/**
 * Update team request
 */
export interface IUpdateTeamRequest {
  name?: string;
  slug?: string;
  description?: string;
}

/**
 * Add team member request
 */
export interface IAddTeamMemberRequest {
  userId?: string; // For existing users
  email?: string; // For invitations
  role: TTeamRole;
  permissions?: TPermission[];
}

/**
 * Update team member request
 */
export interface IUpdateTeamMemberRequest {
  role?: TTeamRole;
  permissions?: TPermission[];
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Team context for multi-tenant operations
 */
export interface ITeamContext {
  teamId: string;
  userId: string;
  role: TTeamRole;
  permissions: TPermission[];
}

/**
 * Permission check result
 */
export interface IPermissionCheckResult {
  allowed: boolean;
  reason?: string;
  requiredPermission?: TPermission;
}
