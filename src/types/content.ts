/**
 * Content Processing Types
 *
 * Type definitions for text chunking, content detection, and processing.
 */

// Content types supported by the system
export type TContentType =
  | 'text'
  | 'markdown'
  | 'json'
  | 'documentation'
  | 'code'
  | 'pdf'
  | 'doc'
  | 'docx'
  | 'python'
  | 'javascript'
  | 'typescript'
  | 'java'
  | 'cpp'
  | 'csharp'
  | 'php'
  | 'ruby'
  | 'go'
  | 'rust'
  | 'swift'
  | 'kotlin'
  | 'scala'
  | 'r'
  | 'sql'
  | 'shell'
  | 'yaml'
  | 'xml'
  | 'html'
  | 'css';

// Chunking strategies
export type TChunkingStrategy = 'character' | 'word' | 'sentence' | 'paragraph' | 'semantic';

// Chunking configuration
export interface IChunkingConfig {
  chunkSize: number;
  chunkOverlap: number;
  strategy?: TChunkingStrategy;
  preserveWordBoundaries?: boolean;
  respectSentenceBoundaries?: boolean;
  minChunkSize?: number; // Minimum chunk size to avoid tiny chunks
}

// Base metadata for all chunks - standardized fields that all vectors must have
export interface IBaseChunkMetadata {
  // Core identification
  title: string;
  category: 'docs' | 'code';
  content_type: TContentType;

  // Chunking information
  chunk_index: number;
  total_chunks: number;

  // Content tracking
  content_hash: string;
  timestamp: string; // Unified timestamp field (ISO string)

  // Optional categorization
  collection?: string;

  // Multi-tenancy and user identification (optional)
  userID?: string;
  projectID?: string;
  tenantID?: string;

  // Content storage
  content?: string; // Chunk content for search results
  original_content?: string; // Full original content

  // Embedding metadata (set during processing)
  embedding_provider?: string;
  embedding_model?: string;
  embedding_dimensions?: number;
}

// Text chunk with generic metadata
export interface ITextChunk<T extends IBaseChunkMetadata = IBaseChunkMetadata> {
  id: string;
  content: string;
  metadata: T;
}

// Extended metadata for file uploads - adds file-specific fields
export interface IFileChunkMetadata extends IBaseChunkMetadata {
  // File-specific metadata
  file_name?: string;
  file_size?: number;
  file_type?: string;
}

// Extended metadata for text submissions - uses base metadata without additional fields
export interface ITextChunkMetadata extends IBaseChunkMetadata {
  // Text submissions use only the base metadata fields
  // No additional fields needed for text submissions
}

// Code detection result
export interface ICodeDetectionResult {
  isCode: boolean;
  confidence: number;
  detectedLanguage?: string;
  reasons: string[];
}

// Content section for structured chunking
export interface IContentSection {
  content: string;
  title?: string;
  description?: string;
  source?: string;
  language?: string;
  code?: boolean;
}

// File processing result
export interface IFileProcessingResult {
  success: boolean;
  content?: string;
  contentType?: TContentType;
  error?: string;
  metadata?: {
    pages?: number;
    wordCount?: number;
    language?: string;
  };
}

// Supported file extensions
export const SUPPORTED_EXTENSIONS = [
  '.txt',
  '.md',
  '.json',
  '.pdf',
  '.doc',
  '.docx',
  '.py',
  '.js',
  '.ts',
  '.java',
  '.cpp',
  '.c',
  '.h',
  '.cs',
  '.php',
  '.rb',
  '.go',
  '.rs',
  '.swift',
  '.kt',
  '.scala',
  '.r',
  '.sql',
  '.sh',
  '.bash',
  '.yml',
  '.yaml',
  '.xml',
  '.html',
  '.css',
] as const;

export type TSupportedExtension = (typeof SUPPORTED_EXTENSIONS)[number];

// File type categories
export const FILE_TYPE_CATEGORIES: Record<string, 'docs' | 'code'> = {
  '.txt': 'docs',
  '.md': 'docs',
  '.pdf': 'docs',
  '.doc': 'docs',
  '.docx': 'docs',
  '.json': 'docs',
  '.py': 'code',
  '.js': 'code',
  '.ts': 'code',
  '.java': 'code',
  '.cpp': 'code',
  '.c': 'code',
  '.h': 'code',
  '.cs': 'code',
  '.php': 'code',
  '.rb': 'code',
  '.go': 'code',
  '.rs': 'code',
  '.swift': 'code',
  '.kt': 'code',
  '.scala': 'code',
  '.r': 'code',
  '.sql': 'code',
  '.sh': 'code',
  '.bash': 'code',
  '.yml': 'docs',
  '.yaml': 'docs',
  '.xml': 'docs',
  '.html': 'code',
  '.css': 'code',
};

// Language detection mappings
export const EXTENSION_TO_LANGUAGE: Record<string, string> = {
  '.py': 'python',
  '.js': 'javascript',
  '.ts': 'typescript',
  '.java': 'java',
  '.cpp': 'cpp',
  '.c': 'c',
  '.h': 'c',
  '.cs': 'csharp',
  '.php': 'php',
  '.rb': 'ruby',
  '.go': 'go',
  '.rs': 'rust',
  '.swift': 'swift',
  '.kt': 'kotlin',
  '.scala': 'scala',
  '.r': 'r',
  '.sql': 'sql',
  '.sh': 'shell',
  '.bash': 'shell',
  '.yml': 'yaml',
  '.yaml': 'yaml',
  '.xml': 'xml',
  '.html': 'html',
  '.css': 'css',
};

// Content hash generation
export interface IContentHashOptions {
  algorithm: 'SHA-256' | 'SHA-1' | 'MD5';
  encoding: 'hex' | 'base64';
}

// Default chunking configuration - now uses word-based chunking
export const DEFAULT_CHUNKING_CONFIG: IChunkingConfig = {
  chunkSize: 1000,
  chunkOverlap: 200,
  strategy: 'word',
  preserveWordBoundaries: true,
  respectSentenceBoundaries: true,
  minChunkSize: 100,
};

// Metadata validation
export interface IMetadataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validates chunk metadata for consistency and completeness
 */
export function validateChunkMetadata(
  metadata: IFileChunkMetadata | ITextChunkMetadata
): IMetadataValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  if (!metadata.title || metadata.title.trim().length === 0) {
    errors.push('Title is required and cannot be empty');
  }

  if (!metadata.category || !['docs', 'code'].includes(metadata.category)) {
    errors.push('Category must be either "docs" or "code"');
  }

  if (!metadata.content_type) {
    errors.push('Content type is required');
  }

  if (typeof metadata.chunk_index !== 'number' || metadata.chunk_index < 0) {
    errors.push('Chunk index must be a non-negative number');
  }

  if (typeof metadata.total_chunks !== 'number' || metadata.total_chunks < 1) {
    errors.push('Total chunks must be a positive number');
  }

  if (metadata.chunk_index >= metadata.total_chunks) {
    errors.push('Chunk index cannot be greater than or equal to total chunks');
  }

  if (!metadata.content_hash || metadata.content_hash.trim().length === 0) {
    errors.push('Content hash is required');
  }

  if (!metadata.timestamp) {
    errors.push('Timestamp is required');
  } else {
    // Validate ISO timestamp format
    const date = new Date(metadata.timestamp);
    if (Number.isNaN(date.getTime())) {
      errors.push('Timestamp must be a valid ISO date string');
    }
  }

  // Optional field validation
  if (metadata.collection && metadata.collection.trim().length === 0) {
    warnings.push('Collection field is empty but present');
  }

  // Validate new ID fields
  if (metadata.userID && metadata.userID.trim().length === 0) {
    warnings.push('UserID field is empty but present');
  }

  if (metadata.projectID && metadata.projectID.trim().length === 0) {
    warnings.push('ProjectID field is empty but present');
  }

  if (metadata.tenantID && metadata.tenantID.trim().length === 0) {
    warnings.push('TenantID field is empty but present');
  }

  if (metadata.embedding_dimensions && metadata.embedding_dimensions <= 0) {
    errors.push('Embedding dimensions must be a positive number');
  }

  // File-specific validation
  if ('file_size' in metadata && metadata.file_size !== undefined) {
    if (metadata.file_size < 0) {
      errors.push('File size cannot be negative');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
