/**
 * Authentication Types
 *
 * Type definitions for authentication, authorization, and API key management.
 */

// TEMPORARILY DISABLED TO AVOID GLOBAL SCOPE ASYNC OPERATIONS
// import type { TApiKey, TUser } from '@dbSchema';

// Temporary placeholder types to avoid global scope async operations
type TApiKey = {
  id: string;
  userId: string;
  teamId?: string;
  name: string;
  keyPrefix: string;
  permissions?: string[];
  scopedPermissions?: string[];
  usageCount: number;
  usageLimit?: number;
  isActive: boolean;
  lastUsedAt?: Date;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
};

type TUser = {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
};

// ============================================================================
// Authentication Context Types
// ============================================================================

/**
 * Authentication method used for the current request
 */
export type TAuthMethod = 'session' | 'api_key' | null;

/**
 * API key authentication context
 */
export interface IApiKeyContext {
  /** The API key record from database */
  apiKey: TApiKey | null;
  /** Authentication method used */
  authMethod: TAuthMethod;
  /** Whether the request is authenticated */
  isAuthenticated: boolean;
  /** User associated with the API key */
  user: TUser | null;
}

/**
 * Session authentication context
 */
export interface ISessionContext {
  /** Whether the request has a valid session */
  isAuthenticated: boolean;
  /** User from session */
  user: TUser | null;
  /** Session data */
  session: Record<string, unknown> | null;
}

/**
 * Combined authentication context
 */
export interface IAuthContext extends IApiKeyContext, ISessionContext {
  /** Primary authentication method */
  authMethod: TAuthMethod;
}

// ============================================================================
// API Key Types
// ============================================================================

/**
 * API key creation request
 */
export interface ICreateApiKeyRequest {
  /** Human-readable name for the API key */
  name: string;
  /** Optional team ID for team-scoped keys */
  teamId?: string;
  /** Permissions for the API key */
  permissions?: string[];
  /** Scoped permissions for team context */
  scopedPermissions?: string[];
  /** Usage limit for the API key */
  usageLimit?: number;
  /** Expiration date for the API key */
  expiresAt?: string; // ISO date string
}

/**
 * API key update request
 */
export interface IUpdateApiKeyRequest {
  /** Updated name */
  name?: string;
  /** Updated permissions */
  permissions?: string[];
  /** Updated scoped permissions */
  scopedPermissions?: string[];
  /** Updated usage limit */
  usageLimit?: number;
  /** Updated expiration date */
  expiresAt?: string; // ISO date string
  /** Whether the key is active */
  isActive?: boolean;
}

/**
 * API key response (without sensitive data)
 */
export interface IApiKeyResponse {
  /** API key ID */
  id: string;
  /** User ID */
  userId: string;
  /** Team ID (if team-scoped) */
  teamId?: string;
  /** Human-readable name */
  name: string;
  /** Key prefix for identification */
  keyPrefix: string;
  /** Permissions */
  permissions?: string[];
  /** Scoped permissions */
  scopedPermissions?: string[];
  /** Usage statistics */
  usageCount: number;
  /** Usage limit */
  usageLimit?: number;
  /** Whether the key is active */
  isActive: boolean;
  /** Last used timestamp */
  lastUsedAt?: string;
  /** Expiration date */
  expiresAt?: string;
  /** Creation timestamp */
  createdAt: string;
  /** Update timestamp */
  updatedAt: string;
}

/**
 * API key creation response (includes the actual key)
 */
export interface ICreateApiKeyResponse extends IApiKeyResponse {
  /** The actual API key (only returned on creation) */
  key: string;
}

// ============================================================================
// Permission Types (Unified with RBAC System)
// ============================================================================

// Import unified permission types from the main RBAC system
import type { TPermission, TPermissionCategory } from '@/types/teams';

/**
 * API key permission configuration
 */
export interface IApiKeyPermissionConfig {
  /** Permissions granted to this API key */
  permissions: TPermission[];
  /** Scoped permissions with additional restrictions */
  scopedPermissions?: TPermission[];
  /** Permission scope restrictions */
  scope?: {
    type: 'global' | 'team' | 'project' | 'resource';
    id?: string;
    restrictions?: {
      allowedActions?: string[];
      deniedActions?: string[];
      allowedCategories?: TPermissionCategory[];
      deniedCategories?: TPermissionCategory[];
    };
  };
}

/**
 * Permission categories for API key management UI
 */
export interface IApiKeyPermissionCategory {
  /** Category name */
  name: string;
  /** Category description */
  description: string;
  /** Category identifier */
  category: TPermissionCategory;
  /** Permissions in this category */
  permissions: TPermission[];
}

/**
 * API key permission categories mapped to RBAC system
 */
export const API_KEY_PERMISSION_CATEGORIES: IApiKeyPermissionCategory[] = [
  {
    name: 'Team Management',
    description: 'Team and member management operations',
    category: 'team',
    permissions: ['read.team', 'manage.team', 'create.team', 'update.team', 'delete.team'],
  },
  {
    name: 'Project Management',
    description: 'Project and collection management',
    category: 'project',
    permissions: [
      'read.project',
      'manage.project',
      'create.project',
      'update.project',
      'delete.project',
    ],
  },
  {
    name: 'Vector Operations',
    description: 'Document and vector management',
    category: 'vectors',
    permissions: [
      'read.vectors',
      'manage.vectors',
      'create.vectors',
      'update.vectors',
      'delete.vectors',
    ],
  },
  {
    name: 'Billing & Subscriptions',
    description: 'Billing and subscription management',
    category: 'billing',
    permissions: [
      'read.billing',
      'manage.billing',
      'create.billing',
      'update.billing',
      'delete.billing',
    ],
  },
  {
    name: 'Analytics & Reporting',
    description: 'Usage analytics and reporting',
    category: 'analytics',
    permissions: [
      'read.analytics',
      'manage.analytics',
      'create.analytics',
      'update.analytics',
      'delete.analytics',
    ],
  },
  {
    name: 'API Key Management',
    description: 'API key creation and management',
    category: 'api_keys',
    permissions: [
      'read.api_keys',
      'manage.api_keys',
      'create.api_keys',
      'update.api_keys',
      'delete.api_keys',
    ],
  },
];

// ============================================================================
// Error Types
// ============================================================================

/**
 * Authentication error types
 */
export type TAuthErrorType =
  | 'missing_auth'
  | 'invalid_session'
  | 'invalid_api_key'
  | 'expired_api_key'
  | 'usage_limit_exceeded'
  | 'insufficient_permissions'
  | 'inactive_api_key';

/**
 * Authentication error response
 */
export interface IAuthErrorResponse {
  /** Error status */
  status: 'error';
  /** Error message */
  error: string;
  /** Error type */
  error_type: TAuthErrorType;
  /** Additional error details */
  details?: {
    message?: string;
    required_permissions?: string[];
    current_permissions?: string[];
    [key: string]: unknown;
  };
  /** Timestamp */
  timestamp: string;
}
