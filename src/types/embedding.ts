/**
 * Embedding Service Types
 *
 * Type definitions for embedding providers, configurations, and responses.
 */

export type TEmbeddingProvider = 'openai' | 'voyageai' | 'cloudflare';

// Provider-specific response types
export interface IVoyageAIEmbeddingResponse {
  data: Array<{
    embedding: number[];
    index: number;
  }>;
  model: string;
  usage: {
    total_tokens: number;
  };
}

export interface ICloudflareAIEmbeddingResponse {
  shape: number[];
  data: number[][];
}

export interface IOpenAIEmbeddingResponse {
  data: Array<{
    embedding: number[];
    index: number;
  }>;
  model: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

export interface IEmbeddingConfig {
  provider: TEmbeddingProvider;
  model: string;
  dimensions: number;
}

export interface IEmbeddingResult {
  embedding: number[];
  provider: TEmbeddingProvider;
  model: string;
  dimensions: number;
  usage?: {
    prompt_tokens?: number;
    total_tokens: number;
  };
}

// Provider model mappings
export const PROVIDER_MODELS: Record<TEmbeddingProvider, string[]> = {
  openai: ['text-embedding-3-small', 'text-embedding-3-large', 'text-embedding-ada-002'],
  voyageai: [
    'voyage-3-large',
    'voyage-3',
    'voyage-code-2',
    'voyage-large-2-instruct',
    'voyage-large-2',
    'voyage-2',
    'voyage-law-2',
    'voyage-code',
  ],
  cloudflare: [
    '@cf/baai/bge-large-en-v1.5',
    '@cf/baai/bge-base-en-v1.5',
    '@cf/baai/bge-small-en-v1.5',
    '@cf/baai/bge-m3',
  ],
};

// Default models for each provider
export const DEFAULT_MODELS: Record<TEmbeddingProvider, string> = {
  openai: 'text-embedding-3-large',
  voyageai: 'voyage-3-large',
  cloudflare: '@cf/baai/bge-large-en-v1.5',
};

// Model dimensions mapping
export const MODEL_DIMENSIONS: Record<string, number> = {
  // OpenAI models
  'text-embedding-3-small': 1536,
  'text-embedding-3-large': 3072,
  'text-embedding-ada-002': 1536,

  // VoyageAI models
  'voyage-3-large': 1024,
  'voyage-3': 1024,
  'voyage-code-2': 1536,
  'voyage-large-2-instruct': 1024,
  'voyage-large-2': 1024,
  'voyage-2': 1024,
  'voyage-law-2': 1024,
  'voyage-code': 1536,

  // Cloudflare models
  '@cf/baai/bge-large-en-v1.5': 1024,
  '@cf/baai/bge-base-en-v1.5': 768,
  '@cf/baai/bge-small-en-v1.5': 384,
  '@cf/baai/bge-m3': 1024,
};

// Category-specific model preferences
export const CATEGORY_MODEL_PREFERENCES: Record<
  string,
  Partial<Record<TEmbeddingProvider, string>>
> = {
  code: {
    voyageai: 'voyage-code-2',
    openai: 'text-embedding-3-large',
    cloudflare: '@cf/baai/bge-large-en-v1.5',
  },
  docs: {
    voyageai: 'voyage-3-large',
    openai: 'text-embedding-3-large',
    cloudflare: '@cf/baai/bge-large-en-v1.5',
  },
};

export interface IEmbeddingServiceConfig {
  provider?: TEmbeddingProvider;
  model?: string;
  category?: 'code' | 'docs';
}

// Backward compatibility exports (deprecated - use I/T prefixed versions)
export type EmbeddingProvider = TEmbeddingProvider;
export type VoyageAIEmbeddingResponse = IVoyageAIEmbeddingResponse;
export type CloudflareAIEmbeddingResponse = ICloudflareAIEmbeddingResponse;
export type OpenAIEmbeddingResponse = IOpenAIEmbeddingResponse;
export type EmbeddingConfig = IEmbeddingConfig;
export type EmbeddingResult = IEmbeddingResult;
export type EmbeddingServiceConfig = IEmbeddingServiceConfig;
