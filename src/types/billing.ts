/**
 * Billing Types and Interfaces
 *
 * Comprehensive type definitions for billing system including
 * subscription plans, payment providers, and billing operations.
 */

// ============================================================================
// Billing Provider Types
// ============================================================================

/**
 * Supported billing providers
 */
export type TBillingProvider = 'paddle' | 'paypal';

/**
 * Subscription status types
 */
export type TSubscriptionStatus =
  | 'active'
  | 'canceled'
  | 'past_due'
  | 'unpaid'
  | 'trialing'
  | 'paused';

/**
 * Transaction status types
 */
export type TTransactionStatus = 'completed' | 'pending' | 'failed' | 'refunded' | 'disputed';

/**
 * Transaction types
 */
export type TTransactionType = 'subscription' | 'api_requests';

/**
 * Payment method types
 */
export type TPaymentMethod = 'card' | 'paypal' | 'bank_transfer' | 'apple_pay' | 'google_pay';

// ============================================================================
// Subscription Plan Types
// ============================================================================

/**
 * Available subscription plans
 */
export type TSubscriptionPlan = 'free' | 'starter' | 'professional' | 'enterprise';

/**
 * Billing intervals
 */
export type TBillingInterval = 'monthly' | 'yearly';

/**
 * Plan features and limits
 */
export interface IPlanLimits {
  // Team limits
  maxTeamMembers: number;
  maxTeams: number;

  // Project limits
  maxProjects: number;
  maxCollections: number;

  // Document database limits
  maxDocuments: number;
  maxDocumentDimensions: number;
  maxSearchRequests: number; // per month

  // API limits
  maxApiKeys: number;
  maxApiRequests: number; // per month

  // Storage limits
  maxStorageGB: number;
  maxFileSize: number; // in MB

  // Feature flags
  advancedAnalytics: boolean;
  prioritySupport: boolean;
  customIntegrations: boolean;
  ssoEnabled: boolean;
  auditLogs: boolean;
}

/**
 * Subscription plan configuration
 */
export interface ISubscriptionPlanConfig {
  id: TSubscriptionPlan;
  name: string;
  description: string;
  limits: IPlanLimits;
  pricing: {
    monthly: {
      amount: number; // in cents
      currency: string;
      paddlePlanId?: string;
      paypalPlanId?: string;
    };
    yearly: {
      amount: number; // in cents
      currency: string;
      paddlePlanId?: string;
      paypalPlanId?: string;
      discount?: number; // percentage discount
    };
  };
  features: string[];
  popular?: boolean;
  trialDays?: number;
}

// ============================================================================
// Billing Service Interfaces
// ============================================================================

/**
 * Subscription creation parameters
 */
export interface ICreateSubscriptionParams {
  teamId: string;
  planId: TSubscriptionPlan;
  interval: TBillingInterval;
  provider: TBillingProvider;
  paymentMethodId?: string;
  trialDays?: number;
  couponCode?: string;
}

/**
 * Subscription update parameters
 */
export interface IUpdateSubscriptionParams {
  planId?: TSubscriptionPlan;
  interval?: TBillingInterval;
  paymentMethodId?: string;
  pauseCollection?: boolean;
}

/**
 * Payment intent creation parameters
 */
export interface ICreatePaymentIntentParams {
  teamId: string;
  amount: number;
  currency: string;
  description: string;
  metadata?: Record<string, string>;
}

/**
 * API request package purchase parameters
 */
export interface IPurchaseApiRequestsParams {
  teamId: string;
  requestCount: number; // Number of additional requests to purchase (in thousands)
  provider: TBillingProvider;
  paymentMethodId?: string;
}

/**
 * Additional API requests data
 */
export interface IAdditionalApiRequestsData {
  id: string;
  teamId: string;
  requestCount: number;
  usedCount: number;
  remainingCount: number;
  expiresAt: Date;
  isActive: boolean;
  createdAt: Date;
}

/**
 * Webhook event data
 */
export interface IWebhookEvent {
  id: string;
  type: string;
  provider: TBillingProvider;
  data: Record<string, unknown>;
  timestamp: Date;
  signature?: string;
}

/**
 * Usage tracking data
 */
export interface IUsageData {
  teamId: string;
  period: {
    start: Date;
    end: Date;
  };
  metrics: {
    apiRequests: number;
    searchRequests: number;
    documentsStored: number;
    storageUsedGB: number;
    teamMembers: number;
    projects: number;
    collections: number;
  };
  additionalApiRequests?: {
    total: number;
    used: number;
    remaining: number;
    packages: IAdditionalApiRequestsData[];
  };
}

/**
 * Billing dashboard data
 */
export interface IBillingDashboard {
  subscription?: {
    id: string;
    planId: TSubscriptionPlan;
    status: TSubscriptionStatus;
    currentPeriodStart: Date;
    currentPeriodEnd: Date;
    cancelAtPeriodEnd: boolean;
    trialEnd?: Date;
  };
  usage: IUsageData;
  upcomingInvoice?: {
    amount: number;
    currency: string;
    dueDate: Date;
  };
  paymentMethods: Array<{
    id: string;
    type: TPaymentMethod;
    last4?: string;
    expiryMonth?: number;
    expiryYear?: number;
    isDefault: boolean;
  }>;
  invoices: Array<{
    id: string;
    amount: number;
    currency: string;
    status: string;
    createdAt: Date;
    paidAt?: Date;
    downloadUrl?: string;
  }>;
}

// ============================================================================
// Provider-Specific Types
// ============================================================================

/**
 * Paddle-specific subscription data
 */
export interface IPaddleSubscriptionData {
  subscription_id: string;
  plan_id: string;
  user_id: string;
  status: string;
  next_payment: {
    amount: number;
    currency: string;
    date: string;
  };
  update_url: string;
  cancel_url: string;
}

/**
 * Paddle-specific webhook data
 */
export interface IPaddleWebhookData {
  alert_name: string;
  alert_id: string;
  subscription_id?: string;
  plan_id?: string;
  user_id?: string;
  status?: string;
  [key: string]: unknown;
}

/**
 * PayPal-specific subscription data
 */
export interface IPayPalSubscriptionData {
  id: string;
  plan_id: string;
  status: string;
  billing_info: {
    outstanding_balance: {
      currency_code: string;
      value: string;
    };
    cycle_executions: Array<{
      tenure_type: string;
      sequence: number;
      cycles_completed: number;
      cycles_remaining: number;
    }>;
  };
}

/**
 * PayPal-specific webhook data
 */
export interface IPayPalWebhookData {
  id: string;
  event_type: string;
  resource_type: string;
  resource: Record<string, unknown>;
  create_time: string;
}

// ============================================================================
// Error Types
// ============================================================================

/**
 * Billing error types
 */
export type TBillingErrorType =
  | 'payment_failed'
  | 'subscription_not_found'
  | 'plan_not_found'
  | 'invalid_payment_method'
  | 'insufficient_funds'
  | 'provider_error'
  | 'webhook_verification_failed'
  | 'usage_limit_exceeded'
  | 'subscription_canceled';

/**
 * Billing error interface
 */
export interface IBillingError extends Error {
  type: TBillingErrorType;
  provider?: TBillingProvider;
  code?: string;
  details?: Record<string, unknown>;
}
