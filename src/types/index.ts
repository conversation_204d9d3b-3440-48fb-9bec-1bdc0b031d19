/**
 * Types Index
 *
 * Central export point for all type definitions.
 */

// API Types
export * from './api';
// Authentication Types
export * from './auth';
// Billing Types
export * from './billing';
// Content Processing Types
export * from './content';
// Email and Notification Types
export * from './email';
// Embedding Types
export * from './embedding';
// Permission Types
export * from './permissions';
// Team Types (excluding types already exported by permissions)
export type { ITeamContext, TTeamRole } from './teams';

// Environment Types (from global.d.ts)
export interface ICloudflareEnv {
  // Cloudflare bindings
  ai: Ai;
  vdb: VectorizeIndex;
  kv: KVNamespace;
  r2: R2Bucket;
  browser: Fetcher;

  // Environment variables
  EMBEDDING_PROVIDER: 'openai' | 'voyageai' | 'cloudflare';
  EMBEDDING_MODEL: string;
  EMBEDDING_DIMENSIONS: string;
  VECTOR_DIMENSIONS: string;
  OPENAI_BASE_URL?: string;
  OPENAI_API_KEY?: string;
  OPENROUTER_API_KEY?: string;
  VOYAGEAI_API_KEY?: string;
  MCP_MAX_CONNECTIONS?: string;
  MCP_CACHE_TTL?: string;
  ALLOWED_FILE_EXTENSIONS?: string;

  // Database configuration
  DATABASE_URL: string;

  // Better Auth configuration
  BETTER_AUTH_SECRET: string;
  BETTER_AUTH_URL?: string;

  // Email configuration
  RESEND_API_KEY?: string;
  FROM_EMAIL?: string;
  FROM_NAME?: string;
  SUPPORT_EMAIL?: string;

  // Security and rate limiting
  RATE_LIMIT_REQUESTS_PER_MINUTE?: string;
  RATE_LIMIT_ENABLED?: string;

  // Logging and monitoring
  LOG_LEVEL?: 'debug' | 'info' | 'warn' | 'error';
  ENABLE_REQUEST_LOGGING?: string;

  // CORS configuration
  CORS_ORIGINS?: string;
  CORS_METHODS?: string;
  CORS_HEADERS?: string;
}

// Hono Environment Type
export interface IHonoEnv {
  Bindings: ICloudflareEnv;
}

// Re-export commonly used external types
export type { Context } from 'hono';

// Backward compatibility exports (deprecated - use I/T prefixed versions)
export type CloudflareEnv = ICloudflareEnv;
export type HonoEnv = IHonoEnv;
