/**
 * Permission Types
 *
 * Comprehensive type definitions for the RBAC permission system.
 * Extends the existing team types with enhanced permission functionality.
 */

import type {
  IPermissionCheckResult,
  IPermissionContext,
  IPermissionStructure,
  TPermission,
  TPermissionAction,
  TPermissionCategory,
} from '@utils/permission-constants';
import type { TTeamRole } from './teams';

// Re-export permission types for convenience
export type {
  TPermission,
  TPermissionCategory,
  TPermissionAction,
  IPermissionStructure,
  IPermissionContext,
  IPermissionCheckResult,
};

// ============================================================================
// Enhanced Permission Types
// ============================================================================

/**
 * Resource-specific permission context
 */
export interface IResourcePermissionContext extends IPermissionContext {
  resourceType:
    | 'team'
    | 'project'
    | 'collection'
    | 'document'
    | 'api_key'
    | 'billing'
    | 'search'
    | 'upload'
    | 'analytics'
    | 'unknown';
  resourceId?: string;
  resourceOwnerId?: string;
  resourceTeamId?: string;
}

/**
 * Permission check options
 */
export interface IPermissionCheckOptions {
  strict?: boolean; // If true, requires exact permission match
  allowInheritance?: boolean; // If true, allows role hierarchy inheritance
  context?: IResourcePermissionContext;
}

/**
 * Enhanced permission check result with detailed information
 */
export interface IEnhancedPermissionCheckResult extends IPermissionCheckResult {
  checkedPermissions: TPermission[];
  userRole?: TTeamRole;
  effectivePermissions?: TPermission[];
  inheritedFromRole?: boolean;
  resourceAccess?: {
    canRead: boolean;
    canWrite: boolean;
    canDelete: boolean;
    canManage: boolean;
  };
}

/**
 * Permission audit log entry
 */
export interface IPermissionAuditLog {
  id: string;
  userId: string;
  teamId?: string;
  action: string;
  resource: string;
  resourceId: string;
  permission: TPermission;
  allowed: boolean;
  reason?: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  context?: Record<string, unknown>;
}

/**
 * Permission validation result
 */
export interface IPermissionValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

/**
 * Role permission matrix
 */
export interface IRolePermissionMatrix {
  role: TTeamRole;
  permissions: {
    [K in TPermissionCategory]: {
      read: boolean;
      create: boolean;
      update: boolean;
      delete: boolean;
      manage: boolean;
    };
  };
}

/**
 * Permission scope definition
 */
export interface IPermissionScope {
  type: 'global' | 'team' | 'project' | 'resource';
  id?: string;
  restrictions?: {
    allowedActions?: TPermissionAction[];
    deniedActions?: TPermissionAction[];
    allowedCategories?: TPermissionCategory[];
    deniedCategories?: TPermissionCategory[];
  };
}

// API key permission configuration is now defined in auth.ts to avoid duplication

/**
 * Team member permission override
 */
export interface ITeamMemberPermissionOverride {
  userId: string;
  teamId: string;
  addedPermissions: TPermission[];
  removedPermissions: TPermission[];
  reason?: string;
  expiresAt?: Date;
  createdBy: string;
  createdAt: Date;
}

/**
 * Permission policy definition
 */
export interface IPermissionPolicy {
  id: string;
  name: string;
  description?: string;
  conditions: {
    roles?: TTeamRole[];
    permissions?: TPermission[];
    resourceTypes?: string[];
    customRules?: string; // JSON string of custom rules
  };
  actions: {
    allow?: TPermission[];
    deny?: TPermission[];
    require?: TPermission[];
  };
  priority: number;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Permission Service Types
// ============================================================================

/**
 * Permission service interface
 */
export interface IPermissionService {
  /**
   * Check if user has permission
   */
  hasPermission(
    userId: string,
    permission: TPermission,
    context?: IResourcePermissionContext,
    options?: IPermissionCheckOptions
  ): Promise<IEnhancedPermissionCheckResult>;

  /**
   * Check if user has any of the permissions
   */
  hasAnyPermission(
    userId: string,
    permissions: TPermission[],
    context?: IResourcePermissionContext,
    options?: IPermissionCheckOptions
  ): Promise<IEnhancedPermissionCheckResult>;

  /**
   * Check if user has all permissions
   */
  hasAllPermissions(
    userId: string,
    permissions: TPermission[],
    context?: IResourcePermissionContext,
    options?: IPermissionCheckOptions
  ): Promise<IEnhancedPermissionCheckResult>;

  /**
   * Get user's effective permissions
   */
  getUserPermissions(
    userId: string,
    teamId?: string,
    context?: IResourcePermissionContext
  ): Promise<TPermission[]>;

  /**
   * Validate permission configuration
   */
  validatePermissions(permissions: string[]): IPermissionValidationResult;

  /**
   * Log permission check for audit
   */
  logPermissionCheck(
    userId: string,
    permission: TPermission,
    result: IEnhancedPermissionCheckResult,
    context?: IResourcePermissionContext
  ): Promise<void>;
}

/**
 * Permission middleware configuration
 */
export interface IPermissionMiddlewareConfig {
  requiredPermissions?: TPermission[];
  requireAny?: boolean; // If true, user needs any of the permissions (OR logic)
  requireAll?: boolean; // If true, user needs all permissions (AND logic)
  allowInheritance?: boolean;
  resourceType?: string;
  resourceIdParam?: string; // Parameter name to extract resource ID from
  customCheck?: (
    context: IResourcePermissionContext,
    permissions: TPermission[]
  ) => Promise<IEnhancedPermissionCheckResult>;
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Permission string literal union from constants
 */
export type TPermissionLiteral =
  | 'read.team'
  | 'manage.team'
  | 'create.team'
  | 'update.team'
  | 'delete.team'
  | 'read.project'
  | 'manage.project'
  | 'create.project'
  | 'update.project'
  | 'delete.project'
  | 'read.billing'
  | 'manage.billing'
  | 'create.billing'
  | 'update.billing'
  | 'delete.billing'
  | 'read.vectors'
  | 'manage.vectors'
  | 'create.vectors'
  | 'update.vectors'
  | 'delete.vectors'
  | 'read.analytics'
  | 'manage.analytics'
  | 'create.analytics'
  | 'update.analytics'
  | 'delete.analytics'
  | 'read.api_keys'
  | 'manage.api_keys'
  | 'create.api_keys'
  | 'update.api_keys'
  | 'delete.api_keys';

/**
 * Permission category literal union
 */
export type TPermissionCategoryLiteral =
  | 'team'
  | 'project'
  | 'billing'
  | 'vectors'
  | 'analytics'
  | 'api_keys';

/**
 * Permission action literal union
 */
export type TPermissionActionLiteral = 'read' | 'manage' | 'create' | 'update' | 'delete';

/**
 * Role hierarchy mapping
 */
export type TRoleHierarchy = {
  [K in TTeamRole]: number;
};

/**
 * Permission matrix for role
 */
export type TRolePermissions = {
  [K in TTeamRole]: TPermission[];
};
