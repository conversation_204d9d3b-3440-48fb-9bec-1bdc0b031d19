/**
 * API Types and Interfaces
 *
 * Centralized type definitions for all API endpoints, requests, and responses.
 */

import { z } from 'zod';

// Common API Response Types
export interface IErrorDetails {
  field?: string;
  code?: string;
  message?: string;
  [key: string]: unknown;
}

export interface IMetadata {
  [key: string]: string | number | boolean | null | undefined;
}
export interface IBaseResponse {
  status: 'success' | 'error' | 'duplicate';
  timestamp: string;
}

export interface IErrorResponse extends IBaseResponse {
  status: 'error';
  error: string;
  error_type?: string;
  details?: IErrorDetails;
}

export interface ISuccessResponse<T = unknown> extends IBaseResponse {
  status: 'success';
  data: T;
  message?: string;
}

export interface IDuplicateResponse extends IBaseResponse {
  status: 'duplicate';
  message: string;
  existing_content?: {
    content_hash: string;
    upload_timestamp: string;
    chunks_count: number;
  };
}

// Search API Types
export const SearchRequestSchema = z.object({
  query: z.string().min(1, 'Query cannot be empty'),
  collection: z.string().optional(),
  limit: z.number().min(1).max(20).default(5),
  embedding_provider: z.enum(['openai', 'voyageai', 'cloudflare']).optional(),
  embedding_model: z.string().optional(),
  // Multi-tenancy filtering
  userID: z.string().optional(),
  projectID: z.string().optional(),
  tenantID: z.string().optional(),
  // Vector database query parameters
  topK: z.number().min(1).max(20).optional(),
  returnMetadata: z.boolean().default(true),
  returnValues: z.boolean().default(false),
});

export type TSearchRequest = z.infer<typeof SearchRequestSchema>;

export interface ISearchResult {
  id: string;
  content: string;
  score: number;
}

export interface ISearchResponse extends ISuccessResponse<ISearchResult[]> {
  data: ISearchResult[];
  metadata: {
    query: string;
    total_results: number;
    search_time_ms: number;
  };
}

// Upload API Types
export const TextSubmissionSchema = z.object({
  text: z.string().min(1, 'Text content cannot be empty'),
  title: z.string().min(1, 'Title cannot be empty'),
  category: z.enum(['docs', 'code']).default('docs'),
  collection: z.string().optional(),
  embedding_provider: z.enum(['openai', 'voyageai', 'cloudflare']).optional(),
  embedding_model: z.string().optional(),
});

export type TTextSubmissionRequest = z.infer<typeof TextSubmissionSchema>;

export interface IUploadResponse extends IBaseResponse {
  data?: {
    chunks_processed: number;
    vectors_created: number;
    content_hash: string;
    collection?: string;
    category: string;
    processing_time_ms: number;
  };
  message?: string;
  existing_content?: {
    content_hash: string;
    upload_timestamp: string;
    chunks_count: number;
  };
}

// Vector Management API Types
export const PaginationSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  collection: z.string().optional(),
  category: z.enum(['docs', 'code']).optional(),
});

export type TPaginationRequest = z.infer<typeof PaginationSchema>;

export const DocumentUpdateSchema = z.object({
  metadata: z.record(z.union([z.string(), z.number(), z.boolean(), z.null()])).optional(),
});

export type TDocumentUpdateRequest = z.infer<typeof DocumentUpdateSchema>;

export const BulkDeleteSchema = z.object({
  collection: z.string().optional(),
  category: z.enum(['docs', 'code']).optional(),
  content_hash: z.string().optional(),
});

export type TBulkDeleteRequest = z.infer<typeof BulkDeleteSchema>;

// Vector Database Configuration Types

/**
 * Configuration options for vector database queries
 *
 * @interface IVectorQueryConfig
 * @property topK - Maximum number of results to return (1-20 with metadata, 1-1000 without)
 * @property returnMetadata - Whether to include metadata in results (default: true)
 * @property returnValues - Whether to include vector values in results (default: false)
 * @property namespace - Optional namespace for multi-tenancy
 */
export interface IDocumentQueryConfig {
  topK?: number;
  returnMetadata?: boolean;
  returnValues?: boolean;
  namespace?: string;
}

export const DocumentQueryConfigSchema = z.object({
  topK: z.number().min(1).max(20).default(5),
  returnMetadata: z.boolean().default(true),
  returnValues: z.boolean().default(false),
  namespace: z.string().optional(),
});

export type TDocumentQueryConfig = z.infer<typeof DocumentQueryConfigSchema>;

/**
 * Represents a vector item with metadata and optional similarity score
 *
 * @interface IVectorItem
 * @property id - Unique identifier for the vector
 * @property metadata - Associated metadata for the vector
 * @property score - Optional similarity score (present in search results)
 */
// Primary interface with new naming
export interface IDocumentItem {
  id: string;
  metadata: IMetadata;
  score?: number;
}

/**
 * Pagination information for paginated responses
 *
 * @interface IPaginationInfo
 * @property current_page - Current page number (1-based)
 * @property total_pages - Total number of pages available
 * @property total_count - Total number of items across all pages
 * @property has_next - Whether there is a next page available
 * @property has_previous - Whether there is a previous page available
 * @property limit - Number of items per page
 */
export interface IPaginationInfo {
  current_page: number;
  total_pages: number;
  total_count: number;
  has_next: boolean;
  has_previous: boolean;
  limit: number;
}

// Primary interfaces with new naming
export interface IDocumentListResponse
  extends ISuccessResponse<{
    documents: IDocumentItem[];
    pagination: IPaginationInfo;
  }> {}

export interface IDocumentResponse extends IBaseResponse {
  data?: IDocumentItem;
}

export interface IBulkDeleteResponse
  extends ISuccessResponse<{
    deleted_count: number;
    filters_applied: IMetadata;
  }> {}

// Collection Info API Types
export const CollectionInfoRequestSchema = z.object({
  collection_name: z.string().min(1, 'Collection name cannot be empty'),
  include_versions: z.boolean().default(false),
});

export type TCollectionInfoRequest = z.infer<typeof CollectionInfoRequestSchema>;

export interface ICollectionInfo {
  name: string;
  description?: string;
  versions?: string[];
  total_documents: number;
  categories: string[];
  last_updated: string;
}

export interface ICollectionInfoResponse extends ISuccessResponse<ICollectionInfo> {}

// Health Check Types
export interface IHealthCheckResponse {
  service: string;
  version: string;
  status: string;
  endpoints: Record<string, string>;
  timestamp: string;
}

// MCP Types
export interface IMCPParams {
  [key: string]: unknown;
}

export interface IMCPResult {
  [key: string]: unknown;
}

export interface IMCPErrorData {
  [key: string]: unknown;
}

export interface IMCPRequest {
  jsonrpc: '2.0';
  id: string | number;
  method: string;
  params?: IMCPParams;
}

export interface IMCPResponse {
  jsonrpc: '2.0';
  id: string | number;
  result?: IMCPResult;
  error?: {
    code: number;
    message: string;
    data?: IMCPErrorData;
  };
}

export interface IMCPNotification {
  jsonrpc: '2.0';
  method: string;
  params?: IMCPParams;
}

export const SearchToolSchema = z.object({
  query: z.string().min(1, 'Query cannot be empty'),
  collection: z.string().optional(),
  limit: z.number().min(1).max(20).default(5),
  embedding_provider: z.enum(['openai', 'voyageai', 'cloudflare']).optional(),
  embedding_model: z.string().optional(),
});

export type TSearchToolRequest = z.infer<typeof SearchToolSchema>;

// Backward compatibility exports (deprecated - use I/T prefixed versions)
export type BaseResponse = IBaseResponse;
export type ErrorResponse = IErrorResponse;
export type SuccessResponse<T = unknown> = ISuccessResponse<T>;
export type SearchRequest = TSearchRequest;
export type SearchResult = ISearchResult;
export type SearchResponse = ISearchResponse;
export type TextSubmissionRequest = TTextSubmissionRequest;
export type UploadResponse = IUploadResponse;
export type PaginationRequest = TPaginationRequest;
export type BulkDeleteRequest = TBulkDeleteRequest;
export type DocumentItem = IDocumentItem;
export type PaginationInfo = IPaginationInfo;
export type DocumentListResponse = IDocumentListResponse;
export type DocumentResponse = IDocumentResponse;
export type BulkDeleteResponse = IBulkDeleteResponse;
export type CollectionInfoRequest = TCollectionInfoRequest;
export type CollectionInfo = ICollectionInfo;
export type CollectionInfoResponse = ICollectionInfoResponse;
export type HealthCheckResponse = IHealthCheckResponse;
export type MCPRequest = IMCPRequest;
export type MCPResponse = IMCPResponse;
export type MCPNotification = IMCPNotification;
export type SearchToolRequest = TSearchToolRequest;
