/**
 * Email Service Types
 *
 * Type definitions for email service, templates, and notifications
 * for API key expiration and rotation management.
 */

import type { TPermission } from './teams';

// ============================================================================
// Email Configuration Types
// ============================================================================

/**
 * Email provider configuration
 */
export interface IEmailConfig {
  provider: 'resend';
  apiKey: string;
  fromEmail: string;
  supportEmail: string;
  baseUrl: string;
}

/**
 * Email template context for API key notifications
 */
export interface IApiKeyEmailContext {
  // User information
  userName: string;
  userEmail: string;

  // API key information
  keyName: string;
  keyPrefix: string;
  keyId: string;

  // Team information (optional)
  teamName?: string;
  teamId?: string;

  // Expiration/rotation information
  expiresAt?: Date;
  daysUntilExpiration?: number;
  rotatedAt?: Date;
  newKeyPrefix?: string;

  // Action URLs
  manageUrl: string;
  rotateUrl: string;
  teamUrl?: string;

  // Additional context
  permissions?: TPermission[];
  usageStats?: {
    totalUsage: number;
    monthlyUsage: number;
    lastUsed: Date | null;
  };
}

/**
 * Email template types for API key management
 */
export type TEmailTemplate =
  | 'api_key_expiring_7_days'
  | 'api_key_expiring_1_day'
  | 'api_key_expired'
  | 'api_key_rotated'
  | 'api_key_rotation_scheduled'
  | 'api_key_rotation_failed'
  | 'bulk_expiration_warning'
  | 'team_api_keys_expiring';

/**
 * Email notification preferences
 */
export interface IEmailNotificationPreferences {
  userId: string;
  teamId?: string;

  // Expiration notifications
  expirationWarnings: boolean;
  expirationWarningDays: number[]; // e.g., [7, 1] for 7 days and 1 day

  // Rotation notifications
  rotationNotifications: boolean;
  rotationFailureAlerts: boolean;

  // Bulk operation notifications
  bulkOperationSummaries: boolean;

  // Team-level notifications (for admins)
  teamExpirationSummaries: boolean;

  // Delivery preferences
  emailEnabled: boolean;
  webhookEnabled: boolean;
  webhookUrl?: string;

  // Frequency controls
  maxEmailsPerDay: number;
  quietHours?: {
    start: string; // HH:MM format
    end: string; // HH:MM format
    timezone: string;
  };
}

// ============================================================================
// Email Message Types
// ============================================================================

/**
 * Email message structure
 */
export interface IEmailMessage {
  to: string | string[];
  from: string;
  subject: string;
  html: string;
  text?: string;
  replyTo?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: IEmailAttachment[];
  headers?: Record<string, string>;
  tags?: IEmailTag[];
}

/**
 * Email attachment
 */
export interface IEmailAttachment {
  filename: string;
  content: string | Buffer;
  contentType: string;
  disposition?: 'attachment' | 'inline';
  contentId?: string;
}

/**
 * Email tag for tracking and analytics
 */
export interface IEmailTag {
  name: string;
  value: string;
}

/**
 * Email sending result
 */
export interface IEmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
  provider: 'resend';
  timestamp: Date;
}

// ============================================================================
// Notification Queue Types
// ============================================================================

/**
 * Notification job for queue processing
 */
export interface INotificationJob {
  id: string;
  type: TEmailTemplate;
  priority: 'low' | 'normal' | 'high' | 'urgent';

  // Recipient information
  userId: string;
  userEmail: string;
  teamId?: string;

  // Template context
  context: IApiKeyEmailContext;

  // Scheduling
  scheduledAt: Date;
  maxRetries: number;
  retryCount: number;

  // Status tracking
  status: 'pending' | 'processing' | 'sent' | 'failed' | 'cancelled';
  lastAttemptAt?: Date;
  sentAt?: Date;
  error?: string;

  // Metadata
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Batch notification job for bulk operations
 */
export interface IBatchNotificationJob {
  id: string;
  type: 'bulk_expiration_warning' | 'team_expiration_summary';

  // Target information
  teamId?: string;
  userIds?: string[];

  // Job details
  totalRecipients: number;
  processedRecipients: number;
  successfulSends: number;
  failedSends: number;

  // Status
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startedAt?: Date;
  completedAt?: Date;
  error?: string;

  // Context for batch operations
  batchContext: {
    expiringKeysCount: number;
    expiredKeysCount: number;
    teamsAffected: number;
    expirationThreshold: Date;
  };

  // Metadata
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Webhook Types
// ============================================================================

/**
 * Webhook notification payload
 */
export interface IWebhookNotification {
  event: TEmailTemplate;
  timestamp: Date;

  // API key information
  apiKey: {
    id: string;
    name: string;
    prefix: string;
    expiresAt?: Date;
    permissions?: TPermission[];
  };

  // User/team context
  user: {
    id: string;
    email: string;
    name: string;
  };

  team?: {
    id: string;
    name: string;
    slug: string;
  };

  // Event-specific data
  data: Record<string, unknown>;

  // Metadata
  notificationId: string;
  retryCount: number;
}

/**
 * Webhook delivery result
 */
export interface IWebhookResult {
  success: boolean;
  statusCode?: number;
  responseBody?: string;
  error?: string;
  deliveredAt: Date;
  responseTime: number;
}

// ============================================================================
// Template System Types
// ============================================================================

/**
 * Email template definition
 */
export interface IEmailTemplate {
  id: TEmailTemplate;
  name: string;
  description: string;

  // Template content
  subject: string;
  htmlTemplate: string;
  textTemplate?: string;

  // Template metadata
  category: 'expiration' | 'rotation' | 'security' | 'bulk';
  priority: 'low' | 'normal' | 'high' | 'urgent';

  // Customization options
  customizable: boolean;
  requiredVariables: string[];
  optionalVariables: string[];

  // Versioning
  version: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Custom template override for teams
 */
export interface ICustomEmailTemplate {
  id: string;
  teamId: string;
  templateId: TEmailTemplate;

  // Custom content
  customSubject?: string;
  customHtmlTemplate?: string;
  customTextTemplate?: string;

  // Settings
  isActive: boolean;

  // Metadata
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Analytics Types
// ============================================================================

/**
 * Email analytics data
 */
export interface IEmailAnalytics {
  templateId: TEmailTemplate;
  teamId?: string;

  // Delivery metrics
  totalSent: number;
  totalDelivered: number;
  totalBounced: number;
  totalFailed: number;

  // Engagement metrics
  totalOpened: number;
  totalClicked: number;
  uniqueOpens: number;
  uniqueClicks: number;

  // Time-based metrics
  averageDeliveryTime: number;
  peakSendingHour: number;

  // Period information
  periodStart: Date;
  periodEnd: Date;

  // Last updated
  updatedAt: Date;
}
