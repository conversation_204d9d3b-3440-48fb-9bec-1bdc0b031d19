/**
 * Authentication Middleware
 *
 * Better Auth integration middleware for Hono.
 * Handles session management and user authentication.
 */

import type { IHonoEnv } from '@types';
import type { Context, Next } from 'hono';
import { getAuth, type ISession, type IUser } from '../auth';

/**
 * Extended Hono environment with auth context
 */
export interface IAuthHonoEnv {
  Bindings: IHonoEnv['Bindings'];
  Variables: {
    message: string;
    requestId: string;
    user: IUser | null;
    session: ISession['session'] | null;
    isAuthenticated: boolean;
  };
}

/**
 * Authentication middleware for Hono
 *
 * Extracts and validates Better Auth session from request headers.
 * Sets user and session information in Hono context variables.
 *
 * @param c - Hono context
 * @param next - Next middleware function
 *
 * @example
 * ```typescript
 * app.use('*', authMiddleware);
 *
 * app.get('/protected', (c) => {
 *   const user = c.get('user');
 *   const isAuthenticated = c.get('isAuthenticated');
 *
 *   if (!isAuthenticated) {
 *     return c.json({ error: 'Unauthorized' }, 401);
 *   }
 *
 *   return c.json({ user });
 * });
 * ```
 */
export const authMiddleware = async (c: Context<IAuthHonoEnv>, next: Next) => {
  try {
    const auth = await getAuth();

    // Get session from Better Auth
    const sessionData = await auth.api.getSession({
      headers: c.req.raw.headers,
    });

    if (sessionData) {
      // Set authenticated user and session
      c.set('user', sessionData.user as IUser);
      c.set('session', sessionData.session as ISession['session']);
      c.set('isAuthenticated', true);
    } else {
      // Set null values for unauthenticated requests
      c.set('user', null);
      c.set('session', null);
      c.set('isAuthenticated', false);
    }
  } catch (error) {
    console.error('Auth middleware error:', error);

    // Set null values on error
    c.set('user', null);
    c.set('session', null);
    c.set('isAuthenticated', false);
  }

  await next();
};

/**
 * Require authentication middleware
 *
 * Ensures the user is authenticated before proceeding.
 * Returns 401 Unauthorized if no valid session is found.
 *
 * @param c - Hono context
 * @param next - Next middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/protected/*', requireAuth);
 * ```
 */
export const requireAuth = async (c: Context<IAuthHonoEnv>, next: Next) => {
  const isAuthenticated = c.get('isAuthenticated');

  if (!isAuthenticated) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  await next();
};

/**
 * Optional authentication middleware
 *
 * Extracts session information but doesn't require authentication.
 * Useful for endpoints that work for both authenticated and anonymous users.
 *
 * @param c - Hono context
 * @param next - Next middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/public/*', optionalAuth);
 *
 * app.get('/api/public/data', (c) => {
 *   const user = c.get('user');
 *   const isAuthenticated = c.get('isAuthenticated');
 *
 *   if (isAuthenticated) {
 *     // Return personalized data
 *     return c.json({ data: getPersonalizedData(user.id) });
 *   } else {
 *     // Return public data
 *     return c.json({ data: getPublicData() });
 *   }
 * });
 * ```
 */
export const optionalAuth = authMiddleware;

/**
 * Role-based authorization middleware factory
 *
 * Creates middleware that checks if the authenticated user has required roles.
 *
 * @param requiredRoles - Array of required roles
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/admin/*', requireRoles(['admin']));
 * app.use('/api/moderator/*', requireRoles(['admin', 'moderator']));
 * ```
 */
export const requireRoles = (requiredRoles: string[]) => {
  return async (c: Context<IAuthHonoEnv>, next: Next) => {
    const user = c.get('user');
    const isAuthenticated = c.get('isAuthenticated');

    if (!isAuthenticated || !user) {
      return c.json(
        {
          status: 'error',
          error: 'Authentication required',
          error_type: 'authentication',
          timestamp: new Date().toISOString(),
        },
        401
      );
    }

    const userRole = user.role || 'user';

    if (!requiredRoles.includes(userRole)) {
      return c.json(
        {
          status: 'error',
          error: 'Insufficient permissions',
          error_type: 'authorization',
          required_roles: requiredRoles,
          user_role: userRole,
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    await next();
  };
};

/**
 * User ownership middleware factory
 *
 * Creates middleware that checks if the authenticated user owns the resource.
 * Extracts user ID from request parameters and compares with authenticated user.
 *
 * @param userIdParam - Parameter name containing the user ID (default: 'userId')
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/users/:userId/*', requireOwnership());
 * app.use('/api/profiles/:profileUserId/*', requireOwnership('profileUserId'));
 * ```
 */
export const requireOwnership = (userIdParam = 'userId') => {
  return async (c: Context<IAuthHonoEnv>, next: Next) => {
    const user = c.get('user');
    const isAuthenticated = c.get('isAuthenticated');

    if (!isAuthenticated || !user) {
      return c.json(
        {
          status: 'error',
          error: 'Authentication required',
          error_type: 'authentication',
          timestamp: new Date().toISOString(),
        },
        401
      );
    }

    const resourceUserId = c.req.param(userIdParam);

    if (!resourceUserId) {
      return c.json(
        {
          status: 'error',
          error: `Missing ${userIdParam} parameter`,
          error_type: 'validation',
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    if (user.id !== resourceUserId) {
      // Allow admin users to access any resource
      const userRole = user.role || 'user';
      if (userRole !== 'admin') {
        return c.json(
          {
            status: 'error',
            error: 'Access denied: You can only access your own resources',
            error_type: 'authorization',
            timestamp: new Date().toISOString(),
          },
          403
        );
      }
    }

    await next();
  };
};
