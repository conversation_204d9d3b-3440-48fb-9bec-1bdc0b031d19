/**
 * Billing Middleware
 *
 * Middleware for billing operations including plan enforcement,
 * usage limits, payment validation, and webhook security.
 */

import type { TBillingSubscription } from '@dbSchema';

// Lazy imports to avoid global scope async operations
// import { billingService } from '@services/billingService';
// import { paddleService } from '@services/paddleService';
// import { paypalService } from '@services/paypalService';
import type { Context, Next } from 'hono';
import type { IUsageData, TBillingProvider } from '@/types/billing';
import type { IApiKeyHonoEnv } from './api-key-middleware';

// ============================================================================
// Billing Environment Types
// ============================================================================

/**
 * Extended Hono environment with billing context
 */
export interface IBillingHonoEnv extends IApiKeyHonoEnv {
  Variables: IApiKeyHonoEnv['Variables'] & {
    team: { id: string; name: string } | null;
    subscription: TBillingSubscription | null;
    usage: IUsageData | null;
    webhookBody: string | null;
  };
}

// ============================================================================
// Plan Enforcement Middleware
// ============================================================================

/**
 * Middleware to track and enforce API request limits
 *
 * Records API request usage and enforces plan-based API request limits.
 * This middleware should be applied to all API endpoints that count towards
 * the API request quota.
 *
 * @returns Middleware function
 */
export function enforceApiRequestLimits() {
  return async (c: Context<IBillingHonoEnv>, next: Next) => {
    const teamContext = c.get('teamContext');
    const user = c.get('user');

    if (!teamContext) {
      return c.json(
        {
          status: 'error',
          error: 'Team context required',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    try {
      // Check current API request limits before processing
      const { billingService } = await import('@services/billingService');
      const limitsCheck = await billingService.checkPlanLimits(teamContext.teamId);

      if (!limitsCheck.withinLimits && limitsCheck.exceededLimits.includes('api_requests')) {
        // Provide detailed information about available options
        const additionalRequests = limitsCheck.currentUsage.additionalApiRequests;
        const totalAvailable =
          limitsCheck.planLimits.maxApiRequests + (additionalRequests?.remaining || 0);

        return c.json(
          {
            status: 'error',
            error: 'API request limit exceeded',
            error_type: 'plan_limit_exceeded',
            details: {
              limitType: 'api_requests',
              currentUsage: limitsCheck.currentUsage.metrics.apiRequests,
              planLimit: limitsCheck.planLimits.maxApiRequests,
              additionalRequestsRemaining: additionalRequests?.remaining || 0,
              totalAvailable,
              resetDate: limitsCheck.currentUsage.period.end,
              purchaseUrl: `/api/billing/teams/${teamContext.teamId}/api-requests`,
              message: 'You can purchase additional API requests to continue using the service.',
            },
            timestamp: new Date().toISOString(),
          },
          402 // Payment Required
        );
      }

      // Record API request usage
      const endpoint = c.req.path;
      const ipAddress =
        c.req.header('cf-connecting-ip') || c.req.header('x-forwarded-for') || undefined;
      const userAgent = c.req.header('user-agent') || undefined;
      const authMethod = c.get('authMethod');
      const apiKey = c.get('apiKey');

      // Record general API request usage for billing
      const { billingService: billingServiceForUsage } = await import('@services/billingService');
      await billingServiceForUsage.recordApiRequestUsage(
        teamContext.teamId,
        endpoint,
        user?.id,
        ipAddress,
        userAgent
      );

      // If authenticated via API key, also record API key specific usage
      if (authMethod === 'api_key' && apiKey) {
        const { incrementApiKeyUsage } = await import('@services/apiKeyService');
        const { logApiKeyUsage } = await import('@services/auditService');

        // Increment API key usage count
        await incrementApiKeyUsage(apiKey.id);

        // Log API key usage for audit trail
        await logApiKeyUsage(
          apiKey.id,
          user?.id || 'unknown',
          teamContext.teamId,
          endpoint,
          ipAddress,
          userAgent
        );
      }

      await next();
    } catch (error) {
      console.error('API request limits check failed:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to check API request limits',
          error_type: 'billing_error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  };
}

/**
 * Middleware to enforce team plan limits
 *
 * Checks if the team has exceeded their plan limits and blocks
 * requests that would exceed those limits.
 *
 * @param limitType - Type of limit to check
 * @returns Middleware function
 */
export function enforcePlanLimits(
  limitType: 'api_requests' | 'search_requests' | 'documents' | 'storage'
) {
  return async (c: Context<IBillingHonoEnv>, next: Next) => {
    const teamContext = c.get('teamContext');

    if (!teamContext) {
      return c.json(
        {
          status: 'error',
          error: 'Team context required',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    try {
      // Check plan limits
      const { billingService } = await import('@services/billingService');
      const limitsCheck = await billingService.checkPlanLimits(teamContext.teamId);

      if (!limitsCheck.withinLimits) {
        // Check if the specific limit type is exceeded
        const isLimitExceeded = limitsCheck.exceededLimits.includes(limitType);

        if (isLimitExceeded) {
          return c.json(
            {
              status: 'error',
              error: 'Plan limit exceeded',
              error_type: 'plan_limit_exceeded',
              details: {
                limitType,
                currentUsage: limitsCheck.currentUsage.metrics,
                planLimits: limitsCheck.planLimits,
                exceededLimits: limitsCheck.exceededLimits,
              },
              timestamp: new Date().toISOString(),
            },
            402 // Payment Required
          );
        }
      }

      await next();
    } catch (error) {
      console.error('Plan limits check failed:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to check plan limits',
          error_type: 'billing_error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  };
}

/**
 * Middleware to require active subscription
 *
 * Ensures the team has an active subscription for paid features.
 */
export async function requireActiveSubscription(c: Context<IBillingHonoEnv>, next: Next) {
  const teamContext = c.get('teamContext');

  if (!teamContext) {
    return c.json(
      {
        status: 'error',
        error: 'Team context required',
        error_type: 'authorization',
        timestamp: new Date().toISOString(),
      },
      400
    );
  }

  try {
    const { billingService } = await import('@services/billingService');
    const subscription = await billingService.getActiveSubscription(teamContext.teamId);

    if (!subscription || subscription.status !== 'active') {
      return c.json(
        {
          status: 'error',
          error: 'Active subscription required',
          error_type: 'subscription_required',
          details: {
            subscriptionStatus: subscription?.status || 'none',
            upgradeUrl: '/billing/upgrade',
          },
          timestamp: new Date().toISOString(),
        },
        402 // Payment Required
      );
    }

    // Add subscription to context
    c.set('subscription', subscription);
    await next();
  } catch (error) {
    console.error('Subscription check failed:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to check subscription status',
        error_type: 'billing_error',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
}

// ============================================================================
// Webhook Security Middleware
// ============================================================================

/**
 * Middleware to verify webhook signatures
 *
 * Validates webhook signatures from billing providers to ensure
 * authenticity and prevent tampering.
 *
 * @param provider - Billing provider (paddle or paypal)
 * @returns Middleware function
 */
export function verifyWebhookSignature(provider: TBillingProvider) {
  return async (c: Context, next: Next) => {
    try {
      const body = await c.req.text();
      const headers = c.req.header();

      let isValid = false;

      if (provider === 'paddle') {
        const signature = headers['paddle-signature'] || '';
        const { paddleService } = await import('@services/paddleService');
        isValid = await paddleService.verifyWebhookSignature(body, signature);
      } else if (provider === 'paypal') {
        const { paypalService } = await import('@services/paypalService');
        isValid = await paypalService.verifyWebhookSignature(body, headers);
      }

      if (!isValid) {
        console.error(`Invalid ${provider} webhook signature`);
        return c.json(
          {
            status: 'error',
            error: 'Invalid webhook signature',
            error_type: 'webhook_verification_failed',
            timestamp: new Date().toISOString(),
          },
          401
        );
      }

      // Store the raw body for webhook processing
      c.set('webhookBody', body);
      await next();
    } catch (error) {
      console.error(`Webhook signature verification failed for ${provider}:`, error);
      return c.json(
        {
          status: 'error',
          error: 'Webhook verification failed',
          error_type: 'webhook_verification_failed',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  };
}

// ============================================================================
// Payment Validation Middleware
// ============================================================================

/**
 * Middleware to validate payment amounts and currencies
 *
 * Ensures payment amounts are valid and within acceptable ranges.
 */
export async function validatePaymentAmount(c: Context, next: Next) {
  try {
    const body = await c.req.json();
    const { amount, currency } = body;

    // Validate amount
    if (typeof amount !== 'number' || amount <= 0) {
      return c.json(
        {
          status: 'error',
          error: 'Invalid payment amount',
          error_type: 'validation_error',
          details: {
            field: 'amount',
            message: 'Amount must be a positive number',
          },
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    // Check minimum amount (e.g., $0.50)
    if (amount < 50) {
      return c.json(
        {
          status: 'error',
          error: 'Payment amount too small',
          error_type: 'validation_error',
          details: {
            field: 'amount',
            message: 'Minimum payment amount is $0.50',
            minimumAmount: 50,
          },
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    // Check maximum amount (e.g., $10,000)
    if (amount > 1000000) {
      return c.json(
        {
          status: 'error',
          error: 'Payment amount too large',
          error_type: 'validation_error',
          details: {
            field: 'amount',
            message: 'Maximum payment amount is $10,000',
            maximumAmount: 1000000,
          },
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    // Validate currency
    const supportedCurrencies = ['USD', 'EUR', 'GBP'];
    if (!currency || !supportedCurrencies.includes(currency.toUpperCase())) {
      return c.json(
        {
          status: 'error',
          error: 'Unsupported currency',
          error_type: 'validation_error',
          details: {
            field: 'currency',
            message: 'Currency must be one of: USD, EUR, GBP',
            supportedCurrencies,
          },
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    await next();
  } catch (error) {
    console.error('Payment validation failed:', error);
    return c.json(
      {
        status: 'error',
        error: 'Payment validation failed',
        error_type: 'validation_error',
        timestamp: new Date().toISOString(),
      },
      400
    );
  }
}

// ============================================================================
// Rate Limiting for Billing Operations
// ============================================================================

/**
 * Rate limiting for billing operations
 *
 * Prevents abuse of billing endpoints with stricter limits
 * than regular API endpoints.
 */
export async function billingRateLimit(c: Context, next: Next) {
  // Get client IP for rate limiting
  const clientIP = c.req.header('cf-connecting-ip') || c.req.header('x-forwarded-for') || 'unknown';

  // Simple in-memory rate limiting (in production, use Redis or similar)
  const _rateLimitKey = `billing_rate_limit:${clientIP}`;

  // For now, just log and continue
  // In production, implement proper rate limiting
  console.log(`Billing request from IP: ${clientIP}`);

  await next();
}

// ============================================================================
// Billing Context Middleware
// ============================================================================

/**
 * Middleware to add billing context to requests
 *
 * Adds subscription and usage information to the request context
 * for billing-related operations.
 */
export async function addBillingContext(c: Context<IBillingHonoEnv>, next: Next) {
  const teamContext = c.get('teamContext');

  if (teamContext) {
    try {
      // Get subscription and usage data
      const { billingService } = await import('@services/billingService');
      const [subscription, usage] = await Promise.all([
        billingService.getActiveSubscription(teamContext.teamId),
        billingService.getUsageData(teamContext.teamId),
      ]);

      // Add to context
      c.set('subscription', subscription);
      c.set('usage', usage);
    } catch (error) {
      console.error('Failed to load billing context:', error);
      // Continue without billing context
    }
  }

  await next();
}

// ============================================================================
// Feature Flag Middleware
// ============================================================================

/**
 * Middleware to check feature availability based on plan
 *
 * @param feature - Feature to check
 * @returns Middleware function
 */
export function requireFeature(
  feature:
    | 'advancedAnalytics'
    | 'prioritySupport'
    | 'customIntegrations'
    | 'ssoEnabled'
    | 'auditLogs'
) {
  return async (c: Context<IBillingHonoEnv>, next: Next) => {
    const teamContext = c.get('teamContext');

    if (!teamContext) {
      return c.json(
        {
          status: 'error',
          error: 'Team context required',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        400
      );
    }

    try {
      const { billingService } = await import('@services/billingService');
      const subscription = await billingService.getActiveSubscription(teamContext.teamId);
      const planId = subscription?.planId || 'free';
      const planConfig = billingService.getAvailablePlans().find((p) => p.id === planId);

      if (!planConfig || !planConfig.limits[feature]) {
        return c.json(
          {
            status: 'error',
            error: 'Feature not available in current plan',
            error_type: 'feature_not_available',
            details: {
              feature,
              currentPlan: planId,
              upgradeUrl: '/billing/upgrade',
            },
            timestamp: new Date().toISOString(),
          },
          402 // Payment Required
        );
      }

      await next();
    } catch (error) {
      console.error('Feature check failed:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to check feature availability',
          error_type: 'billing_error',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  };
}
