/**
 * API Key Authentication Middleware
 *
 * Middleware for validating API keys from Authorization headers.
 * Supports both session-based and API key authentication.
 */

import type { TApiKey } from '@dbSchema';
// Lazy import to avoid global scope async operations
// import * as schema from '@dbSchema';
// Lazy imports to avoid global scope async operations
// import { getApiKeyByHash, incrementApiKeyUsage } from '@services/apiKeyService';
import getDB from '@utils/getDB';
import { createTeamContext } from '@utils/permissions';
import { and, eq } from 'drizzle-orm';
import type { Context, Next } from 'hono';
import type { ITeamContext, TTeamRole } from '@/types/teams';
import type { IAuthHonoEnv } from './auth-middleware';

/**
 * Extended Hono environment with API key context
 */
export interface IApiKeyHonoEnv extends IAuthHonoEnv {
  Variables: IAuthHonoEnv['Variables'] & {
    apiKey: TApiKey | null;
    authMethod: 'session' | 'api_key' | null;
    teamContext: ITeamContext | null;
  };
}

/**
 * Hash API key using Web Crypto API (Cloudflare Workers compatible)
 *
 * @param key - Raw API key
 * @returns Hashed API key
 */
async function hashApiKey(key: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(key);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  return Array.from(new Uint8Array(hashBuffer))
    .map((b) => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Extract API key from Authorization header
 *
 * @param authHeader - Authorization header value
 * @returns API key or null if not found/invalid format
 */
function extractApiKey(authHeader: string | undefined): string | null {
  if (!authHeader) return null;

  // Check for Bearer token format
  const bearerMatch = authHeader.match(/^Bearer\s+(.+)$/i);
  if (!bearerMatch) return null;

  const token = bearerMatch[1];

  // Validate API key format (should start with 'ezc_')
  if (!token.startsWith('ezc_')) return null;

  return token;
}

/**
 * API Key authentication middleware
 *
 * Validates API keys from Authorization headers and sets API key context.
 * Works alongside session authentication - either method can authenticate the request.
 *
 * @param c - Hono context
 * @param next - Next middleware function
 *
 * @example
 * ```typescript
 * app.use('*', authMiddleware); // Session auth
 * app.use('*', apiKeyMiddleware); // API key auth
 * app.use('/api/protected/*', requireAuth); // Require either auth method
 * ```
 */
export const apiKeyMiddleware = async (c: Context<IApiKeyHonoEnv>, next: Next) => {
  try {
    // Dynamic import to avoid global scope async operations
    const schema = await import('@dbSchema');

    // Initialize API key context
    c.set('apiKey', null);
    c.set('authMethod', null);

    // Check if already authenticated via session
    const isSessionAuthenticated = c.get('isAuthenticated');
    if (isSessionAuthenticated) {
      c.set('authMethod', 'session');
      await next();
      return;
    }

    // Try API key authentication
    const authHeader = c.req.header('Authorization');
    const apiKey = extractApiKey(authHeader);

    if (!apiKey) {
      // No API key provided, continue without authentication
      await next();
      return;
    }

    // Hash the API key for database lookup
    const keyHash = await hashApiKey(apiKey);

    // Validate API key
    const { getApiKeyByHash } = await import('@services/apiKeyService');
    const apiKeyRecord = await getApiKeyByHash(keyHash);

    if (!apiKeyRecord) {
      // Invalid API key - set error context but don't block request
      // Let requireAuth middleware handle the rejection
      await next();
      return;
    }

    // Load user data from API key
    const db = await getDB();
    const user = await db.query.users.findFirst({
      where: eq(schema.users.id, apiKeyRecord.userId),
    });

    if (!user) {
      // API key has invalid user reference
      await next();
      return;
    }

    // Convert database user to IUser format for consistency with session auth
    const userForContext = {
      id: user.id,
      name: user.name,
      email: user.email,
      emailVerified: user.emailVerified,
      image: user.image || undefined, // Convert null to undefined for IUser compatibility
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    // Set API key and user context
    c.set('apiKey', apiKeyRecord);
    c.set('authMethod', 'api_key');
    c.set('user', userForContext);
    c.set('session', null); // No session for API key auth
    c.set('isAuthenticated', true);

    // If API key is associated with a team, set team context for billing
    if (apiKeyRecord.teamId) {
      try {
        const team = await db.query.teams.findFirst({
          where: eq(schema.teams.id, apiKeyRecord.teamId),
        });

        if (team) {
          // Get team member info for the user
          const teamMember = await db.query.teamMembers.findFirst({
            where: and(
              eq(schema.teamMembers.teamId, team.id),
              eq(schema.teamMembers.userId, user.id)
            ),
          });

          if (teamMember) {
            // Parse custom permissions
            const customPermissions = teamMember.permissions
              ? JSON.parse(teamMember.permissions)
              : [];

            // Create team context using the utility function
            const teamContext = createTeamContext(
              team.id,
              user.id,
              teamMember.role as TTeamRole,
              customPermissions
            );

            // Set team context
            c.set('teamContext', teamContext);
          }
        }
      } catch (error) {
        console.error('Failed to load team context for API key:', error);
        // Continue without team context - billing middleware will handle missing context
      }
    }

    await next();

    // Increment API key usage after successful request
    if (c.res.status < 400) {
      try {
        const { incrementApiKeyUsage } = await import('@services/apiKeyService');
        await incrementApiKeyUsage(apiKeyRecord.id);
      } catch (error) {
        console.error('Failed to increment API key usage:', error);
        // Don't fail the request if usage tracking fails
      }
    }
  } catch (error) {
    console.error('API key middleware error:', error);

    // Set null values on error
    c.set('apiKey', null);
    c.set('authMethod', null);

    await next();
  }
};

/**
 * Require API key authentication middleware
 *
 * Ensures the request is authenticated with a valid API key.
 * Returns 401 Unauthorized if no valid API key is found.
 *
 * @param c - Hono context
 * @param next - Next middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/api-only/*', requireApiKey);
 * ```
 */
export const requireApiKey = async (c: Context<IApiKeyHonoEnv>, next: Next) => {
  const apiKey = c.get('apiKey');
  const authMethod = c.get('authMethod');

  if (authMethod !== 'api_key' || !apiKey) {
    return c.json(
      {
        status: 'error',
        error: 'API key authentication required',
        error_type: 'authentication',
        details: {
          message: 'Valid API key must be provided in Authorization header',
          format: 'Authorization: Bearer <api_key>',
        },
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  await next();
};

/**
 * Dual authentication middleware
 *
 * Accepts either session authentication OR API key authentication.
 * This is the recommended middleware for most protected routes.
 *
 * @param c - Hono context
 * @param next - Next middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/protected/*', requireDualAuth);
 * ```
 */
export const requireDualAuth = async (c: Context<IApiKeyHonoEnv>, next: Next) => {
  const isAuthenticated = c.get('isAuthenticated');
  const authMethod = c.get('authMethod');

  if (!isAuthenticated || !authMethod) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        details: {
          message: 'Valid session or API key required',
          session_auth: 'Use session cookies from /api/auth/sign-in',
          api_key_auth: 'Use Authorization: Bearer <api_key> header',
        },
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  await next();
};
