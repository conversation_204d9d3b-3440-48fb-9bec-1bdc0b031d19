/**
 * Production Logger Middleware
 *
 * Enhanced logging middleware for production monitoring and debugging.
 */

import type { HonoEnv } from '@types';
import type { Context, Next } from 'hono';

interface LogEntry {
  timestamp: string;
  method: string;
  path: string;
  status: number;
  duration: number;
  userAgent?: string;
  ip?: string;
  requestId?: string;
  error?: string;
}

export const productionLogger = async (c: Context<HonoEnv>, next: Next) => {
  const start = Date.now();
  const requestId = c.get('requestId') || crypto.randomUUID();

  // Set request ID for tracing
  c.set('requestId', requestId);

  const logEntry: Partial<LogEntry> = {
    timestamp: new Date().toISOString(),
    method: c.req.method,
    path: c.req.path,
    requestId,
    userAgent: c.req.header('User-Agent'),
    ip: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown',
  };

  try {
    await next();

    const duration = Date.now() - start;
    const status = c.res.status;

    const completeLogEntry: LogEntry = {
      ...(logEntry as LogEntry),
      status,
      duration,
    };

    // Log based on status code
    if (status >= 500) {
      console.error('Server Error:', JSON.stringify(completeLogEntry));
    } else if (status >= 400) {
      console.warn('Client Error:', JSON.stringify(completeLogEntry));
    } else {
      console.log('Request:', JSON.stringify(completeLogEntry));
    }
  } catch (error) {
    const duration = Date.now() - start;
    const errorLogEntry: LogEntry = {
      ...(logEntry as LogEntry),
      status: 500,
      duration,
      error: error instanceof Error ? error.message : String(error),
    };

    console.error('Request Error:', JSON.stringify(errorLogEntry));
    throw error; // Re-throw to be handled by error middleware
  }
};

export const requestIdMiddleware = async (c: Context<HonoEnv>, next: Next) => {
  // Generate or use existing request ID
  const requestId = c.req.header('X-Request-ID') || crypto.randomUUID();
  c.set('requestId', requestId);
  c.res.headers.set('X-Request-ID', requestId);
  await next();
};

export const securityHeaders = async (c: Context<HonoEnv>, next: Next) => {
  await next();

  // Production-ready security headers
  c.res.headers.set('X-Content-Type-Options', 'nosniff');
  c.res.headers.set('X-Frame-Options', 'DENY');
  c.res.headers.set('X-XSS-Protection', '1; mode=block');
  c.res.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Content Security Policy for enhanced XSS protection
  c.res.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: https:; " +
      "font-src 'self'; " +
      "connect-src 'self'; " +
      "frame-ancestors 'none'; " +
      "base-uri 'self'; " +
      "form-action 'self'"
  );

  // Strict Transport Security (HTTPS enforcement)
  c.res.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');

  // Permissions Policy (formerly Feature Policy)
  c.res.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()'
  );

  // Cross-Origin policies
  c.res.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');
  c.res.headers.set('Cross-Origin-Opener-Policy', 'same-origin');
  c.res.headers.set('Cross-Origin-Resource-Policy', 'same-origin');

  // Cache control for sensitive endpoints
  if (c.req.path.includes('/api/')) {
    c.res.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    c.res.headers.set('Pragma', 'no-cache');
    c.res.headers.set('Expires', '0');
  }

  // Remove sensitive headers that might leak information
  c.res.headers.delete('Server');
  c.res.headers.delete('X-Powered-By');
  c.res.headers.delete('Via');
  c.res.headers.delete('X-AspNet-Version');
  c.res.headers.delete('X-AspNetMvc-Version');
};
