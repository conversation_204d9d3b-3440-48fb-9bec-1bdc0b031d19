/**
 * RBAC Middleware
 *
 * Role-Based Access Control middleware for team-based permissions.
 * Integrates with existing authentication middleware and team management system.
 */

import * as schema from '@dbSchema';
// Lazy imports to avoid global scope async operations
// import { permissionService } from '@services/permissionService';
import getDB from '@utils/getDB';
import {
  createTeamContext,
  hasAllPermissions,
  hasAnyPermission,
  hasMinimumRole,
  hasPermission,
} from '@utils/permissions';
import { and, eq, isNull } from 'drizzle-orm';
import type { Context, Next } from 'hono';
import type {
  IEnhancedPermissionCheckResult,
  IPermissionCheckOptions,
  IPermissionMiddlewareConfig,
  IResourcePermissionContext,
} from '@/types/permissions';
import type { ITeamContext, TPermission, TTeamRole } from '@/types/teams';
import type { IAuthHonoEnv } from './auth-middleware';

/**
 * Extended Hono environment with team context
 */
export interface IRBACHonoEnv extends IAuthHonoEnv {
  Variables: IAuthHonoEnv['Variables'] & {
    teamContext: ITeamContext | null;
    teamId: string | null;
    teamRole: TTeamRole | null;
    teamPermissions: TPermission[];
    permissionContext: IResourcePermissionContext | null;
    permissionResult: IEnhancedPermissionCheckResult | null;
  };
}

/**
 * Team context middleware
 *
 * Extracts team context from request parameters and sets team information
 * in Hono context variables. Must be used after authMiddleware.
 *
 * @param teamIdParam - Parameter name containing the team ID (default: 'teamId')
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/teams/:teamId/*', teamContextMiddleware());
 * app.use('/api/projects/:projectId/teams/:teamId/*', teamContextMiddleware());
 * ```
 */
export const teamContextMiddleware = (teamIdParam = 'teamId') => {
  return async (c: Context<IRBACHonoEnv>, next: Next) => {
    const user = c.get('user');
    const isAuthenticated = c.get('isAuthenticated');

    // Check if team context already exists (e.g., from API key middleware)
    const existingTeamContext = c.get('teamContext');

    // Initialize team context variables only if not already set
    if (!existingTeamContext) {
      c.set('teamContext', null);
      c.set('teamId', null);
      c.set('teamRole', null);
      c.set('teamPermissions', []);
    }

    if (!isAuthenticated || !user) {
      await next();
      return;
    }

    const teamId = c.req.param(teamIdParam);
    if (!teamId) {
      // If no team ID parameter and we already have team context, preserve it
      if (existingTeamContext) {
        c.set('teamId', existingTeamContext.teamId);
        c.set('teamRole', existingTeamContext.role);
        c.set('teamPermissions', existingTeamContext.permissions);
      }
      await next();
      return;
    }

    try {
      const db = await getDB();

      // Get team member information
      const teamMember = await db.query.teamMembers.findFirst({
        where: and(
          eq(schema.teamMembers.teamId, teamId),
          eq(schema.teamMembers.userId, user.id),
          isNull(schema.teamMembers.deletedAt)
        ),
        with: {
          team: true,
        },
      });

      if (teamMember?.team && !teamMember.team.deletedAt) {
        // Parse custom permissions
        const customPermissions: TPermission[] = teamMember.permissions
          ? JSON.parse(teamMember.permissions)
          : [];

        // Create team context
        const teamContext = createTeamContext(
          teamId,
          user.id,
          teamMember.role as TTeamRole,
          customPermissions
        );

        // Set context variables
        c.set('teamContext', teamContext);
        c.set('teamId', teamId);
        c.set('teamRole', teamMember.role as TTeamRole);
        c.set('teamPermissions', teamContext.permissions);
      }
    } catch (error) {
      console.error('Team context middleware error:', error);
      // Continue with null context on error
    }

    await next();
  };
};

/**
 * Require team membership middleware
 *
 * Ensures the user is a member of the specified team.
 * Must be used after teamContextMiddleware.
 *
 * @param c - Hono context
 * @param next - Next middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/teams/:teamId/*', teamContextMiddleware(), requireTeamMembership);
 * ```
 */
export const requireTeamMembership = async (c: Context<IRBACHonoEnv>, next: Next) => {
  const isAuthenticated = c.get('isAuthenticated');
  const teamContext = c.get('teamContext');

  if (!isAuthenticated) {
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'authentication',
        timestamp: new Date().toISOString(),
      },
      401
    );
  }

  if (!teamContext) {
    return c.json(
      {
        status: 'error',
        error: 'Team membership required',
        error_type: 'authorization',
        timestamp: new Date().toISOString(),
      },
      403
    );
  }

  await next();
};

/**
 * Require team permission middleware factory
 *
 * Creates middleware that checks if the user has a specific permission in the team.
 * Must be used after teamContextMiddleware.
 *
 * @param requiredPermission - Required permission
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/teams/:teamId/settings', requireTeamPermission('manage.team'));
 * app.use('/api/teams/:teamId/projects', requireTeamPermission('read.project'));
 * ```
 */
export const requireTeamPermission = (requiredPermission: TPermission) => {
  return async (c: Context<IRBACHonoEnv>, next: Next) => {
    const teamContext = c.get('teamContext');

    if (!teamContext) {
      return c.json(
        {
          status: 'error',
          error: 'Team membership required',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    const permissionCheck = hasPermission(teamContext, requiredPermission);

    if (!permissionCheck.allowed) {
      return c.json(
        {
          status: 'error',
          error: 'Insufficient permissions',
          error_type: 'authorization',
          required_permission: requiredPermission,
          reason: permissionCheck.reason,
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    await next();
  };
};

/**
 * Require any team permission middleware factory
 *
 * Creates middleware that checks if the user has any of the specified permissions.
 * Must be used after teamContextMiddleware.
 *
 * @param requiredPermissions - Array of required permissions (OR logic)
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/teams/:teamId/data', requireAnyTeamPermission(['read.project', 'read.vectors']));
 * ```
 */
export const requireAnyTeamPermission = (requiredPermissions: TPermission[]) => {
  return async (c: Context<IRBACHonoEnv>, next: Next) => {
    const teamContext = c.get('teamContext');

    if (!teamContext) {
      return c.json(
        {
          status: 'error',
          error: 'Team membership required',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    const permissionCheck = hasAnyPermission(teamContext, requiredPermissions);

    if (!permissionCheck.allowed) {
      return c.json(
        {
          status: 'error',
          error: 'Insufficient permissions',
          error_type: 'authorization',
          required_permissions: requiredPermissions,
          reason: permissionCheck.reason,
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    await next();
  };
};

/**
 * Require all team permissions middleware factory
 *
 * Creates middleware that checks if the user has all of the specified permissions.
 * Must be used after teamContextMiddleware.
 *
 * @param requiredPermissions - Array of required permissions (AND logic)
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/teams/:teamId/admin', requireAllTeamPermissions(['manage.team', 'manage.billing']));
 * ```
 */
export const requireAllTeamPermissions = (requiredPermissions: TPermission[]) => {
  return async (c: Context<IRBACHonoEnv>, next: Next) => {
    const teamContext = c.get('teamContext');

    if (!teamContext) {
      return c.json(
        {
          status: 'error',
          error: 'Team membership required',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    const permissionCheck = hasAllPermissions(teamContext, requiredPermissions);

    if (!permissionCheck.allowed) {
      return c.json(
        {
          status: 'error',
          error: 'Insufficient permissions',
          error_type: 'authorization',
          required_permissions: requiredPermissions,
          reason: permissionCheck.reason,
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    await next();
  };
};

/**
 * Require minimum team role middleware factory
 *
 * Creates middleware that checks if the user has a minimum role level in the team.
 * Must be used after teamContextMiddleware.
 *
 * @param minimumRole - Minimum required role
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/teams/:teamId/members', requireMinimumTeamRole('manager'));
 * app.use('/api/teams/:teamId/billing', requireMinimumTeamRole('admin'));
 * ```
 */
export const requireMinimumTeamRole = (minimumRole: TTeamRole) => {
  return async (c: Context<IRBACHonoEnv>, next: Next) => {
    const teamRole = c.get('teamRole');

    if (!teamRole) {
      return c.json(
        {
          status: 'error',
          error: 'Team membership required',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    if (!hasMinimumRole(teamRole, minimumRole)) {
      return c.json(
        {
          status: 'error',
          error: 'Insufficient role level',
          error_type: 'authorization',
          required_role: minimumRole,
          user_role: teamRole,
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    await next();
  };
};

// ============================================================================
// Enhanced Permission Middleware
// ============================================================================

/**
 * Enhanced permission checking middleware factory
 *
 * Creates middleware that uses the comprehensive permission service for validation.
 * Supports resource-level access control and detailed audit logging.
 *
 * @param config - Permission middleware configuration
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/teams/:teamId/projects', enhancedPermissionCheck({
 *   requiredPermissions: ['read.project'],
 *   resourceType: 'project',
 *   resourceIdParam: 'projectId'
 * }));
 * ```
 */
export const enhancedPermissionCheck = (config: IPermissionMiddlewareConfig) => {
  return async (c: Context<IRBACHonoEnv>, next: Next) => {
    // Lazy import to avoid global scope async operations
    const { permissionService } = await import('@services/permissionService');

    const user = c.get('user');
    const isAuthenticated = c.get('isAuthenticated');
    const teamId = c.get('teamId');

    if (!isAuthenticated || !user) {
      return c.json(
        {
          status: 'error',
          error: 'Authentication required',
          error_type: 'authentication',
          timestamp: new Date().toISOString(),
        },
        401
      );
    }

    // Build resource context
    const context: IResourcePermissionContext = {
      userId: user.id,
      teamId: teamId || undefined,
      resourceType:
        (config.resourceType as IResourcePermissionContext['resourceType']) || 'unknown',
      resourceId: config.resourceIdParam ? c.req.param(config.resourceIdParam) : undefined,
    };

    // Build permission check options
    const options: IPermissionCheckOptions = {
      allowInheritance: config.allowInheritance !== false,
      context,
    };

    let permissionResult: IEnhancedPermissionCheckResult;

    if (config.requiredPermissions && config.requiredPermissions.length > 0) {
      if (config.requireAll) {
        // Require ALL permissions (AND logic)
        permissionResult = await permissionService.hasAllPermissions(
          user.id,
          config.requiredPermissions,
          context,
          options
        );
      } else if (config.requireAny !== false) {
        // Require ANY permission (OR logic) - default behavior
        permissionResult = await permissionService.hasAnyPermission(
          user.id,
          config.requiredPermissions,
          context,
          options
        );
      } else {
        // Check first permission only
        permissionResult = await permissionService.hasPermission(
          user.id,
          config.requiredPermissions[0],
          context,
          options
        );
      }
    } else if (config.customCheck) {
      // Use custom permission check function
      permissionResult = await config.customCheck(context, config.requiredPermissions || []);
    } else {
      // No permissions required - allow access
      permissionResult = { allowed: true, checkedPermissions: [] };
    }

    // Log permission check for audit
    if (config.requiredPermissions && config.requiredPermissions.length > 0) {
      await permissionService.logPermissionCheck(
        user.id,
        config.requiredPermissions[0],
        permissionResult,
        context
      );
    }

    if (!permissionResult.allowed) {
      return c.json(
        {
          status: 'error',
          error: 'Insufficient permissions',
          error_type: 'authorization',
          required_permissions: config.requiredPermissions,
          reason: permissionResult.reason,
          resource_type: context.resourceType,
          resource_id: context.resourceId,
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    // Set permission context for downstream middleware
    c.set('permissionContext', context);
    c.set('permissionResult', permissionResult);

    await next();
  };
};

/**
 * Resource-specific permission middleware factory
 *
 * Creates middleware that checks permissions for specific resource types.
 * Automatically extracts resource ID from request parameters.
 *
 * @param resourceType - Type of resource being accessed
 * @param permissions - Required permissions
 * @param options - Additional options
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/projects/:projectId', resourcePermissionCheck('project', ['read.project']));
 * app.use('/api/teams/:teamId/billing', resourcePermissionCheck('billing', ['read.billing'], {
 *   requireAll: true
 * }));
 * ```
 */
export const resourcePermissionCheck = (
  resourceType: string,
  permissions: TPermission[],
  options: Partial<IPermissionMiddlewareConfig> = {}
) => {
  const resourceIdParam = `${resourceType}Id`;

  return enhancedPermissionCheck({
    requiredPermissions: permissions,
    resourceType,
    resourceIdParam,
    requireAny: true,
    allowInheritance: true,
    ...options,
  });
};

/**
 * Admin-only permission middleware
 *
 * Ensures only admin or owner roles can access the route.
 * Combines role checking with permission validation.
 *
 * @param permissions - Additional permissions to check
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.use('/api/teams/:teamId/admin', adminOnlyPermissionCheck(['manage.team']));
 * ```
 */
export const adminOnlyPermissionCheck = (permissions: TPermission[] = []) => {
  return async (c: Context<IRBACHonoEnv>, next: Next) => {
    const teamRole = c.get('teamRole');

    // Check minimum role first
    if (!teamRole || !['admin', 'owner'].includes(teamRole)) {
      return c.json(
        {
          status: 'error',
          error: 'Admin access required',
          error_type: 'authorization',
          required_role: 'admin',
          current_role: teamRole,
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    // If additional permissions are required, check them
    if (permissions.length > 0) {
      return enhancedPermissionCheck({
        requiredPermissions: permissions,
        requireAll: true,
        allowInheritance: true,
      })(c, next);
    }

    await next();
  };
};
