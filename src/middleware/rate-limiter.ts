/**
 * Rate Limiting Middleware
 *
 * Production-ready rate limiting to prevent abuse and ensure fair usage.
 */

import type { HonoEnv } from '@types';
import { getEnv, getKV } from '@utils';
import type { Context, Next } from 'hono';
import { HTTPException } from 'hono/http-exception';

interface RateLimitConfig {
  requestsPerMinute: number;
  windowSizeMs: number;
  keyGenerator: (c: Context<HonoEnv>) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitInfo {
  count: number;
  resetTime: number;
  remaining: number;
}

export class RateLimiter {
  private config: RateLimitConfig;

  constructor(config: Partial<RateLimitConfig> = {}) {
    this.config = {
      requestsPerMinute: config.requestsPerMinute || 60,
      windowSizeMs: config.windowSizeMs || 60000, // 1 minute
      keyGenerator: config.keyGenerator || this.defaultKeyGenerator,
      skipSuccessfulRequests: config.skipSuccessfulRequests || false,
      skipFailedRequests: config.skipFailedRequests || false,
    };
  }

  private defaultKeyGenerator(c: Context<HonoEnv>): string {
    // Use CF-Connecting-IP for Cloudflare, fallback to X-Forwarded-For, then to 'unknown'
    const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';
    return `rate_limit:${ip}`;
  }

  async middleware(c: Context<HonoEnv>, next: Next) {
    const env = await getEnv();

    // Check if rate limiting is enabled
    if (env.RATE_LIMIT_ENABLED === 'false') {
      await next();
      return;
    }

    // Override default config with environment variables
    const requestsPerMinute = parseInt(env.RATE_LIMIT_REQUESTS_PER_MINUTE || '60');
    this.config.requestsPerMinute = requestsPerMinute;

    const key = this.config.keyGenerator(c);
    const now = Date.now();
    const windowStart = now - this.config.windowSizeMs;

    try {
      const kv = await getKV();

      // Get current rate limit info
      const rateLimitInfo = await this.getRateLimitInfo(kv, key, windowStart);

      // Check if limit exceeded
      if (rateLimitInfo.count >= this.config.requestsPerMinute) {
        const resetTimeSeconds = Math.ceil(rateLimitInfo.resetTime / 1000);

        // Set rate limit headers
        c.res.headers.set('X-RateLimit-Limit', this.config.requestsPerMinute.toString());
        c.res.headers.set('X-RateLimit-Remaining', '0');
        c.res.headers.set('X-RateLimit-Reset', resetTimeSeconds.toString());
        c.res.headers.set(
          'Retry-After',
          Math.ceil((rateLimitInfo.resetTime - now) / 1000).toString()
        );

        throw new HTTPException(429, {
          message: 'Too Many Requests. Rate limit exceeded.',
        });
      }

      // Process the request
      await next();

      // Update rate limit counter (unless configured to skip successful requests)
      if (!this.config.skipSuccessfulRequests || c.res.status >= 400) {
        await this.updateRateLimit(kv, key, now);
      }

      // Set rate limit headers for successful requests
      const updatedInfo = await this.getRateLimitInfo(kv, key, windowStart);
      c.res.headers.set('X-RateLimit-Limit', this.config.requestsPerMinute.toString());
      c.res.headers.set(
        'X-RateLimit-Remaining',
        Math.max(0, this.config.requestsPerMinute - updatedInfo.count).toString()
      );
      c.res.headers.set('X-RateLimit-Reset', Math.ceil(updatedInfo.resetTime / 1000).toString());
    } catch (error) {
      // If rate limiting fails, log the error but don't block the request
      if (error instanceof HTTPException) {
        throw error; // Re-throw rate limit exceeded errors
      }

      console.warn('Rate limiting error:', error);
      await next(); // Continue without rate limiting
    }
  }

  private async getRateLimitInfo(
    kv: KVNamespace,
    key: string,
    windowStart: number
  ): Promise<RateLimitInfo> {
    try {
      const data = (await kv.get(key, 'json')) as { requests: number[]; resetTime: number } | null;

      if (!data) {
        return {
          count: 0,
          resetTime: windowStart + this.config.windowSizeMs,
          remaining: this.config.requestsPerMinute,
        };
      }

      // Filter requests within the current window
      const validRequests = data.requests.filter((timestamp) => timestamp > windowStart);

      return {
        count: validRequests.length,
        resetTime: data.resetTime,
        remaining: Math.max(0, this.config.requestsPerMinute - validRequests.length),
      };
    } catch (error) {
      console.warn('Failed to get rate limit info:', error);
      return {
        count: 0,
        resetTime: windowStart + this.config.windowSizeMs,
        remaining: this.config.requestsPerMinute,
      };
    }
  }

  private async updateRateLimit(kv: KVNamespace, key: string, timestamp: number): Promise<void> {
    try {
      const windowStart = timestamp - this.config.windowSizeMs;
      const data = (await kv.get(key, 'json')) as { requests: number[]; resetTime: number } | null;

      let requests: number[] = [];
      let resetTime = timestamp + this.config.windowSizeMs;

      if (data) {
        // Filter out old requests and add the new one
        requests = data.requests.filter((ts) => ts > windowStart);
        resetTime = data.resetTime;
      }

      requests.push(timestamp);

      // Store with TTL slightly longer than window to handle clock skew
      const ttlSeconds = Math.ceil(this.config.windowSizeMs / 1000) + 60;

      await kv.put(
        key,
        JSON.stringify({
          requests,
          resetTime,
        }),
        {
          expirationTtl: ttlSeconds,
        }
      );
    } catch (error) {
      console.warn('Failed to update rate limit:', error);
    }
  }
}

// Pre-configured rate limiters for different endpoints
export const createApiRateLimiter = () =>
  new RateLimiter({
    requestsPerMinute: 60,
    keyGenerator: (c) => {
      const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';
      return `api_rate_limit:${ip}`;
    },
  });

export const createUploadRateLimiter = () =>
  new RateLimiter({
    requestsPerMinute: 10, // More restrictive for uploads
    keyGenerator: (c) => {
      const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';
      return `upload_rate_limit:${ip}`;
    },
  });

export const createSearchRateLimiter = () =>
  new RateLimiter({
    requestsPerMinute: 30, // Moderate limit for search
    keyGenerator: (c) => {
      const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';
      return `search_rate_limit:${ip}`;
    },
  });

// Middleware functions
export const apiRateLimit = createApiRateLimiter().middleware.bind(createApiRateLimiter());
export const uploadRateLimit = createUploadRateLimiter().middleware.bind(createUploadRateLimiter());
export const searchRateLimit = createSearchRateLimiter().middleware.bind(createSearchRateLimiter());
