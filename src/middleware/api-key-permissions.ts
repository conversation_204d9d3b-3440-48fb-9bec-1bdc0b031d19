/**
 * API Key Permission Middleware
 *
 * Provides permission validation for API key authentication using the unified RBAC system.
 * Integrates with the existing authentication system to provide granular access control for API keys.
 */

import type { TApiKey } from '@dbSchema';
import { isValidPermission } from '@utils/permission-constants';
import type { Context, Next } from 'hono';
import type { TPermission } from '@/types/teams';
import type { IApiKeyHonoEnv } from './api-key-middleware';

// ============================================================================
// Permission Constants (Using Unified RBAC System)
// ============================================================================

/**
 * API key permission mapping to RBAC permissions
 * Maps legacy API key permissions to new unified permission system
 */
export const API_KEY_PERMISSION_MAPPING = {
  // Vector/Document operations (mapped to vectors category)
  'read.documents': 'read.vectors' as TPermission,
  'write.documents': 'create.vectors' as TPermission,
  'delete.documents': 'delete.vectors' as TPermission,
  'manage.documents': 'manage.vectors' as TPermission,

  // Search operations (mapped to vectors category for search)
  'read.search': 'read.vectors' as TPermission,
  'write.search': 'create.vectors' as TPermission,

  // Upload operations (mapped to vectors category)
  'read.upload': 'read.vectors' as TPermission,
  'write.upload': 'create.vectors' as TPermission,

  // Project operations
  'read.projects': 'read.project' as TPermission,
  'write.projects': 'create.project' as TPermission,
  'delete.projects': 'delete.project' as TPermission,
  'manage.projects': 'manage.project' as TPermission,

  // Team operations
  'read.teams': 'read.team' as TPermission,
  'write.teams': 'create.team' as TPermission,
  'delete.teams': 'delete.team' as TPermission,
  'manage.teams': 'manage.team' as TPermission,

  // Billing operations
  'read.billing': 'read.billing' as TPermission,
  'write.billing': 'create.billing' as TPermission,
  'manage.billing': 'manage.billing' as TPermission,

  // Analytics operations
  'read.analytics': 'read.analytics' as TPermission,
  'write.analytics': 'create.analytics' as TPermission,
  'manage.analytics': 'manage.analytics' as TPermission,

  // API key operations
  'read.api_keys': 'read.api_keys' as TPermission,
  'write.api_keys': 'create.api_keys' as TPermission,
  'manage.api_keys': 'manage.api_keys' as TPermission,
} as const;

/**
 * Permission groups for easier management using unified RBAC permissions
 */
export const API_KEY_PERMISSION_GROUPS = {
  // Vector/Document operations
  VECTORS_READ: ['read.vectors'] as TPermission[],
  VECTORS_WRITE: ['read.vectors', 'create.vectors'] as TPermission[],
  VECTORS_FULL: [
    'read.vectors',
    'create.vectors',
    'update.vectors',
    'delete.vectors',
  ] as TPermission[],
  VECTORS_MANAGE: ['manage.vectors'] as TPermission[],

  // Project operations
  PROJECTS_READ: ['read.project'] as TPermission[],
  PROJECTS_WRITE: ['read.project', 'create.project'] as TPermission[],
  PROJECTS_FULL: [
    'read.project',
    'create.project',
    'update.project',
    'delete.project',
  ] as TPermission[],
  PROJECTS_MANAGE: ['manage.project'] as TPermission[],

  // Team operations
  TEAMS_READ: ['read.team'] as TPermission[],
  TEAMS_WRITE: ['read.team', 'create.team'] as TPermission[],
  TEAMS_FULL: ['read.team', 'create.team', 'update.team', 'delete.team'] as TPermission[],
  TEAMS_MANAGE: ['manage.team'] as TPermission[],

  // Billing operations
  BILLING_READ: ['read.billing'] as TPermission[],
  BILLING_WRITE: ['read.billing', 'create.billing'] as TPermission[],
  BILLING_FULL: [
    'read.billing',
    'create.billing',
    'update.billing',
    'delete.billing',
  ] as TPermission[],
  BILLING_MANAGE: ['manage.billing'] as TPermission[],

  // Analytics operations
  ANALYTICS_READ: ['read.analytics'] as TPermission[],
  ANALYTICS_WRITE: ['read.analytics', 'create.analytics'] as TPermission[],
  ANALYTICS_FULL: [
    'read.analytics',
    'create.analytics',
    'update.analytics',
    'delete.analytics',
  ] as TPermission[],
  ANALYTICS_MANAGE: ['manage.analytics'] as TPermission[],

  // API key operations
  API_KEYS_READ: ['read.api_keys'] as TPermission[],
  API_KEYS_WRITE: ['read.api_keys', 'create.api_keys'] as TPermission[],
  API_KEYS_FULL: [
    'read.api_keys',
    'create.api_keys',
    'update.api_keys',
    'delete.api_keys',
  ] as TPermission[],
  API_KEYS_MANAGE: ['manage.api_keys'] as TPermission[],
} as const;

// ============================================================================
// Permission Validation Functions
// ============================================================================

/**
 * Parse permissions from API key record using unified RBAC system
 *
 * @param apiKey - API key record
 * @returns Array of unified permissions
 */
function parseApiKeyPermissions(apiKey: TApiKey): TPermission[] {
  const permissions: TPermission[] = [];

  // Parse main permissions
  if (apiKey.permissions) {
    try {
      const parsed = JSON.parse(apiKey.permissions);
      if (Array.isArray(parsed)) {
        // Validate and add permissions
        for (const permission of parsed) {
          if (typeof permission === 'string' && isValidPermission(permission)) {
            permissions.push(permission as TPermission);
          }
        }
      }
    } catch (error) {
      console.error('Failed to parse API key permissions:', error);
    }
  }

  // Parse scoped permissions (for team context)
  if (apiKey.scopedPermissions) {
    try {
      const parsed = JSON.parse(apiKey.scopedPermissions);
      if (Array.isArray(parsed)) {
        // Validate and add scoped permissions
        for (const permission of parsed) {
          if (typeof permission === 'string' && isValidPermission(permission)) {
            permissions.push(permission as TPermission);
          }
        }
      }
    } catch (error) {
      console.error('Failed to parse API key scoped permissions:', error);
    }
  }

  return permissions;
}

/**
 * Check if API key has required permission using unified RBAC system
 *
 * @param apiKey - API key record
 * @param requiredPermission - Required permission
 * @returns True if permission is granted
 */
function hasPermission(apiKey: TApiKey, requiredPermission: TPermission): boolean {
  const permissions = parseApiKeyPermissions(apiKey);

  // Check for specific permission
  return permissions.includes(requiredPermission);
}

/**
 * Check if API key has any of the required permissions using unified RBAC system
 *
 * @param apiKey - API key record
 * @param requiredPermissions - Array of required permissions (OR logic)
 * @returns True if any permission is granted
 */
function hasAnyPermission(apiKey: TApiKey, requiredPermissions: TPermission[]): boolean {
  return requiredPermissions.some((permission) => hasPermission(apiKey, permission));
}

/**
 * Check if API key has all required permissions using unified RBAC system
 *
 * @param apiKey - API key record
 * @param requiredPermissions - Array of required permissions (AND logic)
 * @returns True if all permissions are granted
 */
function hasAllPermissions(apiKey: TApiKey, requiredPermissions: TPermission[]): boolean {
  return requiredPermissions.every((permission) => hasPermission(apiKey, permission));
}

// ============================================================================
// Permission Middleware Functions
// ============================================================================

/**
 * Require specific permission middleware factory
 *
 * Creates middleware that checks if the API key has the required permission.
 * Only applies to API key authentication - session auth bypasses permission checks.
 *
 * @param requiredPermission - Required permission
 * @returns Middleware function
 *
 * @example
 * ```typescript
 * app.get('/api/documents', requirePermission('read.documents'), handler);
 * ```
 */
export const requirePermission = (requiredPermission: TPermission) => {
  return async (c: Context<IApiKeyHonoEnv>, next: Next) => {
    const authMethod = c.get('authMethod');
    const apiKey = c.get('apiKey');

    // Session authentication bypasses API key permission checks
    if (authMethod === 'session') {
      await next();
      return;
    }

    // API key authentication requires permission validation
    if (authMethod === 'api_key' && apiKey) {
      if (!hasPermission(apiKey, requiredPermission)) {
        const currentPermissions = parseApiKeyPermissions(apiKey);

        return c.json(
          {
            status: 'error',
            error: 'Insufficient permissions',
            error_type: 'insufficient_permissions',
            details: {
              message: `API key does not have required permission: ${requiredPermission}`,
              required_permissions: [requiredPermission],
              current_permissions: currentPermissions,
            },
            timestamp: new Date().toISOString(),
          },
          403
        );
      }

      await next();
      return;
    }

    // No authentication or invalid auth method
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'missing_auth',
        details: {
          message: 'Valid authentication required to access this resource',
        },
        timestamp: new Date().toISOString(),
      },
      401
    );
  };
};

/**
 * Require any of the specified permissions middleware factory
 *
 * @param requiredPermissions - Array of permissions (OR logic)
 * @returns Middleware function
 */
export const requireAnyPermission = (requiredPermissions: TPermission[]) => {
  return async (c: Context<IApiKeyHonoEnv>, next: Next) => {
    const authMethod = c.get('authMethod');
    const apiKey = c.get('apiKey');

    // Session authentication bypasses API key permission checks
    if (authMethod === 'session') {
      await next();
      return;
    }

    // API key authentication requires permission validation
    if (authMethod === 'api_key' && apiKey) {
      if (!hasAnyPermission(apiKey, requiredPermissions)) {
        const currentPermissions = parseApiKeyPermissions(apiKey);

        return c.json(
          {
            status: 'error',
            error: 'Insufficient permissions',
            error_type: 'insufficient_permissions',
            details: {
              message: `API key does not have any of the required permissions`,
              required_permissions: requiredPermissions,
              current_permissions: currentPermissions,
            },
            timestamp: new Date().toISOString(),
          },
          403
        );
      }

      await next();
      return;
    }

    // No authentication or invalid auth method
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'missing_auth',
        timestamp: new Date().toISOString(),
      },
      401
    );
  };
};

/**
 * Require all specified permissions middleware factory
 *
 * @param requiredPermissions - Array of permissions (AND logic)
 * @returns Middleware function
 */
export const requireAllPermissions = (requiredPermissions: TPermission[]) => {
  return async (c: Context<IApiKeyHonoEnv>, next: Next) => {
    const authMethod = c.get('authMethod');
    const apiKey = c.get('apiKey');

    // Session authentication bypasses API key permission checks
    if (authMethod === 'session') {
      await next();
      return;
    }

    // API key authentication requires permission validation
    if (authMethod === 'api_key' && apiKey) {
      if (!hasAllPermissions(apiKey, requiredPermissions)) {
        const currentPermissions = parseApiKeyPermissions(apiKey);

        return c.json(
          {
            status: 'error',
            error: 'Insufficient permissions',
            error_type: 'insufficient_permissions',
            details: {
              message: `API key does not have all required permissions`,
              required_permissions: requiredPermissions,
              current_permissions: currentPermissions,
            },
            timestamp: new Date().toISOString(),
          },
          403
        );
      }

      await next();
      return;
    }

    // No authentication or invalid auth method
    return c.json(
      {
        status: 'error',
        error: 'Authentication required',
        error_type: 'missing_auth',
        timestamp: new Date().toISOString(),
      },
      401
    );
  };
};
