/**
 * Error Handler Middleware
 *
 * Centralized error handling for production-ready error responses.
 */

import type { IErrorDetails, IErrorResponse, IHonoEnv } from '@types';
import type { Context, Next } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { ZodError } from 'zod';

export const errorHandler = async (c: Context<IHonoEnv>, next: Next) => {
  try {
    await next();
  } catch (error) {
    // Log error with request context for security monitoring
    const requestId = c.req.header('X-Request-ID') || 'unknown';
    const userAgent = c.req.header('User-Agent') || 'unknown';
    const ip = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown';

    console.error('Unhandled error:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      requestId,
      userAgent,
      ip,
      path: c.req.path,
      method: c.req.method,
      timestamp: new Date().toISOString(),
    });

    // Handle HTTP exceptions from Hono
    if (error instanceof HTTPException) {
      // Sanitize error message to prevent information leakage
      let sanitizedMessage = error.message;
      if (error.status >= 500) {
        sanitizedMessage = 'Internal server error occurred';
      }

      const errorResponse: IErrorResponse = {
        status: 'error',
        error: sanitizedMessage,
        error_type: 'http',
        timestamp: new Date().toISOString(),
      };
      return c.json(errorResponse, error.status);
    }

    // Handle validation errors with enhanced security
    if (error instanceof ZodError) {
      // Sanitize validation error messages to prevent information leakage
      const sanitizedErrors = error.errors.map((err) => ({
        path: err.path.join('.'),
        message: err.message,
        code: err.code,
      }));

      const errorDetails: IErrorDetails = {
        field: 'validation',
        message: 'Input validation failed',
        errors: sanitizedErrors,
      };

      const errorResponse: IErrorResponse = {
        status: 'error',
        error: 'Validation failed',
        error_type: 'validation',
        details: errorDetails,
        timestamp: new Date().toISOString(),
      };
      return c.json(errorResponse, 400);
    }

    // Handle input sanitization errors
    if (error instanceof Error && error.message.includes('exceeds maximum')) {
      const errorResponse: IErrorResponse = {
        status: 'error',
        error: 'Input validation failed: Content exceeds allowed limits',
        error_type: 'validation',
        timestamp: new Date().toISOString(),
      };
      return c.json(errorResponse, 400);
    }

    // Handle potential security-related errors
    if (
      error instanceof Error &&
      (error.message.includes('SQL') ||
        error.message.includes('XSS') ||
        error.message.includes('injection') ||
        error.message.includes('script'))
    ) {
      // Log security incident but don't expose details
      console.warn('Potential security incident detected:', {
        error: error.message,
        ip,
        userAgent,
        path: c.req.path,
        timestamp: new Date().toISOString(),
      });

      const errorResponse: IErrorResponse = {
        status: 'error',
        error: 'Request blocked for security reasons',
        error_type: 'security',
        timestamp: new Date().toISOString(),
      };
      return c.json(errorResponse, 403);
    }

    // Handle custom application errors
    if (error instanceof Error) {
      let errorType = 'unknown';
      let statusCode = 500;

      // Categorize errors based on error names or messages
      if (error.name === 'FileValidationError') {
        errorType = 'validation';
        statusCode = 400;
      } else if (error.name === 'DatabaseError') {
        errorType = 'database';
        statusCode = 500;
      } else if (error.message.includes('not found')) {
        errorType = 'not_found';
        statusCode = 404;
      } else if (error.message.includes('unauthorized')) {
        errorType = 'unauthorized';
        statusCode = 401;
      } else if (error.message.includes('forbidden')) {
        errorType = 'forbidden';
        statusCode = 403;
      }

      const errorResponse: IErrorResponse = {
        status: 'error',
        error: error.message,
        error_type: errorType,
        timestamp: new Date().toISOString(),
      };

      return c.json(errorResponse, statusCode as 400 | 401 | 403 | 404 | 422 | 429 | 500);
    }

    // Fallback for unknown errors
    const errorResponse: IErrorResponse = {
      status: 'error',
      error: 'An unexpected error occurred',
      error_type: 'unknown',
      timestamp: new Date().toISOString(),
    };

    return c.json(errorResponse, 500);
  }
};
