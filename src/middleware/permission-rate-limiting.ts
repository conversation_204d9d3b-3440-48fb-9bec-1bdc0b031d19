/**
 * Permission-Based Rate Limiting Middleware
 *
 * Implements rate limiting based on API key permissions with different limits
 * for read vs write operations. Integrates with the billing middleware for
 * comprehensive usage tracking and enforcement.
 */

import type { TApiKey } from '@dbSchema';
import type { Context, Next } from 'hono';
import type { TPermission } from '@/types/teams';
import type { IApiKeyHonoEnv } from './api-key-middleware';
import type { IBillingHonoEnv } from './billing-middleware';

// ============================================================================
// Permission Rate Limiting Types
// ============================================================================

/**
 * Rate limiting configuration per permission level
 */
export interface IPermissionRateLimit {
  /** Permission category */
  category: string;
  /** Permission action */
  action: string;
  /** Requests per minute limit */
  requestsPerMinute: number;
  /** Requests per hour limit */
  requestsPerHour: number;
  /** Requests per day limit */
  requestsPerDay: number;
  /** Burst limit (short-term spike allowance) */
  burstLimit: number;
}

/**
 * Rate limiting configuration by permission type
 */
export const PERMISSION_RATE_LIMITS: Record<string, IPermissionRateLimit> = {
  // Read operations - higher limits
  'read.vectors': {
    category: 'vectors',
    action: 'read',
    requestsPerMinute: 100,
    requestsPerHour: 5000,
    requestsPerDay: 50000,
    burstLimit: 20,
  },
  'read.project': {
    category: 'project',
    action: 'read',
    requestsPerMinute: 60,
    requestsPerHour: 3000,
    requestsPerDay: 30000,
    burstLimit: 15,
  },
  'read.team': {
    category: 'team',
    action: 'read',
    requestsPerMinute: 30,
    requestsPerHour: 1500,
    requestsPerDay: 15000,
    burstLimit: 10,
  },
  'read.billing': {
    category: 'billing',
    action: 'read',
    requestsPerMinute: 20,
    requestsPerHour: 1000,
    requestsPerDay: 10000,
    burstLimit: 5,
  },
  'read.analytics': {
    category: 'analytics',
    action: 'read',
    requestsPerMinute: 30,
    requestsPerHour: 1500,
    requestsPerDay: 15000,
    burstLimit: 10,
  },
  'read.api_keys': {
    category: 'api_keys',
    action: 'read',
    requestsPerMinute: 20,
    requestsPerHour: 1000,
    requestsPerDay: 10000,
    burstLimit: 5,
  },

  // Write operations - lower limits
  'create.vectors': {
    category: 'vectors',
    action: 'create',
    requestsPerMinute: 30,
    requestsPerHour: 1500,
    requestsPerDay: 15000,
    burstLimit: 10,
  },
  'update.vectors': {
    category: 'vectors',
    action: 'update',
    requestsPerMinute: 20,
    requestsPerHour: 1000,
    requestsPerDay: 10000,
    burstLimit: 5,
  },
  'delete.vectors': {
    category: 'vectors',
    action: 'delete',
    requestsPerMinute: 10,
    requestsPerHour: 500,
    requestsPerDay: 5000,
    burstLimit: 3,
  },
  'create.project': {
    category: 'project',
    action: 'create',
    requestsPerMinute: 10,
    requestsPerHour: 500,
    requestsPerDay: 5000,
    burstLimit: 3,
  },
  'update.project': {
    category: 'project',
    action: 'update',
    requestsPerMinute: 15,
    requestsPerHour: 750,
    requestsPerDay: 7500,
    burstLimit: 5,
  },
  'delete.project': {
    category: 'project',
    action: 'delete',
    requestsPerMinute: 5,
    requestsPerHour: 250,
    requestsPerDay: 2500,
    burstLimit: 2,
  },

  // Manage operations - moderate limits
  'manage.vectors': {
    category: 'vectors',
    action: 'manage',
    requestsPerMinute: 50,
    requestsPerHour: 2500,
    requestsPerDay: 25000,
    burstLimit: 15,
  },
  'manage.project': {
    category: 'project',
    action: 'manage',
    requestsPerMinute: 30,
    requestsPerHour: 1500,
    requestsPerDay: 15000,
    burstLimit: 10,
  },
  'manage.team': {
    category: 'team',
    action: 'manage',
    requestsPerMinute: 20,
    requestsPerHour: 1000,
    requestsPerDay: 10000,
    burstLimit: 5,
  },
  'manage.billing': {
    category: 'billing',
    action: 'manage',
    requestsPerMinute: 10,
    requestsPerHour: 500,
    requestsPerDay: 5000,
    burstLimit: 3,
  },
  'manage.analytics': {
    category: 'analytics',
    action: 'manage',
    requestsPerMinute: 15,
    requestsPerHour: 750,
    requestsPerDay: 7500,
    burstLimit: 5,
  },
  'manage.api_keys': {
    category: 'api_keys',
    action: 'manage',
    requestsPerMinute: 10,
    requestsPerHour: 500,
    requestsPerDay: 5000,
    burstLimit: 3,
  },
};

/**
 * Default rate limits for unknown permissions
 */
export const DEFAULT_RATE_LIMIT: IPermissionRateLimit = {
  category: 'default',
  action: 'default',
  requestsPerMinute: 10,
  requestsPerHour: 500,
  requestsPerDay: 5000,
  burstLimit: 3,
};

/**
 * Rate limiting window types
 */
export type TRateLimitWindow = 'minute' | 'hour' | 'day' | 'burst';

/**
 * Rate limiting result
 */
export interface IRateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: Date;
  window: TRateLimitWindow;
  retryAfter?: number; // seconds
}

// ============================================================================
// Extended Environment Types
// ============================================================================

/**
 * Extended Hono environment with permission rate limiting context
 */
export interface IPermissionRateLimitHonoEnv extends IApiKeyHonoEnv, IBillingHonoEnv {
  Variables: IApiKeyHonoEnv['Variables'] &
    IBillingHonoEnv['Variables'] & {
      rateLimitResult: IRateLimitResult | null;
      appliedRateLimit: IPermissionRateLimit | null;
    };
}

// ============================================================================
// Rate Limiting Storage (In-Memory for now, can be extended to Redis/KV)
// ============================================================================

/**
 * Rate limiting storage interface
 */
interface IRateLimitStorage {
  get(key: string): Promise<number | null>;
  set(key: string, value: number, ttlSeconds: number): Promise<void>;
  increment(key: string, ttlSeconds: number): Promise<number>;
}

/**
 * In-memory rate limiting storage
 * Note: In production, this should be replaced with Redis or Cloudflare KV
 */
class InMemoryRateLimitStorage implements IRateLimitStorage {
  private storage = new Map<string, { value: number; expires: number }>();

  async get(key: string): Promise<number | null> {
    const item = this.storage.get(key);
    if (!item || item.expires < Date.now()) {
      this.storage.delete(key);
      return null;
    }
    return item.value;
  }

  async set(key: string, value: number, ttlSeconds: number): Promise<void> {
    this.storage.set(key, {
      value,
      expires: Date.now() + ttlSeconds * 1000,
    });
  }

  async increment(key: string, ttlSeconds: number): Promise<number> {
    const current = (await this.get(key)) || 0;
    const newValue = current + 1;
    await this.set(key, newValue, ttlSeconds);
    return newValue;
  }

  // Cleanup expired entries periodically
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.storage.entries()) {
      if (item.expires < now) {
        this.storage.delete(key);
      }
    }
  }
}

// Global storage instance
const rateLimitStorage = new InMemoryRateLimitStorage();

// Cleanup expired entries every 5 minutes
setInterval(() => rateLimitStorage.cleanup(), 5 * 60 * 1000);

// ============================================================================
// Rate Limiting Functions
// ============================================================================

/**
 * Get rate limit configuration for a permission
 *
 * @param permission - Permission to get rate limit for
 * @returns Rate limit configuration
 */
function getRateLimitForPermission(permission: TPermission): IPermissionRateLimit {
  return PERMISSION_RATE_LIMITS[permission] || DEFAULT_RATE_LIMIT;
}

/**
 * Get the most restrictive rate limit from a list of permissions
 *
 * @param permissions - List of permissions
 * @returns Most restrictive rate limit
 */
function getMostRestrictiveRateLimit(permissions: TPermission[]): IPermissionRateLimit {
  if (permissions.length === 0) {
    return DEFAULT_RATE_LIMIT;
  }

  let mostRestrictive = getRateLimitForPermission(permissions[0]);

  for (let i = 1; i < permissions.length; i++) {
    const current = getRateLimitForPermission(permissions[i]);

    // Use the most restrictive limits
    if (current.requestsPerMinute < mostRestrictive.requestsPerMinute) {
      mostRestrictive = current;
    }
  }

  return mostRestrictive;
}

/**
 * Generate rate limiting key
 *
 * @param apiKeyId - API key ID
 * @param window - Time window
 * @returns Storage key
 */
function getRateLimitKey(apiKeyId: string, window: TRateLimitWindow): string {
  const now = new Date();
  let timeKey: string;

  switch (window) {
    case 'minute':
      timeKey = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours()}-${now.getMinutes()}`;
      break;
    case 'hour':
      timeKey = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours()}`;
      break;
    case 'day':
      timeKey = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}`;
      break;
    case 'burst':
      // Burst window is 10 seconds
      timeKey = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours()}-${now.getMinutes()}-${Math.floor(now.getSeconds() / 10)}`;
      break;
    default:
      timeKey = 'unknown';
  }

  return `rate_limit:${apiKeyId}:${window}:${timeKey}`;
}

/**
 * Get TTL for rate limiting window
 *
 * @param window - Time window
 * @returns TTL in seconds
 */
function getWindowTTL(window: TRateLimitWindow): number {
  switch (window) {
    case 'minute':
      return 60;
    case 'hour':
      return 3600;
    case 'day':
      return 86400;
    case 'burst':
      return 10;
    default:
      return 60;
  }
}

/**
 * Check rate limit for a specific window
 *
 * @param apiKeyId - API key ID
 * @param window - Time window
 * @param limit - Request limit for this window
 * @returns Rate limit result
 */
async function checkRateLimitWindow(
  apiKeyId: string,
  window: TRateLimitWindow,
  limit: number
): Promise<IRateLimitResult> {
  const key = getRateLimitKey(apiKeyId, window);
  const ttl = getWindowTTL(window);

  const current = (await rateLimitStorage.get(key)) || 0;
  const remaining = Math.max(0, limit - current);
  const allowed = current < limit;

  // Calculate reset time
  const now = new Date();
  let resetTime: Date;

  switch (window) {
    case 'minute':
      resetTime = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        now.getHours(),
        now.getMinutes() + 1
      );
      break;
    case 'hour':
      resetTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours() + 1);
      break;
    case 'day':
      resetTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      break;
    case 'burst':
      resetTime = new Date(now.getTime() + 10000); // 10 seconds
      break;
    default:
      resetTime = new Date(now.getTime() + ttl * 1000);
  }

  const retryAfter = allowed ? undefined : Math.ceil((resetTime.getTime() - now.getTime()) / 1000);

  return {
    allowed,
    limit,
    remaining,
    resetTime,
    window,
    retryAfter,
  };
}

/**
 * Check all rate limiting windows for an API key
 *
 * @param apiKey - API key record
 * @param rateLimit - Rate limit configuration
 * @returns Rate limit result (most restrictive)
 */
async function checkAllRateLimits(
  apiKey: TApiKey,
  rateLimit: IPermissionRateLimit
): Promise<IRateLimitResult> {
  const checks = await Promise.all([
    checkRateLimitWindow(apiKey.id, 'burst', rateLimit.burstLimit),
    checkRateLimitWindow(apiKey.id, 'minute', rateLimit.requestsPerMinute),
    checkRateLimitWindow(apiKey.id, 'hour', rateLimit.requestsPerHour),
    checkRateLimitWindow(apiKey.id, 'day', rateLimit.requestsPerDay),
  ]);

  // Return the most restrictive result (first one that's not allowed)
  const restrictive = checks.find((check) => !check.allowed);
  return restrictive || checks[0]; // If all allowed, return burst result
}

/**
 * Record API key usage for rate limiting
 *
 * @param apiKeyId - API key ID
 * @param rateLimit - Rate limit configuration
 */
async function recordRateLimitUsage(
  apiKeyId: string,
  _rateLimit: IPermissionRateLimit
): Promise<void> {
  const windows: TRateLimitWindow[] = ['burst', 'minute', 'hour', 'day'];

  await Promise.all(
    windows.map(async (window) => {
      const key = getRateLimitKey(apiKeyId, window);
      const ttl = getWindowTTL(window);
      await rateLimitStorage.increment(key, ttl);
    })
  );
}

// ============================================================================
// Permission-Based Rate Limiting Middleware
// ============================================================================

/**
 * Permission-based rate limiting middleware
 *
 * Applies rate limiting based on API key permissions with different limits
 * for read vs write operations. Only applies to API key authentication.
 *
 * @param requiredPermission - Permission required for this endpoint
 * @returns Middleware function
 */
export function permissionRateLimit(requiredPermission: TPermission) {
  return async (c: Context<IPermissionRateLimitHonoEnv>, next: Next) => {
    const authMethod = c.get('authMethod');
    const apiKey = c.get('apiKey');

    // Initialize rate limit context
    c.set('rateLimitResult', null);
    c.set('appliedRateLimit', null);

    // Only apply rate limiting to API key authentication
    if (authMethod !== 'api_key' || !apiKey) {
      await next();
      return;
    }

    try {
      // Get rate limit configuration for the required permission
      const rateLimit = getRateLimitForPermission(requiredPermission);
      c.set('appliedRateLimit', rateLimit);

      // Check rate limits
      const rateLimitResult = await checkAllRateLimits(apiKey, rateLimit);
      c.set('rateLimitResult', rateLimitResult);

      // Set rate limiting headers
      c.header('X-RateLimit-Limit', rateLimitResult.limit.toString());
      c.header('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
      c.header(
        'X-RateLimit-Reset',
        Math.floor(rateLimitResult.resetTime.getTime() / 1000).toString()
      );
      c.header('X-RateLimit-Window', rateLimitResult.window);

      if (!rateLimitResult.allowed) {
        if (rateLimitResult.retryAfter) {
          c.header('Retry-After', rateLimitResult.retryAfter.toString());
        }

        return c.json(
          {
            status: 'error',
            error: 'Rate limit exceeded',
            error_type: 'rate_limit_exceeded',
            details: {
              permission: requiredPermission,
              window: rateLimitResult.window,
              limit: rateLimitResult.limit,
              remaining: rateLimitResult.remaining,
              resetTime: rateLimitResult.resetTime.toISOString(),
              retryAfter: rateLimitResult.retryAfter,
            },
            timestamp: new Date().toISOString(),
          },
          429 // Too Many Requests
        );
      }

      // Record usage after successful check
      await recordRateLimitUsage(apiKey.id, rateLimit);

      await next();
    } catch (error) {
      console.error('Permission rate limiting error:', error);
      // Don't block request on rate limiting errors, just log them
      await next();
    }
  };
}

/**
 * Adaptive rate limiting middleware
 *
 * Automatically determines the most restrictive rate limit based on
 * all permissions granted to the API key.
 *
 * @returns Middleware function
 */
export function adaptivePermissionRateLimit() {
  return async (c: Context<IPermissionRateLimitHonoEnv>, next: Next) => {
    const authMethod = c.get('authMethod');
    const apiKey = c.get('apiKey');

    // Initialize rate limit context
    c.set('rateLimitResult', null);
    c.set('appliedRateLimit', null);

    // Only apply rate limiting to API key authentication
    if (authMethod !== 'api_key' || !apiKey) {
      await next();
      return;
    }

    try {
      // Parse API key permissions
      let permissions: TPermission[] = [];
      if (apiKey.permissions) {
        try {
          const parsed = JSON.parse(apiKey.permissions);
          if (Array.isArray(parsed)) {
            permissions = parsed.filter((p): p is TPermission => typeof p === 'string');
          }
        } catch {
          // Use default rate limit if permissions can't be parsed
          permissions = [];
        }
      }

      // Get the most restrictive rate limit
      const rateLimit = getMostRestrictiveRateLimit(permissions);
      c.set('appliedRateLimit', rateLimit);

      // Check rate limits
      const rateLimitResult = await checkAllRateLimits(apiKey, rateLimit);
      c.set('rateLimitResult', rateLimitResult);

      // Set rate limiting headers
      c.header('X-RateLimit-Limit', rateLimitResult.limit.toString());
      c.header('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
      c.header(
        'X-RateLimit-Reset',
        Math.floor(rateLimitResult.resetTime.getTime() / 1000).toString()
      );
      c.header('X-RateLimit-Window', rateLimitResult.window);

      if (!rateLimitResult.allowed) {
        if (rateLimitResult.retryAfter) {
          c.header('Retry-After', rateLimitResult.retryAfter.toString());
        }

        return c.json(
          {
            status: 'error',
            error: 'Rate limit exceeded',
            error_type: 'rate_limit_exceeded',
            details: {
              appliedRateLimit: rateLimit,
              window: rateLimitResult.window,
              limit: rateLimitResult.limit,
              remaining: rateLimitResult.remaining,
              resetTime: rateLimitResult.resetTime.toISOString(),
              retryAfter: rateLimitResult.retryAfter,
            },
            timestamp: new Date().toISOString(),
          },
          429 // Too Many Requests
        );
      }

      // Record usage after successful check
      await recordRateLimitUsage(apiKey.id, rateLimit);

      await next();
    } catch (error) {
      console.error('Adaptive permission rate limiting error:', error);
      // Don't block request on rate limiting errors, just log them
      await next();
    }
  };
}

/**
 * Get rate limiting status for an API key
 *
 * @param apiKey - API key record
 * @param permission - Optional specific permission to check
 * @returns Current rate limiting status
 */
export async function getApiKeyRateLimitStatus(
  apiKey: TApiKey,
  permission?: TPermission
): Promise<{
  rateLimit: IPermissionRateLimit;
  windows: Record<TRateLimitWindow, IRateLimitResult>;
}> {
  const rateLimit = permission
    ? getRateLimitForPermission(permission)
    : getMostRestrictiveRateLimit([]);

  const windows = {
    burst: await checkRateLimitWindow(apiKey.id, 'burst', rateLimit.burstLimit),
    minute: await checkRateLimitWindow(apiKey.id, 'minute', rateLimit.requestsPerMinute),
    hour: await checkRateLimitWindow(apiKey.id, 'hour', rateLimit.requestsPerHour),
    day: await checkRateLimitWindow(apiKey.id, 'day', rateLimit.requestsPerDay),
  };

  return { rateLimit, windows };
}
