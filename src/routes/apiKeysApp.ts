/**
 * API Keys Routes
 *
 * REST API endpoints for API key management with team-based RBAC.
 * Provides CRUD operations, usage analytics, and permission management.
 */

import * as schema from '@dbSchema';
import { zValidator } from '@hono/zod-validator';
// Import middleware individually to avoid global scope async operations
import { authMiddleware, requireAuth } from '@middleware/auth-middleware';
import { enforceApiRequestLimits, type IBillingHonoEnv } from '@middleware/billing-middleware';
import {
  type IRBACHonoEnv,
  requireAnyTeamPermission,
  requireTeamPermission,
  teamContextMiddleware,
} from '@middleware/rbac-middleware';
import { apiKeyAnalyticsService } from '@services/apiKeyAnalyticsService';

// Combined environment type for API keys app
type IApiKeysHonoEnv = IRBACHonoEnv & IBillingHonoEnv;

// ============================================================================
// Type-Compatible Audit Logging
// ============================================================================

/**
 * Type-compatible wrapper for createAuditLog
 */
const createApiKeyAuditLog = async (
  c: Context<IApiKeysHonoEnv>,
  action: TAuditAction,
  resource: TAuditResource,
  resourceId?: string,
  details?: Record<string, unknown>
) => {
  const user = c.get('user');
  const teamId = c.get('teamId');
  const ipAddress = c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || null;
  const userAgent = c.req.header('User-Agent') || null;

  return createAuditLog({
    userId: user?.id,
    teamId: teamId || undefined,
    action,
    resource,
    resourceId,
    details: {
      ...details,
      ip_address: ipAddress || undefined,
      user_agent: userAgent || undefined,
    },
    ipAddress: ipAddress || undefined,
    userAgent: userAgent || undefined,
  });
};

import {
  bulkUpdateApiKeys,
  createApiKey,
  deleteApiKey,
  extendApiKeyExpiration,
  getApiKeyAnalytics,
  getApiKeyById,
  getApiKeyExpirationStatus,
  getBulkExpirationSummary,
  getExpiringApiKeys,
  type IApiKeyFilters,
  type IApiKeyListOptions,
  type IBulkApiKeyOperation,
  type ICreateApiKeyParams,
  type IUpdateApiKeyParams,
  listApiKeys,
  rotateApiKey,
  rotateApiKeyEnhanced,
  sendExpirationNotifications,
  updateApiKey,
  validateApiKeyPermissions,
} from '@services/apiKeyService';
import { createAuditLog, type TAuditAction, type TAuditResource } from '@services/auditService';
import { notificationService } from '@services/notificationService';

import getDB from '@utils/getDB';
import { and, desc, eq, isNull } from 'drizzle-orm';
import type { Context } from 'hono';
import { Hono } from 'hono';
import { z } from 'zod';
import type { TPermission } from '@/types/teams';

const app = new Hono<IApiKeysHonoEnv>();

// Apply authentication middleware to all routes
app.use('*', authMiddleware, requireAuth);

// Apply team context middleware to all routes that need team context for billing
app.use('*', teamContextMiddleware());

// ============================================================================
// Helper Functions
// ============================================================================

/**
 * Check if user has access to a team
 *
 * @param userId - User ID
 * @param teamId - Team ID
 * @returns True if user is a member of the team
 */
async function hasTeamAccess(userId: string, teamId: string): Promise<boolean> {
  const db = await getDB();

  const teamMember = await db.query.teamMembers.findFirst({
    where: and(
      eq(schema.teamMembers.userId, userId),
      eq(schema.teamMembers.teamId, teamId),
      isNull(schema.teamMembers.deletedAt)
    ),
  });

  return !!teamMember;
}

// ============================================================================
// Validation Schemas
// ============================================================================

const createApiKeySchema = z.object({
  name: z.string().min(1).max(255),
  permissions: z.array(z.string()).optional(),
  scopedPermissions: z.array(z.string()).optional(),
  usageLimit: z.number().int().positive().optional(),
  expiresAt: z.string().datetime().optional(),
});

const updateApiKeySchema = z.object({
  name: z.string().min(1).max(255).optional(),
  permissions: z.array(z.string()).optional(),
  scopedPermissions: z.array(z.string()).optional(),
  usageLimit: z.number().int().positive().optional(),
  expiresAt: z.string().datetime().optional(),
  isActive: z.boolean().optional(),
});

const listApiKeysSchema = z.object({
  userId: z.string().uuid().optional(),
  teamId: z.string().uuid().optional(),
  isActive: z.boolean().optional(),
  hasExpired: z.boolean().optional(),
  permissions: z.array(z.string()).optional(),
  usageThreshold: z.number().min(0).max(100).optional(),
  createdAfter: z.string().datetime().optional(),
  createdBefore: z.string().datetime().optional(),
  lastUsedAfter: z.string().datetime().optional(),
  lastUsedBefore: z.string().datetime().optional(),
  sortBy: z.enum(['createdAt', 'lastUsedAt', 'usageCount', 'name']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  limit: z.number().int().min(1).max(100).optional(),
  offset: z.number().int().min(0).optional(),
  includeStats: z.boolean().optional(),
});

const bulkOperationSchema = z.object({
  keyIds: z.array(z.string().uuid()).min(1).max(50),
  operation: z.enum(['activate', 'deactivate', 'delete', 'update_usage_limit']),
  params: z
    .object({
      usageLimit: z.number().int().positive().optional(),
    })
    .optional(),
});

const validatePermissionsSchema = z.object({
  permissions: z.array(z.string()).min(1),
  teamId: z.string().uuid().optional(),
});

const comprehensiveAnalyticsSchema = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
});

const teamAnalyticsSchema = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
});

// ============================================================================
// User API Keys (Personal)
// ============================================================================

/**
 * GET /api/users/:userId/api-keys
 * Get user's personal API keys
 */
app.get('/users/:userId/api-keys', enforceApiRequestLimits(), async (c) => {
  const user = c.get('user');
  const userId = c.req.param('userId');

  // Users can only access their own API keys (or admins can access any)
  if (!user || (user.id !== userId && user.role !== 'admin')) {
    return c.json(
      {
        status: 'error',
        error: 'Access denied',
        error_type: 'authorization',
        timestamp: new Date().toISOString(),
      },
      403
    );
  }

  try {
    // Get user's personal API keys (not team-scoped)
    const db = await getDB();
    const apiKeys = await db.query.apiKeys.findMany({
      where: and(
        eq(schema.apiKeys.userId, userId),
        isNull(schema.apiKeys.teamId),
        isNull(schema.apiKeys.deletedAt)
      ),
      orderBy: desc(schema.apiKeys.createdAt),
    });

    // Get detailed information for each key
    const apiKeysWithStats = await Promise.all(apiKeys.map((key) => getApiKeyById(key.id)));

    return c.json({
      status: 'success',
      data: {
        api_keys: apiKeysWithStats.filter(Boolean),
        total: apiKeysWithStats.length,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching user API keys:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to fetch API keys',
        error_type: 'database',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * POST /api/users/:userId/api-keys
 * Create a personal API key
 */
app.post(
  '/users/:userId/api-keys',
  enforceApiRequestLimits(),
  zValidator('json', createApiKeySchema),
  async (c) => {
    const user = c.get('user');
    const userId = c.req.param('userId');
    const body = c.req.valid('json');

    // Users can only create their own API keys
    if (!user || user.id !== userId) {
      return c.json(
        {
          status: 'error',
          error: 'Access denied',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    try {
      const createParams: ICreateApiKeyParams = {
        userId,
        name: body.name,
        permissions: body.permissions as TPermission[],
        usageLimit: body.usageLimit,
        expiresAt: body.expiresAt ? new Date(body.expiresAt) : undefined,
      };

      const { apiKey, key } = await createApiKey(createParams);

      // Log the action
      await createApiKeyAuditLog(c, 'api_key.create', 'api_key', apiKey.id, {
        api_key_name: body.name,
        permissions: body.permissions,
      });

      return c.json(
        {
          status: 'success',
          data: {
            api_key: {
              id: apiKey.id,
              name: apiKey.name,
              key_prefix: apiKey.keyPrefix,
              permissions: apiKey.permissions ? JSON.parse(apiKey.permissions) : [],
              usage_limit: apiKey.usageLimit,
              expires_at: apiKey.expiresAt,
              created_at: apiKey.createdAt,
            },
            // Return the actual key only once during creation
            key,
          },
          timestamp: new Date().toISOString(),
        },
        201
      );
    } catch (error) {
      console.error('Error creating API key:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to create API key',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// ============================================================================
// Team API Keys
// ============================================================================

/**
 * GET /api/teams/:teamId/api-keys
 * Get team's API keys
 */
app.get(
  '/teams/:teamId/api-keys',
  enforceApiRequestLimits(),
  teamContextMiddleware(),
  requireAnyTeamPermission(['read.api_keys', 'manage.api_keys']),
  async (c) => {
    const teamId = c.req.param('teamId');

    try {
      const db = await getDB();
      const apiKeys = await db.query.apiKeys.findMany({
        where: and(eq(schema.apiKeys.teamId, teamId), isNull(schema.apiKeys.deletedAt)),
        orderBy: desc(schema.apiKeys.createdAt),
      });

      // Get detailed information for each key
      const apiKeysWithStats = await Promise.all(apiKeys.map((key) => getApiKeyById(key.id)));

      return c.json({
        status: 'success',
        data: {
          api_keys: apiKeysWithStats.filter(Boolean),
          total: apiKeysWithStats.length,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error fetching team API keys:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to fetch team API keys',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * POST /api/teams/:teamId/api-keys
 * Create a team API key
 */
app.post(
  '/teams/:teamId/api-keys',
  enforceApiRequestLimits(),
  teamContextMiddleware(),
  requireTeamPermission('manage.api_keys'),
  zValidator('json', createApiKeySchema),
  async (c) => {
    const user = c.get('user');
    const teamId = c.req.param('teamId');
    const body = c.req.valid('json');

    try {
      const createParams: ICreateApiKeyParams = {
        userId: user?.id || '',
        teamId,
        name: body.name,
        permissions: body.permissions as TPermission[],
        scopedPermissions: body.scopedPermissions as TPermission[],
        usageLimit: body.usageLimit,
        expiresAt: body.expiresAt ? new Date(body.expiresAt) : undefined,
      };

      const { apiKey, key } = await createApiKey(createParams);

      // Log the action
      await createApiKeyAuditLog(c, 'api_key.create', 'api_key', apiKey.id, {
        api_key_name: body.name,
        permissions: body.permissions,
        scoped_permissions: body.scopedPermissions,
        team_id: teamId,
      });

      return c.json(
        {
          status: 'success',
          data: {
            api_key: {
              id: apiKey.id,
              name: apiKey.name,
              key_prefix: apiKey.keyPrefix,
              permissions: apiKey.permissions ? JSON.parse(apiKey.permissions) : [],
              scoped_permissions: apiKey.scopedPermissions
                ? JSON.parse(apiKey.scopedPermissions)
                : [],
              usage_limit: apiKey.usageLimit,
              expires_at: apiKey.expiresAt,
              created_at: apiKey.createdAt,
            },
            // Return the actual key only once during creation
            key,
          },
          timestamp: new Date().toISOString(),
        },
        201
      );
    } catch (error) {
      console.error('Error creating team API key:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to create team API key',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * GET /api/api-keys/:keyId
 * Get specific API key details
 */
app.get('/api-keys/:keyId', enforceApiRequestLimits(), async (c) => {
  const keyId = c.req.param('keyId');
  const user = c.get('user');

  try {
    const apiKeyWithStats = await getApiKeyById(keyId);

    if (!apiKeyWithStats) {
      return c.json(
        {
          status: 'error',
          error: 'API key not found',
          error_type: 'not_found',
          timestamp: new Date().toISOString(),
        },
        404
      );
    }

    // Check if user has access to this API key
    const hasAccess =
      apiKeyWithStats.userId === user?.id || // Owner
      user?.role === 'admin'; // Admin

    if (!hasAccess) {
      return c.json(
        {
          status: 'error',
          error: 'Access denied',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    return c.json({
      status: 'success',
      data: {
        api_key: apiKeyWithStats,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching API key:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to fetch API key',
        error_type: 'database',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

/**
 * PUT /api/api-keys/:keyId
 * Update API key
 */
app.put(
  '/api-keys/:keyId',
  enforceApiRequestLimits(),
  zValidator('json', updateApiKeySchema),
  async (c) => {
    const keyId = c.req.param('keyId');
    const user = c.get('user');
    const body = c.req.valid('json');

    try {
      // First check if the API key exists and user has access
      const existingApiKey = await getApiKeyById(keyId, false);

      if (!existingApiKey) {
        return c.json(
          {
            status: 'error',
            error: 'API key not found',
            error_type: 'not_found',
            timestamp: new Date().toISOString(),
          },
          404
        );
      }

      // Check access permissions
      const hasAccess =
        existingApiKey.userId === user?.id || // Owner
        user?.role === 'admin'; // Admin

      if (!hasAccess) {
        return c.json(
          {
            status: 'error',
            error: 'Access denied',
            error_type: 'authorization',
            timestamp: new Date().toISOString(),
          },
          403
        );
      }

      const updateParams: IUpdateApiKeyParams = {
        name: body.name,
        permissions: body.permissions as TPermission[],
        scopedPermissions: body.scopedPermissions as TPermission[],
        usageLimit: body.usageLimit,
        expiresAt: body.expiresAt ? new Date(body.expiresAt) : undefined,
        isActive: body.isActive,
      };

      const updatedApiKey = await updateApiKey(keyId, updateParams);

      if (!updatedApiKey) {
        return c.json(
          {
            status: 'error',
            error: 'Failed to update API key',
            error_type: 'database',
            timestamp: new Date().toISOString(),
          },
          500
        );
      }

      // Log the action
      await createApiKeyAuditLog(c, 'api_key.update', 'api_key', keyId, {
        old_values: {
          name: existingApiKey.name,
          permissions: existingApiKey.effectivePermissions,
        },
        new_values: body,
      });

      return c.json({
        status: 'success',
        data: {
          api_key: updatedApiKey,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error updating API key:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to update API key',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

/**
 * DELETE /api/api-keys/:keyId
 * Delete API key
 */
app.delete('/api-keys/:keyId', enforceApiRequestLimits(), async (c) => {
  const keyId = c.req.param('keyId');
  const user = c.get('user');

  try {
    // First check if the API key exists and user has access
    const existingApiKey = await getApiKeyById(keyId, false);

    if (!existingApiKey) {
      return c.json(
        {
          status: 'error',
          error: 'API key not found',
          error_type: 'not_found',
          timestamp: new Date().toISOString(),
        },
        404
      );
    }

    // Check access permissions
    const hasAccess =
      existingApiKey.userId === user?.id || // Owner
      user?.role === 'admin'; // Admin

    if (!hasAccess) {
      return c.json(
        {
          status: 'error',
          error: 'Access denied',
          error_type: 'authorization',
          timestamp: new Date().toISOString(),
        },
        403
      );
    }

    const deleted = await deleteApiKey(keyId);

    if (!deleted) {
      return c.json(
        {
          status: 'error',
          error: 'Failed to delete API key',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }

    // Log the action
    await createApiKeyAuditLog(c, 'api_key.delete', 'api_key', keyId, {
      api_key_name: existingApiKey.name,
    });

    return c.json({
      status: 'success',
      data: {
        message: 'API key deleted successfully',
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error deleting API key:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to delete API key',
        error_type: 'database',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// ============================================================================
// Enhanced API Key Management Endpoints
// ============================================================================

/**
 * GET /api/api-keys
 * List API keys with advanced filtering and pagination
 */
app.get(
  '/api-keys',
  enforceApiRequestLimits(),
  zValidator('query', listApiKeysSchema),
  async (c) => {
    const user = c.get('user');
    const query = c.req.valid('query');

    if (!user) {
      return c.json({ status: 'error', error: 'Authentication required' }, 401);
    }

    try {
      // Build filters from query parameters
      const filters: IApiKeyFilters = {};

      // Users can only see their own keys unless they're admin
      if (user.role !== 'admin') {
        filters.userId = user.id;
      } else if (query.userId) {
        filters.userId = query.userId;
      }

      if (query.teamId) filters.teamId = query.teamId;
      if (query.isActive !== undefined) filters.isActive = query.isActive;
      if (query.hasExpired !== undefined) filters.hasExpired = query.hasExpired;
      if (query.permissions) filters.permissions = query.permissions as TPermission[];
      if (query.usageThreshold) filters.usageThreshold = query.usageThreshold;
      if (query.createdAfter) filters.createdAfter = new Date(query.createdAfter);
      if (query.createdBefore) filters.createdBefore = new Date(query.createdBefore);
      if (query.lastUsedAfter) filters.lastUsedAfter = new Date(query.lastUsedAfter);
      if (query.lastUsedBefore) filters.lastUsedBefore = new Date(query.lastUsedBefore);

      const options: IApiKeyListOptions = {
        filters,
        sortBy: query.sortBy || 'createdAt',
        sortOrder: query.sortOrder || 'desc',
        limit: query.limit || 50,
        offset: query.offset || 0,
        includeStats: query.includeStats ?? true,
      };

      const result = await listApiKeys(options);

      // Create audit log
      await createApiKeyAuditLog(c, 'api_key.use', 'api_key', undefined, {
        action: 'list',
        filters,
        total: result.total,
        returned: result.apiKeys.length,
      });

      return c.json({
        status: 'success',
        data: {
          apiKeys: result.apiKeys,
          pagination: {
            total: result.total,
            limit: options.limit,
            offset: options.offset,
            hasMore: result.hasMore,
          },
        },
      });
    } catch (error) {
      console.error('List API keys error:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to list API keys',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        500
      );
    }
  }
);

/**
 * POST /api/api-keys/bulk
 * Perform bulk operations on API keys
 */
app.post(
  '/api-keys/bulk',
  enforceApiRequestLimits(),
  zValidator('json', bulkOperationSchema),
  async (c) => {
    const user = c.get('user');
    const body = c.req.valid('json');

    if (!user) {
      return c.json({ status: 'error', error: 'Authentication required' }, 401);
    }

    try {
      // Verify user has access to all specified API keys
      const db = await getDB();

      // Check access for each key
      for (const keyId of body.keyIds) {
        const apiKey = await db.query.apiKeys.findFirst({
          where: and(eq(schema.apiKeys.id, keyId), isNull(schema.apiKeys.deletedAt)),
          columns: { id: true, userId: true, teamId: true },
        });

        if (!apiKey) {
          return c.json(
            {
              status: 'error',
              error: 'API key not found',
              details: `API key ${keyId} not found`,
            },
            404
          );
        }

        // Check if user has access to this API key
        const hasAccess =
          user.role === 'admin' ||
          apiKey.userId === user.id ||
          (apiKey.teamId && (await hasTeamAccess(user.id, apiKey.teamId)));

        if (!hasAccess) {
          return c.json(
            {
              status: 'error',
              error: 'Insufficient permissions',
              details: `No access to API key ${keyId}`,
            },
            403
          );
        }
      }

      // Perform bulk operation
      const operation: IBulkApiKeyOperation = {
        keyIds: body.keyIds,
        operation: body.operation,
        params: body.params,
      };

      const result = await bulkUpdateApiKeys(operation);

      // Create audit log
      await createApiKeyAuditLog(c, 'api_key.update', 'api_key', undefined, {
        action: 'bulk_update',
        operation: body.operation,
        keyIds: body.keyIds,
        params: body.params,
        result,
      });

      return c.json({
        status: 'success',
        data: {
          operation: body.operation,
          results: result,
        },
      });
    } catch (error) {
      console.error('Bulk API key operation error:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to perform bulk operation',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        500
      );
    }
  }
);

/**
 * POST /api/api-keys/:keyId/rotate
 * Rotate API key (generate new key, invalidate old one)
 */
app.post('/api-keys/:keyId/rotate', enforceApiRequestLimits(), async (c) => {
  const keyId = c.req.param('keyId');
  const user = c.get('user');

  if (!user) {
    return c.json({ status: 'error', error: 'Authentication required' }, 401);
  }

  try {
    // First check if the API key exists and user has access
    const existingKey = await getApiKeyById(keyId);
    if (!existingKey) {
      return c.json({ status: 'error', error: 'API key not found' }, 404);
    }

    // Check if user has access to this API key
    const hasAccess =
      user.role === 'admin' ||
      existingKey.userId === user.id ||
      (existingKey.teamId && (await hasTeamAccess(user.id, existingKey.teamId)));

    if (!hasAccess) {
      return c.json({ status: 'error', error: 'Insufficient permissions' }, 403);
    }

    // Rotate the API key
    const result = await rotateApiKey(keyId);
    if (!result) {
      return c.json({ status: 'error', error: 'Failed to rotate API key' }, 500);
    }

    // Create audit log
    await createApiKeyAuditLog(c, 'api_key.update', 'api_key', keyId, {
      action: 'rotate',
      oldKeyId: result.oldKeyId,
      newKeyId: result.apiKey.id,
    });

    return c.json({
      status: 'success',
      data: {
        apiKey: {
          ...result.apiKey,
          keyHash: undefined, // Don't expose hash
        },
        key: result.key, // Only returned once
        message: 'API key rotated successfully. Please update your applications with the new key.',
      },
    });
  } catch (error) {
    console.error('Rotate API key error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to rotate API key',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

/**
 * GET /api/api-keys/:keyId/analytics
 * Get detailed usage analytics for an API key
 */
app.get('/api-keys/:keyId/analytics', enforceApiRequestLimits(), async (c) => {
  const keyId = c.req.param('keyId');
  const user = c.get('user');
  const days = Number(c.req.query('days')) || 30;

  if (!user) {
    return c.json({ status: 'error', error: 'Authentication required' }, 401);
  }

  try {
    // First check if the API key exists and user has access
    const existingKey = await getApiKeyById(keyId);
    if (!existingKey) {
      return c.json({ status: 'error', error: 'API key not found' }, 404);
    }

    // Check if user has access to this API key
    const hasAccess =
      user.role === 'admin' ||
      existingKey.userId === user.id ||
      (existingKey.teamId && (await hasTeamAccess(user.id, existingKey.teamId)));

    if (!hasAccess) {
      return c.json({ status: 'error', error: 'Insufficient permissions' }, 403);
    }

    // Get analytics
    const analytics = await getApiKeyAnalytics(keyId, days);
    if (!analytics) {
      return c.json({ status: 'error', error: 'Failed to get analytics' }, 500);
    }

    // Create audit log
    await createApiKeyAuditLog(c, 'api_key.use', 'api_key', keyId, {
      action: 'analytics',
      days,
      totalRequests: analytics.totalRequests,
    });

    return c.json({
      status: 'success',
      data: analytics,
    });
  } catch (error) {
    console.error('Get API key analytics error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to get API key analytics',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

/**
 * GET /api/api-keys/expiring
 * Get API keys that are expiring soon
 */
app.get('/api-keys/expiring', enforceApiRequestLimits(), async (c) => {
  const user = c.get('user');
  const days = Number(c.req.query('days')) || 7;
  const teamId = c.req.query('teamId');

  if (!user) {
    return c.json({ status: 'error', error: 'Authentication required' }, 401);
  }

  try {
    // Get expiring API keys
    const expiringKeys = await getExpiringApiKeys(days, teamId);

    // Filter keys based on user access
    const accessibleKeys = [];
    for (const key of expiringKeys) {
      const hasAccess =
        user.role === 'admin' ||
        key.userId === user.id ||
        (key.teamId && (await hasTeamAccess(user.id, key.teamId)));

      if (hasAccess) {
        accessibleKeys.push(key);
      }
    }

    // Create audit log
    await createApiKeyAuditLog(c, 'api_key.use', 'api_key', undefined, {
      action: 'expiring_list',
      days,
      teamId,
      found: accessibleKeys.length,
    });

    return c.json({
      status: 'success',
      data: {
        expiringKeys: accessibleKeys,
        days,
        count: accessibleKeys.length,
      },
    });
  } catch (error) {
    console.error('Get expiring API keys error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to get expiring API keys',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

/**
 * POST /api/api-keys/validate-permissions
 * Validate API key permissions against RBAC system
 */
app.post(
  '/api-keys/validate-permissions',
  zValidator('json', validatePermissionsSchema),
  async (c) => {
    const user = c.get('user');
    const body = c.req.valid('json');

    if (!user) {
      return c.json({ status: 'error', error: 'Authentication required' }, 401);
    }

    try {
      // Validate permissions
      const validation = await validateApiKeyPermissions(
        body.permissions as TPermission[],
        body.teamId
      );

      // Create audit log
      await createApiKeyAuditLog(c, 'api_key.use', 'api_key', undefined, {
        action: 'validate_permissions',
        permissions: body.permissions,
        teamId: body.teamId,
        validation,
      });

      return c.json({
        status: 'success',
        data: validation,
      });
    } catch (error) {
      console.error('Validate API key permissions error:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to validate permissions',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        500
      );
    }
  }
);

/**
 * GET /api/api-keys/:keyId/comprehensive-analytics
 * Get comprehensive analytics for an API key
 */
app.get(
  '/api-keys/:keyId/comprehensive-analytics',
  zValidator('query', comprehensiveAnalyticsSchema),
  async (c) => {
    const keyId = c.req.param('keyId');
    const user = c.get('user');
    const query = c.req.valid('query');

    if (!user) {
      return c.json({ status: 'error', error: 'Authentication required' }, 401);
    }

    try {
      // First check if the API key exists and user has access
      const existingKey = await getApiKeyById(keyId);
      if (!existingKey) {
        return c.json({ status: 'error', error: 'API key not found' }, 404);
      }

      // Check if user has access to this API key
      const hasAccess =
        user.role === 'admin' ||
        existingKey.userId === user.id ||
        (existingKey.teamId && (await hasTeamAccess(user.id, existingKey.teamId)));

      if (!hasAccess) {
        return c.json({ status: 'error', error: 'Insufficient permissions' }, 403);
      }

      // Get comprehensive analytics
      const startDate = new Date(query.startDate);
      const endDate = new Date(query.endDate);
      const analytics = await apiKeyAnalyticsService.getComprehensiveAnalytics(
        keyId,
        startDate,
        endDate
      );

      if (!analytics) {
        return c.json({ status: 'error', error: 'Failed to get analytics' }, 500);
      }

      // Create audit log
      await createApiKeyAuditLog(c, 'api_key.use', 'api_key', keyId, {
        action: 'comprehensive_analytics',
        startDate: query.startDate,
        endDate: query.endDate,
        totalRequests: analytics.overview.totalRequests,
      });

      return c.json({
        status: 'success',
        data: analytics,
      });
    } catch (error) {
      console.error('Get comprehensive API key analytics error:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to get comprehensive analytics',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        500
      );
    }
  }
);

/**
 * GET /api/teams/:teamId/analytics
 * Get team analytics summary
 */
app.get(
  '/teams/:teamId/analytics',
  teamContextMiddleware(),
  requireAnyTeamPermission(['read.analytics', 'manage.analytics']),
  zValidator('query', teamAnalyticsSchema),
  async (c) => {
    const teamId = c.req.param('teamId');
    const query = c.req.valid('query');

    try {
      // Get team analytics
      const startDate = new Date(query.startDate);
      const endDate = new Date(query.endDate);
      const analytics = await apiKeyAnalyticsService.getTeamAnalyticsSummary(
        teamId,
        startDate,
        endDate
      );

      if (!analytics) {
        return c.json({ status: 'error', error: 'Failed to get team analytics' }, 500);
      }

      // Create audit log
      await createApiKeyAuditLog(c, 'api_key.use', 'analytics', undefined, {
        action: 'team_analytics',
        teamId,
        startDate: query.startDate,
        endDate: query.endDate,
        totalRequests: analytics.overview.totalRequests,
      });

      return c.json({
        status: 'success',
        data: analytics,
      });
    } catch (error) {
      console.error('Get team analytics error:', error);
      return c.json(
        {
          status: 'error',
          error: 'Failed to get team analytics',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        500
      );
    }
  }
);

// ============================================================================
// Enhanced Expiration and Rotation Endpoints
// ============================================================================

/**
 * GET /api/api-keys/expiration-status
 * Get comprehensive expiration status for API keys
 */
app.get('/api-keys/expiration-status', enforceApiRequestLimits(), async (c) => {
  const user = c.get('user');
  const daysAhead = Number(c.req.query('daysAhead')) || 30;
  const includeExpired = c.req.query('includeExpired') === 'true';
  const teamId = c.req.query('teamId');

  if (!user) {
    return c.json({ status: 'error', error: 'Authentication required' }, 401);
  }

  try {
    // Get expiration status
    const expirationStatus = await getApiKeyExpirationStatus({
      userId: user.role === 'admin' ? undefined : user.id,
      teamId,
      daysAhead,
      includeExpired,
    });

    // Filter based on user access
    const accessibleStatus = [];
    for (const status of expirationStatus) {
      const hasAccess =
        user.role === 'admin' ||
        status.userId === user.id ||
        (status.teamId && (await hasTeamAccess(user.id, status.teamId)));

      if (hasAccess) {
        accessibleStatus.push(status);
      }
    }

    await createApiKeyAuditLog(c, 'api_key.use', 'api_key', undefined, {
      action: 'expiration_status',
      daysAhead,
      includeExpired,
      teamId,
      found: accessibleStatus.length,
    });

    return c.json({
      status: 'success',
      data: {
        expirationStatus: accessibleStatus,
        summary: {
          total: accessibleStatus.length,
          expired: accessibleStatus.filter((s) => s.isExpired).length,
          expiringSoon: accessibleStatus.filter((s) => !s.isExpired && s.daysUntilExpiration <= 7)
            .length,
          inGracePeriod: accessibleStatus.filter((s) => s.isInGracePeriod).length,
        },
      },
    });
  } catch (error) {
    console.error('Get expiration status error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to get expiration status',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

/**
 * GET /api/api-keys/bulk-expiration-summary
 * Get bulk expiration summary for dashboard
 */
app.get('/api-keys/bulk-expiration-summary', async (c) => {
  const user = c.get('user');
  const teamId = c.req.query('teamId');

  if (!user) {
    return c.json({ status: 'error', error: 'Authentication required' }, 401);
  }

  try {
    // Check team access if teamId is provided
    if (teamId && !(await hasTeamAccess(user.id, teamId))) {
      return c.json({ status: 'error', error: 'Insufficient team permissions' }, 403);
    }

    const summary = await getBulkExpirationSummary(teamId);

    await createApiKeyAuditLog(c, 'api_key.use', 'api_key', undefined, {
      action: 'bulk_expiration_summary',
      teamId,
      totalKeys: summary.totalKeys,
      expiredKeys: summary.expiredKeys,
    });

    return c.json({
      status: 'success',
      data: summary,
    });
  } catch (error) {
    console.error('Get bulk expiration summary error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to get bulk expiration summary',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

/**
 * POST /api/api-keys/extend-expiration
 * Extend expiration date for multiple API keys
 */
app.post('/api-keys/extend-expiration', enforceApiRequestLimits(), async (c) => {
  const user = c.get('user');
  const body = await c.req.json();
  const { keyIds, extensionDays, newExpirationDate } = body;

  if (!user) {
    return c.json({ status: 'error', error: 'Authentication required' }, 401);
  }

  if (!keyIds || !Array.isArray(keyIds) || keyIds.length === 0) {
    return c.json({ status: 'error', error: 'keyIds array is required' }, 400);
  }

  if (!extensionDays && !newExpirationDate) {
    return c.json(
      { status: 'error', error: 'Either extensionDays or newExpirationDate is required' },
      400
    );
  }

  try {
    // Verify user has access to all keys
    for (const keyId of keyIds) {
      const existingKey = await getApiKeyById(keyId);
      if (!existingKey) {
        return c.json({ status: 'error', error: `API key ${keyId} not found` }, 404);
      }

      const hasAccess =
        user.role === 'admin' ||
        existingKey.userId === user.id ||
        (existingKey.teamId && (await hasTeamAccess(user.id, existingKey.teamId)));

      if (!hasAccess) {
        return c.json({ status: 'error', error: `Insufficient permissions for key ${keyId}` }, 403);
      }
    }

    // Extend expiration
    const updatedKeys = await extendApiKeyExpiration(
      keyIds,
      extensionDays,
      newExpirationDate ? new Date(newExpirationDate) : undefined
    );

    await createApiKeyAuditLog(c, 'api_key.update', 'api_key', undefined, {
      action: 'extend_expiration',
      keyIds,
      extensionDays,
      newExpirationDate,
      updatedCount: updatedKeys.length,
    });

    return c.json({
      status: 'success',
      data: {
        updatedKeys: updatedKeys.map((key) => ({
          ...key,
          keyHash: undefined, // Don't expose hash
        })),
        message: `Extended expiration for ${updatedKeys.length} API keys`,
      },
    });
  } catch (error) {
    console.error('Extend expiration error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to extend expiration',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

/**
 * POST /api/api-keys/:keyId/rotate-enhanced
 * Enhanced API key rotation with overlap support
 */
app.post('/api-keys/:keyId/rotate-enhanced', async (c) => {
  const keyId = c.req.param('keyId');
  const user = c.get('user');
  const body = await c.req.json();
  const {
    overlapPeriodHours = 24,
    rotationType = 'manual',
    reason = 'Manual rotation',
    sendNotification = true,
    newExpirationDate,
  } = body;

  if (!user) {
    return c.json({ status: 'error', error: 'Authentication required' }, 401);
  }

  try {
    // Check if the API key exists and user has access
    const existingKey = await getApiKeyById(keyId);
    if (!existingKey) {
      return c.json({ status: 'error', error: 'API key not found' }, 404);
    }

    const hasAccess =
      user.role === 'admin' ||
      existingKey.userId === user.id ||
      (existingKey.teamId && (await hasTeamAccess(user.id, existingKey.teamId)));

    if (!hasAccess) {
      return c.json({ status: 'error', error: 'Insufficient permissions' }, 403);
    }

    // Perform enhanced rotation
    const result = await rotateApiKeyEnhanced(keyId, {
      overlapPeriodHours,
      rotationType,
      reason,
      rotatedBy: user.id,
      ipAddress: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For'),
      userAgent: c.req.header('User-Agent'),
      sendNotification,
      newExpirationDate: newExpirationDate ? new Date(newExpirationDate) : undefined,
    });

    if (!result.success) {
      return c.json({ status: 'error', error: result.error || 'Failed to rotate API key' }, 500);
    }

    await createApiKeyAuditLog(c, 'api_key.update', 'api_key', keyId, {
      action: 'rotate_enhanced',
      oldKeyId: result.oldKeyId,
      newKeyId: result.apiKey?.id,
      overlapPeriodHours,
      rotationType,
      reason,
    });

    return c.json({
      status: 'success',
      data: {
        apiKey: result.apiKey
          ? {
              ...result.apiKey,
              keyHash: undefined, // Don't expose hash
            }
          : undefined,
        key: result.key, // Only returned once
        overlapEndsAt: result.overlapEndsAt,
        message: `API key rotated successfully with ${overlapPeriodHours}h overlap period. Please update your applications with the new key.`,
      },
    });
  } catch (error) {
    console.error('Enhanced rotate API key error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to rotate API key',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

/**
 * POST /api/api-keys/send-expiration-notifications
 * Manually trigger expiration notifications
 */
app.post('/api-keys/send-expiration-notifications', async (c) => {
  const user = c.get('user');
  const body = await c.req.json();
  const { warningDays = [7, 1] } = body;

  if (!user) {
    return c.json({ status: 'error', error: 'Authentication required' }, 401);
  }

  // Only admins can manually trigger notifications
  if (user.role !== 'admin') {
    return c.json({ status: 'error', error: 'Admin privileges required' }, 403);
  }

  try {
    const result = await sendExpirationNotifications(warningDays);

    await createApiKeyAuditLog(c, 'api_key.use', 'notification', undefined, {
      action: 'send_expiration_notifications',
      warningDays,
      totalNotifications: result.totalNotifications,
      successfulNotifications: result.successfulNotifications,
      failedNotifications: result.failedNotifications,
    });

    return c.json({
      status: 'success',
      data: {
        ...result,
        message: `Sent ${result.successfulNotifications} notifications successfully`,
      },
    });
  } catch (error) {
    console.error('Send expiration notifications error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to send expiration notifications',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

/**
 * GET /api/api-keys/notification-preferences
 * Get user's notification preferences
 */
app.get('/api-keys/notification-preferences', async (c) => {
  const user = c.get('user');
  const teamId = c.req.query('teamId');

  if (!user) {
    return c.json({ status: 'error', error: 'Authentication required' }, 401);
  }

  try {
    // Check team access if teamId is provided
    if (teamId && !(await hasTeamAccess(user.id, teamId))) {
      return c.json({ status: 'error', error: 'Insufficient team permissions' }, 403);
    }

    const preferences = await notificationService.getNotificationPreferences(user.id, teamId);

    return c.json({
      status: 'success',
      data: preferences,
    });
  } catch (error) {
    console.error('Get notification preferences error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to get notification preferences',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

/**
 * PUT /api/api-keys/notification-preferences
 * Update user's notification preferences
 */
app.put('/api-keys/notification-preferences', async (c) => {
  const user = c.get('user');
  const body = await c.req.json();
  const { teamId, ...preferences } = body;

  if (!user) {
    return c.json({ status: 'error', error: 'Authentication required' }, 401);
  }

  try {
    // Check team access if teamId is provided
    if (teamId && !(await hasTeamAccess(user.id, teamId))) {
      return c.json({ status: 'error', error: 'Insufficient team permissions' }, 403);
    }

    const updatedPreferences = await notificationService.updateNotificationPreferences(
      user.id,
      preferences,
      teamId
    );

    await createApiKeyAuditLog(c, 'api_key.update', 'notification_preferences', undefined, {
      action: 'update_notification_preferences',
      teamId,
      preferences: Object.keys(preferences),
    });

    return c.json({
      status: 'success',
      data: updatedPreferences,
      message: 'Notification preferences updated successfully',
    });
  } catch (error) {
    console.error('Update notification preferences error:', error);
    return c.json(
      {
        status: 'error',
        error: 'Failed to update notification preferences',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      500
    );
  }
});

export default app;
