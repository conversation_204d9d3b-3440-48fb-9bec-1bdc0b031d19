/**
 * Document Management Routes
 *
 * API routes for CRUD operations on document database with comprehensive validation.
 */

import { zValidator } from '@hono/zod-validator';
// TEMPORARILY DISABLED TO AVOID GLOBAL SCOPE ASYNC OPERATIONS
// import { enforcePlanLimits } from '@middleware/billing-middleware';
// Lazy imports to avoid global scope async operations
// import { documentService } from '@services';
import {
  BulkDeleteSchema,
  DocumentUpdateSchema,
  type IBulkDeleteResponse,
  type IDocumentListResponse,
  type IDocumentResponse,
  type IErrorResponse,
  type IHonoEnv,
  PaginationSchema,
} from '@types';
import { Hono } from 'hono';
import { z } from 'zod';

const documentApp = new Hono<IHonoEnv>();

// Document ID parameter validation
const DocumentIdSchema = z.object({
  id: z.string().min(1, 'Document ID cannot be empty'),
});

// GET /api/documents - List documents with pagination and filtering
documentApp.get('/', zValidator('query', PaginationSchema), async (c) => {
  try {
    const { documentService } = await import('@services/document-service');
    const params = c.req.valid('query');
    const result = await documentService.listDocuments(params);
    return c.json<IDocumentListResponse>(result);
  } catch (error) {
    console.error('List documents endpoint error:', error);
    return c.json<IErrorResponse>(
      {
        status: 'error',
        error: error instanceof Error ? error.message : 'Failed to list documents',
        error_type: 'database',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// GET /api/documents/health - Health check for document management (must be before /:id route)
documentApp.get('/health', async (c) => {
  try {
    const { documentService } = await import('@services/document-service');
    // Test basic document operations
    await documentService.listDocuments({
      page: 1,
      limit: 1,
    });

    return c.json({
      status: 'healthy',
      service: 'document-management',
      checks: {
        document_database: 'ok',
        list_operation: 'ok',
        pagination: 'ok',
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Document management health check failed:', error);
    return c.json(
      {
        status: 'unhealthy',
        service: 'document-management',
        checks: {
          document_database: 'error',
          list_operation: 'error',
          pagination: 'error',
        },
        error: error instanceof Error ? error.message : 'Health check failed',
        timestamp: new Date().toISOString(),
      },
      503
    );
  }
});

// GET /api/documents/:id - Get a specific document by ID
documentApp.get('/:id', zValidator('param', DocumentIdSchema), async (c) => {
  try {
    const { documentService } = await import('@services/document-service');
    const { id } = c.req.valid('param');
    const result = await documentService.getDocument(id);

    if (result.status === 'error') {
      return c.json<IDocumentResponse>(result, 404);
    }

    return c.json<IDocumentResponse>(result);
  } catch (error) {
    console.error('Get document endpoint error:', error);
    return c.json<IErrorResponse>(
      {
        status: 'error',
        error: error instanceof Error ? error.message : 'Failed to get document',
        error_type: 'database',
        timestamp: new Date().toISOString(),
      },
      500
    );
  }
});

// PUT /api/documents/:id - Update a document's metadata
documentApp.put(
  '/:id',
  // TEMPORARILY DISABLED: enforcePlanLimits('documents'),
  zValidator('param', DocumentIdSchema),
  zValidator('json', DocumentUpdateSchema),
  async (c) => {
    try {
      const { documentService } = await import('@services/document-service');
      const { id } = c.req.valid('param');
      const updates = c.req.valid('json');
      const result = await documentService.updateDocument(id, updates);

      if (result.status === 'error') {
        return c.json<IDocumentResponse>(result, 404);
      }

      return c.json<IDocumentResponse>(result);
    } catch (error) {
      console.error('Update document endpoint error:', error);
      return c.json<IErrorResponse>(
        {
          status: 'error',
          error: error instanceof Error ? error.message : 'Failed to update document',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// DELETE /api/documents/:id - Delete a specific document
documentApp.delete(
  '/:id',
  // TEMPORARILY DISABLED: enforcePlanLimits('documents'),
  zValidator('param', DocumentIdSchema),
  async (c) => {
    try {
      const { documentService } = await import('@services/document-service');
      const { id } = c.req.valid('param');
      const result = await documentService.deleteDocument(id);

      if (result.status === 'error') {
        return c.json<IDocumentResponse>(result, 404);
      }

      return c.json<IDocumentResponse>(result);
    } catch (error) {
      console.error('Delete document endpoint error:', error);
      return c.json<IErrorResponse>(
        {
          status: 'error',
          error: error instanceof Error ? error.message : 'Failed to delete document',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

// POST /api/documents/bulk-delete - Bulk delete documents by filters
documentApp.post(
  '/bulk-delete',
  // TEMPORARILY DISABLED: enforcePlanLimits('documents'),
  zValidator('json', BulkDeleteSchema),
  async (c) => {
    try {
      const { documentService } = await import('@services/document-service');
      const filters = c.req.valid('json');
      const result = await documentService.bulkDelete(filters);
      return c.json<IBulkDeleteResponse>(result);
    } catch (error) {
      console.error('Bulk delete endpoint error:', error);
      return c.json<IErrorResponse>(
        {
          status: 'error',
          error: error instanceof Error ? error.message : 'Failed to bulk delete documents',
          error_type: 'database',
          timestamp: new Date().toISOString(),
        },
        500
      );
    }
  }
);

export { documentApp };
