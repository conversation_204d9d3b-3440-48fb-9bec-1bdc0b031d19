{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "ezcontext-n",
  "main": "src/index.ts",
  "compatibility_date": "2025-07-05",
  "compatibility_flags": ["nodejs_compat"],
  "ai": {
    "binding": "ai"
  },
  "observability": {
    "enabled": true,
    "head_sampling_rate": 1
  },
  "vectorize": [
    {
      "binding": "vdb",
      "index_name": "ezcontext"
    }
  ],
  "r2_buckets": [
    {
      "binding": "r2",
      "bucket_name": "ezcontext",
      "preview_bucket_name": "ezcontext-preview"
    }
  ],
  "browser": {
    "binding": "browser"
  },
  "kv_namespaces": [
    {
      "binding": "kv",
      "id": "91feeea1302e48d680cffd8b7867c206",
      "preview_id": "41b395f6bdb7443db54e331ff82bedfc"
    }
  ],
  "vars": {
    "EMBEDDING_PROVIDER": "cloudflare",
    "EMBEDDING_MODEL": "@cf/baai/bge-large-en-v1.5",
    "EMBEDDING_DIMENSIONS": "1024",
    "VECTOR_DIMENSIONS": "1024",
    "OPENAI_BASE_URL": "https://openrouter.ai/api/v1",
    "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
    "OPENROUTER_API_KEY": "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",
    "VOYAGEAI_API_KEY": "pa-7RuYUKNI4RszVRexO94TQJAcEMruVkYM8h6M7TSic58",
    "MCP_MAX_CONNECTIONS": "100",
    "MCP_SESSION_TIMEOUT": "30",
    "MCP_CACHE_TTL": "3600",
    "SEARCH_RATE_LIMIT": "60",
    "DEFAULT_SEARCH_LIMIT": "5",
    "MAX_SEARCH_LIMIT": "20",
    "ALLOWED_FILE_EXTENSIONS": ".txt,.md,.json,.pdf,.doc,.docx,.py,.js,.ts,.jsx,.tsx,.java,.cpp,.c,.h,.cs,.php,.rb,.go,.rs,.swift,.kt,.scala,.r,.sql,.sh,.bat,.ps1,.yaml,.yml,.xml,.html,.css,.scss,.less",
    "DATABASE_URL": "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",
    "BETTER_AUTH_SECRET": "CZvhAaw81ZwYDeq8cEhRUkFnnan2jvZi",
    // "BETTER_AUTH_URL": "http://localhost:8787"

    // Billing configuration (development values)
    "PADDLE_API_KEY": "test_paddle_key",
    "PADDLE_ENVIRONMENT": "sandbox",
    "PADDLE_WEBHOOK_SECRET": "test_paddle_webhook_secret",
    "PAYPAL_CLIENT_ID": "test_paypal_client_id",
    "PAYPAL_CLIENT_SECRET": "test_paypal_client_secret",
    "PAYPAL_ENVIRONMENT": "sandbox",
    "PAYPAL_WEBHOOK_ID": "test_paypal_webhook_id",
    "BASE_URL": "http://localhost:8787",

    // Email configuration (Resend)
    "RESEND_API_KEY": "re_HzY9xmiU_HpyFisH7cLkXCDbZeTn3Nn1z",
    "FROM_EMAIL": "<EMAIL>",
    "FROM_NAME": "EZContext",
    "SUPPORT_EMAIL": "<EMAIL>"
  }
}
